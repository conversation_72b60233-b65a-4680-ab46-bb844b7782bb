{"version": 3, "file": "KHR_materials_unlit-BzESk8qd.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/KHR_materials_unlit.js"], "sourcesContent": ["import { Color3 } from \"@babylonjs/core/Maths/math.color.js\";\nimport { PBRMaterial } from \"@babylonjs/core/Materials/PBR/pbrMaterial.js\";\nimport { GLTFLoader } from \"../glTFLoader.js\";\nimport { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nconst NAME = \"KHR_materials_unlit\";\n/**\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_materials_unlit/README.md)\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class KHR_materials_unlit {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        /**\n         * Defines a number that determines the order the extensions are applied.\n         */\n        this.order = 210;\n        this._loader = loader;\n        this.enabled = this._loader.isExtensionUsed(NAME);\n    }\n    /** @internal */\n    dispose() {\n        this._loader = null;\n    }\n    /**\n     * @internal\n     */\n    loadMaterialPropertiesAsync(context, material, babylonMaterial) {\n        return GLTFLoader.LoadExtensionAsync(context, material, this.name, () => {\n            return this._loadUnlitPropertiesAsync(context, material, babylonMaterial);\n        });\n    }\n    _loadUnlitPropertiesAsync(context, material, babylonMaterial) {\n        if (!(babylonMaterial instanceof PBRMaterial)) {\n            throw new Error(`${context}: Material type not supported`);\n        }\n        const promises = new Array();\n        babylonMaterial.unlit = true;\n        const properties = material.pbrMetallicRoughness;\n        if (properties) {\n            if (properties.baseColorFactor) {\n                babylonMaterial.albedoColor = Color3.FromArray(properties.baseColorFactor);\n                babylonMaterial.alpha = properties.baseColorFactor[3];\n            }\n            else {\n                babylonMaterial.albedoColor = Color3.White();\n            }\n            if (properties.baseColorTexture) {\n                promises.push(this._loader.loadTextureInfoAsync(`${context}/baseColorTexture`, properties.baseColorTexture, (texture) => {\n                    texture.name = `${babylonMaterial.name} (Base Color)`;\n                    babylonMaterial.albedoTexture = texture;\n                }));\n            }\n        }\n        if (material.doubleSided) {\n            babylonMaterial.backFaceCulling = false;\n            babylonMaterial.twoSidedLighting = true;\n        }\n        this._loader.loadMaterialAlphaProperties(context, material, babylonMaterial);\n        return Promise.all(promises).then(() => { });\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new KHR_materials_unlit(loader));\n//# sourceMappingURL=KHR_materials_unlit.js.map"], "names": ["NAME", "KHR_materials_unlit", "loader", "context", "material", "babylonMaterial", "GLTFLoader", "PBRMaterial", "promises", "properties", "Color3", "texture", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "uTAIA,MAAMA,EAAO,sBAKN,MAAMC,CAAoB,CAI7B,YAAYC,EAAQ,CAIhB,KAAK,KAAOF,EAIZ,KAAK,MAAQ,IACb,KAAK,QAAUE,EACf,KAAK,QAAU,KAAK,QAAQ,gBAAgBF,CAAI,CACnD,CAED,SAAU,CACN,KAAK,QAAU,IAClB,CAID,4BAA4BG,EAASC,EAAUC,EAAiB,CAC5D,OAAOC,EAAW,mBAAmBH,EAASC,EAAU,KAAK,KAAM,IACxD,KAAK,0BAA0BD,EAASC,EAAUC,CAAe,CAC3E,CACJ,CACD,0BAA0BF,EAASC,EAAUC,EAAiB,CAC1D,GAAI,EAAEA,aAA2BE,GAC7B,MAAM,IAAI,MAAM,GAAGJ,CAAO,+BAA+B,EAE7D,MAAMK,EAAW,IAAI,MACrBH,EAAgB,MAAQ,GACxB,MAAMI,EAAaL,EAAS,qBAC5B,OAAIK,IACIA,EAAW,iBACXJ,EAAgB,YAAcK,EAAO,UAAUD,EAAW,eAAe,EACzEJ,EAAgB,MAAQI,EAAW,gBAAgB,CAAC,GAGpDJ,EAAgB,YAAcK,EAAO,QAErCD,EAAW,kBACXD,EAAS,KAAK,KAAK,QAAQ,qBAAqB,GAAGL,CAAO,oBAAqBM,EAAW,iBAAmBE,GAAY,CACrHA,EAAQ,KAAO,GAAGN,EAAgB,IAAI,gBACtCA,EAAgB,cAAgBM,CACnC,CAAA,CAAC,GAGNP,EAAS,cACTC,EAAgB,gBAAkB,GAClCA,EAAgB,iBAAmB,IAEvC,KAAK,QAAQ,4BAA4BF,EAASC,EAAUC,CAAe,EACpE,QAAQ,IAAIG,CAAQ,EAAE,KAAK,IAAM,CAAA,CAAG,CAC9C,CACL,CACAI,EAAwBZ,CAAI,EAC5Ba,EAAsBb,EAAM,GAAOE,GAAW,IAAID,EAAoBC,CAAM,CAAC", "x_google_ignoreList": [0]}