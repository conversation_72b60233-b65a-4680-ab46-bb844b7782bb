const{SvelteComponent:p,append:a,attr:t,detach:d,init:h,insert:u,noop:l,safe_not_equal:w,svg_element:s}=window.__gradio__svelte__internal;function _(i){let e,n,o;return{c(){e=s("svg"),n=s("polyline"),o=s("path"),t(n,"points","1 4 1 10 7 10"),t(o,"d","M3.51 15a9 9 0 1 0 2.13-9.36L1 10"),t(e,"aria-label","undo"),t(e,"xmlns","http://www.w3.org/2000/svg"),t(e,"width","100%"),t(e,"height","100%"),t(e,"viewBox","0 0 24 24"),t(e,"fill","none"),t(e,"stroke","currentColor"),t(e,"stroke-width","1.5"),t(e,"stroke-linecap","round"),t(e,"stroke-linejoin","round"),t(e,"class","feather feather-rotate-ccw")},m(r,c){u(r,e,c),a(e,n),a(e,o)},p:l,i:l,o:l,d(r){r&&d(e)}}}class g extends p{constructor(e){super(),h(this,e,null,_,w,{})}}export{g as U};
//# sourceMappingURL=Undo-DCjBnnSO.js.map
