import{w as ne}from"./index-Ccc2t4AG.js";const{SvelteComponent:Se,append:X,attr:C,detach:Te,init:qe,insert:Ne,noop:Y,safe_not_equal:je,svg_element:P}=window.__gradio__svelte__internal;function Ee(l){let e,t,i,_;return{c(){e=P("svg"),t=P("circle"),i=P("circle"),_=P("circle"),C(t,"cx","2.5"),C(t,"cy","8"),C(t,"r","1.5"),C(t,"fill","currentColor"),C(i,"cx","8"),C(i,"cy","8"),C(i,"r","1.5"),C(i,"fill","currentColor"),C(_,"cx","13.5"),C(_,"cy","8"),C(_,"r","1.5"),C(_,"fill","currentColor"),C(e,"width","16"),C(e,"height","16"),C(e,"viewBox","0 0 16 16"),C(e,"fill","none"),C(e,"xmlns","http://www.w3.org/2000/svg")},m(r,a){Ne(r,e,a),X(e,t),X(e,i),X(e,_)},p:Y,i:Y,o:Y,d(r){r&&Te(e)}}}class Oe extends Se{constructor(e){super(),qe(this,e,null,Ee,je,{})}}const{SvelteComponent:Re,append:z,attr:m,binding_callbacks:Z,check_outros:Ae,component_subscribe:se,create_component:De,create_slot:Me,destroy_component:Fe,destroy_each:y,detach:E,element:N,empty:$,ensure_array_like:A,flush:M,get_all_dirty_from_scope:Ge,get_slot_changes:He,group_outros:Je,init:Ke,insert:O,listen:H,mount_component:Le,run_all:Pe,safe_not_equal:Qe,set_data:x,set_store_value:F,set_style:oe,space:R,stop_propagation:Ue,text:ee,toggle_class:S,transition_in:G,transition_out:Q,update_slot_base:Ve}=window.__gradio__svelte__internal,{setContext:We,createEventDispatcher:Xe,tick:Ye,onMount:Ze}=window.__gradio__svelte__internal;function _e(l,e,t){const i=l.slice();return i[33]=e[t],i}function ae(l,e,t){const i=l.slice();return i[33]=e[t],i[37]=t,i}function fe(l,e,t){const i=l.slice();return i[33]=e[t],i[38]=e,i[37]=t,i}function ce(l){let e,t,i,_,r,a,f,d,u,c,v,B,j,b=A(l[3]),h=[];for(let s=0;s<b.length;s+=1)h[s]=ue(fe(l,b,s));let T=A(l[7]),g=[];for(let s=0;s<T.length;s+=1)g[s]=be(ae(l,T,s));d=new Oe({});let I=A(l[8]),w=[];for(let s=0;s<I.length;s+=1)w[s]=me(_e(l,I,s));return{c(){e=N("div"),t=N("div");for(let s=0;s<h.length;s+=1)h[s].c();i=R(),_=N("div");for(let s=0;s<g.length;s+=1)g[s].c();r=R(),a=N("span"),f=N("button"),De(d.$$.fragment),u=R(),c=N("div");for(let s=0;s<w.length;s+=1)w[s].c();m(t,"class","tab-container visually-hidden svelte-1tcem6n"),m(t,"aria-hidden","true"),m(_,"class","tab-container svelte-1tcem6n"),m(_,"role","tablist"),m(f,"class","svelte-1tcem6n"),S(f,"overflow-item-selected",l[12]),m(c,"class","overflow-dropdown svelte-1tcem6n"),S(c,"hide",!l[9]),m(a,"class","overflow-menu svelte-1tcem6n"),S(a,"hide",!l[11]||!l[8].some(ve)),m(e,"class","tab-wrapper svelte-1tcem6n")},m(s,k){O(s,e,k),z(e,t);for(let n=0;n<h.length;n+=1)h[n]&&h[n].m(t,null);z(e,i),z(e,_);for(let n=0;n<g.length;n+=1)g[n]&&g[n].m(_,null);l[28](_),z(e,r),z(e,a),z(a,f),Le(d,f,null),z(a,u),z(a,c);for(let n=0;n<w.length;n+=1)w[n]&&w[n].m(c,null);l[31](a),v=!0,B||(j=H(f,"click",Ue(l[29])),B=!0)},p(s,k){if(k[0]&40){b=A(s[3]);let n;for(n=0;n<b.length;n+=1){const q=fe(s,b,n);h[n]?h[n].p(q,k):(h[n]=ue(q),h[n].c(),h[n].m(t,null))}for(;n<h.length;n+=1)h[n].d(1);h.length=b.length}if(k[0]&393408){T=A(s[7]);let n;for(n=0;n<T.length;n+=1){const q=ae(s,T,n);g[n]?g[n].p(q,k):(g[n]=be(q),g[n].c(),g[n].m(_,null))}for(;n<g.length;n+=1)g[n].d(1);g.length=T.length}if((!v||k[0]&4096)&&S(f,"overflow-item-selected",s[12]),k[0]&262464){I=A(s[8]);let n;for(n=0;n<I.length;n+=1){const q=_e(s,I,n);w[n]?w[n].p(q,k):(w[n]=me(q),w[n].c(),w[n].m(c,null))}for(;n<w.length;n+=1)w[n].d(1);w.length=I.length}(!v||k[0]&512)&&S(c,"hide",!s[9]),(!v||k[0]&2304)&&S(a,"hide",!s[11]||!s[8].some(ve))},i(s){v||(G(d.$$.fragment,s),v=!0)},o(s){Q(d.$$.fragment,s),v=!1},d(s){s&&E(e),y(h,s),y(g,s),l[28](null),Fe(d),y(w,s),l[31](null),B=!1,j()}}}function re(l){let e,t=l[33]?.label+"",i,_,r=l[33];const a=()=>l[26](e,r),f=()=>l[26](null,r);return{c(){e=N("button"),i=ee(t),_=R(),m(e,"class","svelte-1tcem6n")},m(d,u){O(d,e,u),z(e,i),z(e,_),a()},p(d,u){l=d,u[0]&8&&t!==(t=l[33]?.label+"")&&x(i,t),r!==l[33]&&(f(),r=l[33],a())},d(d){d&&E(e),f()}}}function ue(l){let e,t=l[33]?.visible&&re(l);return{c(){t&&t.c(),e=$()},m(i,_){t&&t.m(i,_),O(i,e,_)},p(i,_){i[33]?.visible?t?t.p(i,_):(t=re(i),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null)},d(i){i&&E(e),t&&t.d(i)}}}function de(l){let e,t=l[33].label+"",i,_,r,a,f,d,u,c,v,B;function j(){return l[27](l[33],l[37])}return{c(){e=N("button"),i=ee(t),_=R(),m(e,"role","tab"),m(e,"aria-selected",r=l[33].id===l[6]),m(e,"aria-controls",a=l[33].elem_id),e.disabled=f=!l[33].interactive,m(e,"aria-disabled",d=!l[33].interactive),m(e,"id",u=l[33].elem_id?l[33].elem_id+"-button":null),m(e,"data-tab-id",c=l[33].id),m(e,"class","svelte-1tcem6n"),S(e,"selected",l[33].id===l[6])},m(b,h){O(b,e,h),z(e,i),z(e,_),v||(B=H(e,"click",j),v=!0)},p(b,h){l=b,h[0]&128&&t!==(t=l[33].label+"")&&x(i,t),h[0]&192&&r!==(r=l[33].id===l[6])&&m(e,"aria-selected",r),h[0]&128&&a!==(a=l[33].elem_id)&&m(e,"aria-controls",a),h[0]&128&&f!==(f=!l[33].interactive)&&(e.disabled=f),h[0]&128&&d!==(d=!l[33].interactive)&&m(e,"aria-disabled",d),h[0]&128&&u!==(u=l[33].elem_id?l[33].elem_id+"-button":null)&&m(e,"id",u),h[0]&128&&c!==(c=l[33].id)&&m(e,"data-tab-id",c),h[0]&192&&S(e,"selected",l[33].id===l[6])},d(b){b&&E(e),v=!1,B()}}}function be(l){let e,t=l[33]?.visible&&de(l);return{c(){t&&t.c(),e=$()},m(i,_){t&&t.m(i,_),O(i,e,_)},p(i,_){i[33]?.visible?t?t.p(i,_):(t=de(i),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null)},d(i){i&&E(e),t&&t.d(i)}}}function he(l){let e,t=l[33]?.label+"",i,_,r,a;function f(){return l[30](l[33])}return{c(){e=N("button"),i=ee(t),_=R(),m(e,"class","svelte-1tcem6n"),S(e,"selected",l[33]?.id===l[6])},m(d,u){O(d,e,u),z(e,i),z(e,_),r||(a=H(e,"click",f),r=!0)},p(d,u){l=d,u[0]&256&&t!==(t=l[33]?.label+"")&&x(i,t),u[0]&320&&S(e,"selected",l[33]?.id===l[6])},d(d){d&&E(e),r=!1,a()}}}function me(l){let e,t=l[33]?.visible&&he(l);return{c(){t&&t.c(),e=$()},m(i,_){t&&t.m(i,_),O(i,e,_)},p(i,_){i[33]?.visible?t?t.p(i,_):(t=he(i),t.c(),t.m(e.parentNode,e)):t&&(t.d(1),t=null)},d(i){i&&E(e),t&&t.d(i)}}}function ye(l){let e,t,i,_,r,a,f=l[14]&&ce(l);const d=l[25].default,u=Me(d,l,l[24],null);return{c(){e=N("div"),f&&f.c(),t=R(),u&&u.c(),m(e,"class",i="tabs "+l[2].join(" ")+" svelte-1tcem6n"),m(e,"id",l[1]),S(e,"hide",!l[0]),oe(e,"flex-grow",l[13])},m(c,v){O(c,e,v),f&&f.m(e,null),z(e,t),u&&u.m(e,null),_=!0,r||(a=[H(window,"resize",l[20]),H(window,"click",l[19])],r=!0)},p(c,v){c[14]?f?(f.p(c,v),v[0]&16384&&G(f,1)):(f=ce(c),f.c(),G(f,1),f.m(e,t)):f&&(Je(),Q(f,1,1,()=>{f=null}),Ae()),u&&u.p&&(!_||v[0]&16777216)&&Ve(u,d,c,c[24],_?He(d,c[24],v,null):Ge(c[24]),null),(!_||v[0]&4&&i!==(i="tabs "+c[2].join(" ")+" svelte-1tcem6n"))&&m(e,"class",i),(!_||v[0]&2)&&m(e,"id",c[1]),(!_||v[0]&5)&&S(e,"hide",!c[0]),v[0]&8192&&oe(e,"flex-grow",c[13])},i(c){_||(G(f),G(u,c),_=!0)},o(c){Q(f),Q(u,c),_=!1},d(c){c&&E(e),f&&f.d(),u&&u.d(c),r=!1,Pe(a)}}}const $e={};function xe(l,e){const t={};return l.forEach(i=>{i&&(t[i.id]=e[i.id]?.getBoundingClientRect())}),t}const ve=l=>l?.visible;function et(l,e,t){let i,_,r,a,{$$slots:f={},$$scope:d}=e,{visible:u=!0}=e,{elem_id:c=""}=e,{elem_classes:v=[]}=e,{selected:B}=e,{initial_tabs:j}=e,b=[...j],h=[...j],T=[],g=!1,I,w;const s=ne(B||b[0]?.id||!1);se(l,s,o=>t(6,a=o));const k=ne(b.findIndex(o=>o?.id===B)||0);se(l,k,o=>t(23,r=o));const n=Xe();let q=!1,U=!1,J={};Ze(()=>{new IntersectionObserver(p=>{V()}).observe(w)}),We($e,{register_tab:(o,p)=>(t(3,b[p]=o,b),a===!1&&o.visible&&o.interactive&&(F(s,a=o.id,a),F(k,r=p,r)),p),unregister_tab:(o,p)=>{a===o.id&&F(s,a=b[0]?.id||!1,a),t(3,b[p]=null,b)},selected_tab:s,selected_tab_index:k});function K(o){const p=b.find(D=>D?.id===o);o!==void 0&&p&&p.interactive&&p.visible&&a!==p.id&&(t(21,B=o),F(s,a=o,a),F(k,r=b.findIndex(D=>D?.id===o),r),n("change"),t(9,g=!1))}function ge(o){g&&I&&!I.contains(o.target)&&t(9,g=!1)}async function V(){if(!w)return;await Ye();const o=w.getBoundingClientRect();let p=o.width;const D=xe(b,J);let W=0;const Ie=o.left;for(let L=b.length-1;L>=0;L--){const le=b[L];if(!le)continue;const ie=D[le.id];if(ie&&ie.right-Ie<p){W=L;break}}t(8,T=b.slice(W+1)),t(7,h=b.slice(0,W+1)),t(12,U=te(a)),t(11,q=T.length>0)}function te(o){return o===!1?!1:T.some(p=>p?.id===o)}function we(o,p){Z[o?"unshift":"push"](()=>{J[p.id]=o,t(5,J)})}const pe=(o,p)=>{o.id!==a&&(K(o.id),n("select",{value:o.label,index:p}))};function ke(o){Z[o?"unshift":"push"](()=>{w=o,t(4,w)})}const Ce=()=>t(9,g=!g),ze=o=>K(o?.id);function Be(o){Z[o?"unshift":"push"](()=>{I=o,t(10,I)})}return l.$$set=o=>{"visible"in o&&t(0,u=o.visible),"elem_id"in o&&t(1,c=o.elem_id),"elem_classes"in o&&t(2,v=o.elem_classes),"selected"in o&&t(21,B=o.selected),"initial_tabs"in o&&t(22,j=o.initial_tabs),"$$scope"in o&&t(24,d=o.$$scope)},l.$$.update=()=>{l.$$.dirty[0]&8&&t(14,i=b.length>0),l.$$.dirty[0]&2097160&&B!==null&&K(B),l.$$.dirty[0]&56&&V(),l.$$.dirty[0]&64&&t(12,U=te(a)),l.$$.dirty[0]&8388616&&t(13,_=b[r>=0?r:0]?.scale)},[u,c,v,b,w,J,a,h,T,g,I,q,U,_,i,s,k,n,K,ge,V,B,j,r,d,f,we,pe,ke,Ce,ze,Be]}class tt extends Re{constructor(e){super(),Ke(this,e,et,ye,Qe,{visible:0,elem_id:1,elem_classes:2,selected:21,initial_tabs:22},null,[-1,-1])}get visible(){return this.$$.ctx[0]}set visible(e){this.$$set({visible:e}),M()}get elem_id(){return this.$$.ctx[1]}set elem_id(e){this.$$set({elem_id:e}),M()}get elem_classes(){return this.$$.ctx[2]}set elem_classes(e){this.$$set({elem_classes:e}),M()}get selected(){return this.$$.ctx[21]}set selected(e){this.$$set({selected:e}),M()}get initial_tabs(){return this.$$.ctx[22]}set initial_tabs(e){this.$$set({initial_tabs:e}),M()}}const it=tt;export{it as T,$e as a};
//# sourceMappingURL=Tabs-D8sppewm.js.map
