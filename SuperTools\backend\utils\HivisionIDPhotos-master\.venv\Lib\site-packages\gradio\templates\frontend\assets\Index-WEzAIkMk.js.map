{"version": 3, "file": "Index-WEzAIkMk.js", "sources": ["../../../../js/group/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n</script>\n\n<div\n\tid={elem_id}\n\tclass=\"gr-group {elem_classes.join(' ')}\"\n\tclass:hide={!visible}\n>\n\t<div\n\t\tclass=\"styler\"\n\t\tstyle:--block-radius=\"0px\"\n\t\tstyle:--block-border-width=\"0px\"\n\t\tstyle:--layout-gap=\"1px\"\n\t\tstyle:--form-gap-width=\"1px\"\n\t\tstyle:--button-border-width=\"0px\"\n\t\tstyle:--button-large-radius=\"0px\"\n\t\tstyle:--button-small-radius=\"0px\"\n\t>\n\t\t<slot />\n\t</div>\n</div>\n\n<style>\n\tdiv {\n\t\tborder: var(--block-border-width) solid var(--border-color-primary);\n\t\tbackground: var(--block-border-color);\n\t\tborder-radius: var(--block-radius);\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--form-gap-width);\n\t\toverflow: hidden;\n\t}\n\tdiv > :global(*:not(.absolute)) {\n\t\tborder: none;\n\t\tborder-radius: 0;\n\t}\n\t.hide {\n\t\tdisplay: none;\n\t}\n</style>\n"], "names": ["ctx", "insert", "target", "div1", "anchor", "append", "div0", "elem_id", "$$props", "elem_classes", "visible"], "mappings": "2oBAOKA,EAAO,CAAA,CAAA,4BACMA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,iBAAA,cACzBA,EAAO,CAAA,CAAA,UAHrBC,EAiBKC,EAAAC,EAAAC,CAAA,EAZJC,EAWKF,EAAAG,CAAA,qHAfDN,EAAO,CAAA,CAAA,+BACMA,EAAY,CAAA,EAAC,KAAK,GAAG,EAAA,2DACzBA,EAAO,CAAA,CAAA,sHART,QAAAO,EAAU,EAAA,EAAAC,EACV,CAAA,aAAAC,EAAA,EAAA,EAAAD,GACA,QAAAE,EAAU,EAAA,EAAAF"}