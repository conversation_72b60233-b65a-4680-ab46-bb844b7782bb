const{SvelteComponent:_,append:w,attr:e,detach:a,flush:o,init:d,insert:u,noop:h,safe_not_equal:c,svg_element:f}=window.__gradio__svelte__internal;function g(n){let t,i,l;return{c(){t=f("svg"),i=f("rect"),e(i,"x","3"),e(i,"y","3"),e(i,"width","18"),e(i,"height","18"),e(i,"rx","2"),e(i,"ry","2"),e(t,"xmlns","http://www.w3.org/2000/svg"),e(t,"width","100%"),e(t,"height","100%"),e(t,"viewBox","0 0 24 24"),e(t,"fill",n[0]),e(t,"stroke","currentColor"),e(t,"stroke-width",l=`${n[1]}`),e(t,"stroke-linecap","round"),e(t,"stroke-linejoin","round"),e(t,"class","feather feather-square")},m(r,s){u(r,t,s),w(t,i)},p(r,[s]){s&1&&e(t,"fill",r[0]),s&2&&l!==(l=`${r[1]}`)&&e(t,"stroke-width",l)},i:h,o:h,d(r){r&&a(t)}}}function k(n,t,i){let{fill:l="currentColor"}=t,{stroke_width:r=1.5}=t;return n.$$set=s=>{"fill"in s&&i(0,l=s.fill),"stroke_width"in s&&i(1,r=s.stroke_width)},[l,r]}class v extends _{constructor(t){super(),d(this,t,k,g,c,{fill:0,stroke_width:1})}get fill(){return this.$$.ctx[0]}set fill(t){this.$$set({fill:t}),o()}get stroke_width(){return this.$$.ctx[1]}set stroke_width(t){this.$$set({stroke_width:t}),o()}}export{v as S};
//# sourceMappingURL=Square-oAGqOwsh.js.map
