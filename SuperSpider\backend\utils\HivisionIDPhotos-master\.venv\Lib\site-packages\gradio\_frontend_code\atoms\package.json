{"name": "@gradio/atoms", "version": "0.16.1", "description": "Gradio UI packages", "type": "module", "main": "src/index.ts", "author": "", "license": "ISC", "dependencies": {"@gradio/icons": "workspace:^", "@gradio/markdown-code": "workspace:^", "@gradio/utils": "workspace:^"}, "peerDependencies": {"svelte": "^4.0.0"}, "exports": {".": {"gradio": "./src/index.ts", "svelte": "./dist/src/index.js", "types": "./dist/src/index.d.ts"}, "./package.json": "./package.json"}, "main_changeset": true, "repository": {"type": "git", "url": "git+https://github.com/gradio-app/gradio.git", "directory": "js/atoms"}, "scripts": {"sv-pkg": "svelte-package --input=. --cwd=../../.config/"}}