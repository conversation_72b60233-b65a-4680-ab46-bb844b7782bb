#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
调试启动脚本
用于启动应用并提供调试信息
"""

import os
import sys
import webbrowser
import time
from threading import Timer

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

def open_browser():
    """延迟打开浏览器"""
    time.sleep(2)  # 等待服务器启动
    print("正在打开浏览器...")
    webbrowser.open('http://localhost:5000')

def main():
    print("=" * 60)
    print("SuperSpider 调试模式启动")
    print("=" * 60)
    print()
    
    print("🔧 功能测试指南:")
    print("1. 主页面: http://localhost:5000")
    print("2. 登录后点击右上角用户菜单中的'搜索记录'")
    print("3. 或使用页面右下角的调试面板")
    print("4. 查看浏览器控制台获取详细调试信息")
    print()
    
    print("🐛 调试按钮说明:")
    print("- 测试搜索历史: 直接显示搜索历史模态框")
    print("- 检查登录状态: 在控制台显示当前登录状态")
    print("- 测试API: 直接调用搜索历史API")
    print()
    
    print("📝 命名修正总结:")
    print("- downloads.js → search-history.js")
    print("- selectedDownloadIds → selectedRecordIds")
    print("- downloads-modal → search-history-modal")
    print("- 所有相关函数和变量都已更新")
    print()
    
    # 延迟打开浏览器
    timer = Timer(2.0, open_browser)
    timer.start()
    
    print("🚀 启动Flask应用...")
    print("按 Ctrl+C 停止服务器")
    print("=" * 60)
    
    try:
        # 启动Flask应用
        from run import app
        app.run(debug=True, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        print("请检查:")
        print("1. 数据库连接是否正常")
        print("2. 端口5000是否被占用")
        print("3. 依赖包是否安装完整")

if __name__ == '__main__':
    main()
