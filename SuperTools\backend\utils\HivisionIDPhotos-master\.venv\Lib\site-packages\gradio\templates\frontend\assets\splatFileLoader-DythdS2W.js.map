{"version": 3, "mappings": "++BAIA,MAAMA,GAAO,uCACPC,GAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUVC,EAAY,qBAAqBF,EAAI,IACtCE,EAAY,qBAAqBF,EAAI,EAAIC,ICT7C,MAAMD,GAAO,+BACPC,GAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUVC,EAAY,aAAaF,EAAI,IAC9BE,EAAY,aAAaF,EAAI,EAAIC,IAG9B,MAAME,GAA+B,MAAEH,GAAI,OAAEC,EAAQ,gICpBtDD,GAAO,qCACPC,GAAS,kHAEVC,EAAY,qBAAqBF,EAAI,IACtCE,EAAY,qBAAqBF,EAAI,EAAIC,ICF7C,MAAMD,GAAO,kCACPC,GAAS;AAAA;AAAA,0BAIVC,EAAY,qBAAqBF,EAAI,IACtCE,EAAY,qBAAqBF,EAAI,EAAIC,ICR7C,MAAMD,GAAO,oBACPC,GAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+DAgGVC,EAAY,qBAAqBF,EAAI,IACtCE,EAAY,qBAAqBF,EAAI,EAAIC,ICxF7C,MAAMD,GAAO,gCACPC,GAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgCVC,EAAY,aAAaF,EAAI,IAC9BE,EAAY,aAAaF,EAAI,EAAIC,IAG9B,MAAMG,GAAgC,MAAEJ,GAAI,OAAEC,EAAQ,iIC7CvDD,GAAO,uCACPC,GAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWVC,EAAY,yBAAyBF,EAAI,IAC1CE,EAAY,yBAAyBF,EAAI,EAAIC,ICVjD,MAAMD,GAAO,+BACPC,GAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWVC,EAAY,iBAAiBF,EAAI,IAClCE,EAAY,iBAAiBF,EAAI,EAAIC,IAGlC,MAAMI,GAAmC,MAAEL,GAAI,OAAEC,EAAQ,oICrB1DD,GAAO,oBACPC,GAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoHVC,EAAY,yBAAyBF,EAAI,IAC1CE,EAAY,yBAAyBF,EAAI,EAAIC,IC5GjD,MAAMD,GAAO,gCACPC,GAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiCVC,EAAY,iBAAiBF,EAAI,IAClCE,EAAY,iBAAiBF,EAAI,EAAIC,IAGlC,MAAMK,GAAoC,CAAE,KAAAN,GAAM,OAAAC,EAAQ,qICnCjE,MAAMM,WAAyCC,EAAgB,CAI3D,aAAc,CACV,QACA,KAAK,IAAM,GACX,KAAK,eAAiB,GACtB,KAAK,iBAAmB,GACxB,KAAK,UAAY,GACjB,KAAK,WAAa,GAClB,KAAK,WAAa,GAClB,KAAK,WAAa,GAClB,KAAK,WAAa,GAClB,KAAK,WAAa,GAClB,KAAK,UAAY,EACjB,KAAK,QAAO,CACf,CACL,CAKO,MAAMC,WAAkCC,EAAa,CAMxD,YAAYV,EAAMW,EAAO,CACrB,MAAMX,EAAMW,CAAK,EACjB,KAAK,gBAAkB,EAC1B,CAID,IAAI,yBAA0B,CAC1B,MAAO,EACV,CAKD,kBAAmB,CACf,MAAO,EACV,CAKD,mBAAoB,CAChB,MAAO,EACV,CAOD,kBAAkBC,EAAMC,EAAS,CAE7B,MAAMC,EAAcD,EAAQ,aAC5B,GAAIC,EAAY,QAAU,KAAK,UACvBA,EAAY,qBAAuBA,EAAY,+BAAiC,GAChF,MAAO,GAGVD,EAAQ,kBACTA,EAAQ,gBAAkB,IAAIN,IAElC,MAAMI,EAAQ,KAAK,WACbI,EAAUF,EAAQ,gBACxB,GAAI,KAAK,mBAAmBA,CAAO,EAC/B,MAAO,GAEX,MAAMG,EAASL,EAAM,YAYrB,GAVAM,GAAsBL,EAAMD,EAAO,KAAK,qBAAsB,KAAK,YAAa,KAAK,WAAY,GAAOI,CAAO,EAE/GG,GAAkCP,EAAOK,EAAQ,KAAMD,EAAS,GAAc,KAAM,EAAI,EAExFI,GAA4BP,EAAMG,EAAS,GAAO,EAAK,GAEnDC,EAAO,QAAU,GAAKA,EAAO,YAC7BD,EAAQ,UAAeH,EAAK,UAG5BG,EAAQ,QAAS,CACjBA,EAAQ,gBAAe,EACvBJ,EAAM,oBAAmB,EAEzB,MAAMS,EAAU,CAACC,EAAa,aAAc,YAAY,EACxDC,GAA8BF,EAASL,CAAO,EAC9C,MAAMQ,EAAW,CAAC,QAAS,OAAQ,aAAc,YAAa,YAAa,2BAA4B,cAAe,kBAAmB,QAAS,cAAc,EAC1JC,EAAW,CAAC,sBAAuB,sBAAuB,iBAAkB,gBAAiB,aAAc,aAAc,YAAY,EACrIC,EAAiB,CAAC,QAAS,MAAM,EACvCC,GAA+B,CAC3B,cAAeH,EACf,oBAAqBE,EACrB,SAAUD,EACV,QAAST,CACzB,CAAa,EACDY,GAAqBJ,CAAQ,EAC7B,MAAMK,EAAOb,EAAQ,WACfc,EAASlB,EAAM,UAAS,EAAG,aAAa,oBAAqB,CAC/D,WAAYS,EACZ,cAAeG,EACf,oBAAqBE,EACrB,SAAUD,EACV,QAASI,EACT,WAAY,KAAK,WACjB,QAAS,KAAK,QACd,gBAAiB,CAAE,EACnB,eAAgB,KAAK,gBACrB,0BAA2B,SAAY,CAC/B,KAAK,kBAAoB,EACzB,MAAM,QAAQ,IAAI,CAAAE,GAAA,IAAC,2BAAAC,EAAA,EAAwD,wBAAED,GAAA,IAAC,+BAAuD,wBAAC,EAGtI,MAAM,QAAQ,IAAI,CAAAA,GAAA,IAAC,2BAAAE,EAAA,EAAoD,wBAAEF,GAAA,IAAC,+BAAmD,wBAAC,CAErI,CACJ,EAAEd,CAAM,EACTH,EAAQ,UAAUgB,EAAQd,EAAS,KAAK,gBAAgB,CAC3D,CACD,MAAI,CAACF,EAAQ,QAAU,CAACA,EAAQ,OAAO,UAC5B,IAEXE,EAAQ,UAAYJ,EAAM,cAC1BG,EAAY,oBAAsB,GAClCA,EAAY,6BAA+B,GACpC,GACV,CAOD,OAAO,WAAWF,EAAMiB,EAAQlB,EAAO,CACnC,MAAMK,EAASL,EAAM,YACfsB,EAAStB,EAAM,aACfuB,EAAclB,EAAO,iBACrBmB,EAAenB,EAAO,kBAEtBoB,EAAeH,GAAQ,WAAW,WAAW,QAAU,EAC7DJ,EAAO,UAAU,cAAe,GAAKK,EAAcE,GAAe,EAAID,CAAY,EAClF,IAAIE,EAAQ,IACZ,GAAIJ,EAAQ,CAQR,MAAMK,EAAIL,EAAO,oBAAqB,EAAC,EAAE,CAAC,EACtCA,EAAO,SAAWM,GAAO,uBACzBF,EAASF,EAAeG,EAAK,EAG7BD,EAASH,EAAcI,EAAK,CAEnC,CACDT,EAAO,UAAU,QAASQ,EAAOA,CAAK,EACtC,MAAMG,EAAS5B,EACf,GAAI4B,EAAO,oBAAqB,CAC5B,MAAMC,EAAcD,EAAO,oBAAoB,QAAO,EAMtD,GALAX,EAAO,UAAU,kBAAmBY,EAAY,MAAOA,EAAY,MAAM,EACzEZ,EAAO,WAAW,sBAAuBW,EAAO,mBAAmB,EACnEX,EAAO,WAAW,sBAAuBW,EAAO,mBAAmB,EACnEX,EAAO,WAAW,iBAAkBW,EAAO,cAAc,EACzDX,EAAO,WAAW,gBAAiBW,EAAO,aAAa,EACnDA,EAAO,WACP,QAASE,EAAI,EAAGA,EAAIF,EAAO,YAAY,OAAQE,IAC3Cb,EAAO,WAAW,YAAYa,CAAC,GAAIF,EAAO,WAAWE,CAAC,CAAC,CAGlE,CACJ,CAOD,eAAeC,EAAO/B,EAAMC,EAAS,CACjC,MAAMF,EAAQ,KAAK,WACbI,EAAUF,EAAQ,gBACxB,GAAI,CAACE,EACD,OAEJ,MAAMc,EAAShB,EAAQ,OACvB,GAAI,CAACgB,EACD,OAEJ,KAAK,cAAgBA,EAErBjB,EAAK,qBAAsB,EAAC,aAAaiB,EAAQ,MAAM,EACvDjB,EAAK,iBAAiB+B,CAAK,EAER,KAAK,YAAYhC,EAAOkB,EAAQhB,EAASD,EAAK,UAAU,GAEvE,KAAK,SAASiB,CAAM,EACpB,KAAK,mBAAmBA,CAAM,EAC9BpB,GAA0B,WAAWG,EAAM,KAAK,cAAeD,CAAK,EAEpEiC,GAAcf,EAAQ,KAAMlB,CAAK,GAE5BA,EAAM,YAAY,UAAU,iCACjC,KAAK,oBAAsB,IAG/BkC,GAAkBlC,EAAOC,EAAMiB,CAAM,EAEjC,KAAK,qBACLiB,GAAa/B,EAASc,EAAQlB,CAAK,EAEvC,KAAK,WAAWC,EAAM,KAAK,cAAeC,CAAO,CACpD,CAMD,MAAMb,EAAM,CACR,OAAO+C,GAAoB,MAAM,IAAM,IAAItC,GAA0BT,EAAM,KAAK,SAAQ,CAAE,EAAG,IAAI,CACpG,CAKD,WAAY,CACR,MAAMgD,EAAsB,MAAM,YAClC,OAAAA,EAAoB,WAAa,oCAC1BA,CACV,CAKD,cAAe,CACX,MAAO,2BACV,CAQD,OAAO,MAAMC,EAAQtC,EAAOuC,EAAS,CACjC,OAAOH,GAAoB,MAAM,IAAM,IAAItC,GAA0BwC,EAAO,KAAMtC,CAAK,EAAGsC,EAAQtC,EAAOuC,CAAO,CACnH,CACL,CACAC,GAAc,oCAAqC1C,EAAyB,EC5Q5E,MAAM2C,GAAMC,GAICC,EAAS,CAClB,GAAGC,GAIH,MAAO,KAAK,GAAK,EAMjB,KAAM,KAAK,KAMX,KAAM,KAAK,KAOX,IAAAH,EACJ,EChBMI,EAAc,CAACC,EAAOC,IAAS,CACjC,MAAMpB,GAAK,GAAKoB,GAAQ,EACxB,OAAQD,EAAQnB,GAAKA,CACzB,EAEMqB,GAAe,CAACF,EAAOG,IAAW,CACpCA,EAAO,EAAIJ,EAAYC,IAAU,GAAI,EAAE,EACvCG,EAAO,EAAIJ,EAAYC,IAAU,GAAI,EAAE,EACvCG,EAAO,EAAIJ,EAAYC,EAAO,EAAE,CACpC,EAEMI,GAAa,CAACJ,EAAOG,IAAW,CAClCA,EAAO,CAAC,EAAIJ,EAAYC,IAAU,GAAI,CAAC,EAAI,IAC3CG,EAAO,CAAC,EAAIJ,EAAYC,IAAU,GAAI,CAAC,EAAI,IAC3CG,EAAO,CAAC,EAAIJ,EAAYC,IAAU,EAAG,CAAC,EAAI,IAC1CG,EAAO,CAAC,EAAIJ,EAAYC,EAAO,CAAC,EAAI,GACxC,EAGMK,GAAY,CAACL,EAAOG,IAAW,CACjC,MAAMG,EAAO,GAAO,KAAK,KAAK,CAAC,EAAI,IAC7BC,GAAKR,EAAYC,IAAU,GAAI,EAAE,EAAI,IAAOM,EAC5CE,GAAKT,EAAYC,IAAU,GAAI,EAAE,EAAI,IAAOM,EAC5CG,GAAKV,EAAYC,EAAO,EAAE,EAAI,IAAOM,EACrCI,EAAI,KAAK,KAAK,GAAOH,EAAIA,EAAIC,EAAIA,EAAIC,EAAIA,EAAE,EACjD,OAAQT,IAAU,GAAE,CAChB,IAAK,GACDG,EAAO,IAAIO,EAAGH,EAAGC,EAAGC,CAAC,EACrB,MACJ,IAAK,GACDN,EAAO,IAAII,EAAGG,EAAGF,EAAGC,CAAC,EACrB,MACJ,IAAK,GACDN,EAAO,IAAII,EAAGC,EAAGE,EAAGD,CAAC,EACrB,MACJ,IAAK,GACDN,EAAO,IAAII,EAAGC,EAAGC,EAAGC,CAAC,EACrB,KACP,CACL,EAIA,IAAIC,IACH,SAAUA,EAAS,CAChBA,EAAQA,EAAQ,MAAW,CAAC,EAAI,QAChCA,EAAQA,EAAQ,IAAS,CAAC,EAAI,MAC9BA,EAAQA,EAAQ,KAAU,CAAC,EAAI,OAC/BA,EAAQA,EAAQ,OAAY,CAAC,EAAI,SACjCA,EAAQA,EAAQ,MAAW,CAAC,EAAI,QAChCA,EAAQA,EAAQ,UAAe,CAAC,EAAI,WACxC,GAAGA,KAAYA,GAAU,CAAE,EAAC,EAI5B,IAAIC,IACH,SAAUA,EAAU,CACjBA,EAASA,EAAS,MAAW,CAAC,EAAI,QAClCA,EAASA,EAAS,MAAW,CAAC,EAAI,QAClCA,EAASA,EAAS,MAAW,CAAC,EAAI,QAClCA,EAASA,EAAS,MAAW,CAAC,EAAI,QAClCA,EAASA,EAAS,MAAW,CAAC,EAAI,QAClCA,EAASA,EAAS,MAAW,CAAC,EAAI,QAClCA,EAASA,EAAS,YAAiB,CAAC,EAAI,cACxCA,EAASA,EAAS,YAAiB,CAAC,EAAI,cACxCA,EAASA,EAAS,YAAiB,CAAC,EAAI,cACxCA,EAASA,EAAS,YAAiB,CAAC,EAAI,cACxCA,EAASA,EAAS,YAAiB,EAAE,EAAI,cACzCA,EAASA,EAAS,YAAiB,EAAE,EAAI,cACzCA,EAASA,EAAS,gBAAqB,EAAE,EAAI,kBAC7CA,EAASA,EAAS,gBAAqB,EAAE,EAAI,kBAC7CA,EAASA,EAAS,aAAkB,EAAE,EAAI,eAC1CA,EAASA,EAAS,aAAkB,EAAE,EAAI,eAC1CA,EAASA,EAAS,EAAO,EAAE,EAAI,IAC/BA,EAASA,EAAS,EAAO,EAAE,EAAI,IAC/BA,EAASA,EAAS,EAAO,EAAE,EAAI,IAC/BA,EAASA,EAAS,QAAa,EAAE,EAAI,UACrCA,EAASA,EAAS,QAAa,EAAE,EAAI,UACrCA,EAASA,EAAS,QAAa,EAAE,EAAI,UACrCA,EAASA,EAAS,YAAiB,EAAE,EAAI,cACzCA,EAASA,EAAS,cAAmB,EAAE,EAAI,gBAC3CA,EAASA,EAAS,aAAkB,EAAE,EAAI,eAC1CA,EAASA,EAAS,QAAa,EAAE,EAAI,UACrCA,EAASA,EAAS,OAAY,EAAE,EAAI,SACpCA,EAASA,EAAS,OAAY,EAAE,EAAI,SACpCA,EAASA,EAAS,OAAY,EAAE,EAAI,SACpCA,EAASA,EAAS,OAAY,EAAE,EAAI,SACpCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,YAAiB,EAAE,EAAI,cACzCA,EAASA,EAAS,YAAiB,EAAE,EAAI,cACzCA,EAASA,EAAS,YAAiB,EAAE,EAAI,cACzCA,EAASA,EAAS,YAAiB,EAAE,EAAI,cACzCA,EAASA,EAAS,YAAiB,EAAE,EAAI,cACzCA,EAASA,EAAS,YAAiB,EAAE,EAAI,cACzCA,EAASA,EAAS,KAAU,EAAE,EAAI,OAClCA,EAASA,EAAS,KAAU,EAAE,EAAI,OAClCA,EAASA,EAAS,KAAU,EAAE,EAAI,OAClCA,EAASA,EAAS,KAAU,EAAE,EAAI,OAClCA,EAASA,EAAS,KAAU,EAAE,EAAI,OAClCA,EAASA,EAAS,KAAU,EAAE,EAAI,OAClCA,EAASA,EAAS,KAAU,EAAE,EAAI,OAClCA,EAASA,EAAS,KAAU,EAAE,EAAI,OAClCA,EAASA,EAAS,KAAU,EAAE,EAAI,OAClCA,EAASA,EAAS,KAAU,EAAE,EAAI,OAClCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,MAAW,EAAE,EAAI,QACnCA,EAASA,EAAS,UAAe,EAAE,EAAI,WAC3C,GAAGA,KAAaA,GAAW,CAAE,EAAC,EAIvB,MAAMC,UAA8BC,EAAK,CAI5C,IAAI,UAAW,CACX,OAAO,KAAK,SACf,CAID,IAAI,YAAa,CACb,OAAO,KAAK,WACf,CAID,IAAI,qBAAsB,CACtB,OAAO,KAAK,oBACf,CAID,IAAI,qBAAsB,CACtB,OAAO,KAAK,oBACf,CAID,IAAI,gBAAiB,CACjB,OAAO,KAAK,eACf,CAID,IAAI,eAAgB,CAChB,OAAO,KAAK,cACf,CAID,IAAI,YAAa,CACb,OAAO,KAAK,WACf,CAID,IAAI,SAASd,EAAO,CAChB,KAAK,UAAYA,EACjB,KAAK,UAAU,gBAAkB,GACjC,KAAK,UAAU,cAAgB,GAC/BA,EAAM,eAAc,CACvB,CAID,IAAI,UAAW,CACX,OAAO,KAAK,SACf,CAQD,YAAYzD,EAAMwE,EAAM,KAAM7D,EAAQ,KAAM8D,EAAY,GAAO,CAC3D,MAAMzE,EAAMW,CAAK,EACjB,KAAK,aAAe,EACpB,KAAK,QAAU,KACf,KAAK,mBAAqB,GAC1B,KAAK,iBAAmB+D,GAAO,WAC/B,KAAK,iBAAmB,GACxB,KAAK,gBAAkB,GACvB,KAAK,qBAAuB,KAC5B,KAAK,qBAAuB,KAC5B,KAAK,gBAAkB,KACvB,KAAK,eAAiB,KACtB,KAAK,gBAAkB,KACvB,KAAK,YAAc,KACnB,KAAK,YAAc,KACnB,KAAK,YAAc,KACnB,KAAK,IAAM,KACX,KAAK,WAAa,GAClB,KAAK,sBAAwB,KAC7B,KAAK,cAAgB,IAAIC,EACzB,KAAK,mBAAqB,GAC1B,KAAK,UAAY,KACjB,KAAK,gBAAkB,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,CAAC,EACxC,KAAK,aAAe,GACpB,KAAK,UAAY,EACjB,MAAMC,EAAa,IAAIC,GAMvBD,EAAW,UAAY,CAAC,GAAI,GAAI,EAAG,EAAG,GAAI,EAAG,EAAG,EAAG,CAAC,EACpDA,EAAW,QAAU,CAAC,EAAG,EAAG,CAAC,EAC7BA,EAAW,YAAY,IAAI,EAC3B,KAAK,UAAY,GAGjB,IAAIE,GAAQ,EAAG,EAAG,EAAG,EAAG,EAAG,IAAI,EAC/B,KAAK,WAAW,EAAK,EAErB,KAAK,mBAAqB,CAAC,KAAK,UAAW,EAAC,UAAY,KAAK,UAAS,EAAG,UAAY,EACrF,KAAK,WAAaL,EACdD,GACA,KAAK,cAAcA,CAAG,EAE1B,KAAK,UAAY,IAAI/D,GAA0B,KAAK,KAAO,YAAa,KAAK,MAAM,CACtF,CAKD,cAAe,CACX,MAAO,uBACV,CAKD,kBAAmB,CACf,OAAO,KAAK,YACf,CAMD,QAAQsE,EAAgB,GAAO,CAC3B,OAAK,MAAM,QAAQA,EAAe,EAAI,EAGjC,KAAK,gBAKH,IAHH,KAAK,cAAc,EAAI,EAChB,IALA,EAQd,CAED,cAAcC,EAAS,GAAO,CAC1B,MAAMC,EAAU,KAAK,SAAU,EAAC,WAAU,EAC1C,IAAKD,GAAUC,IAAY,KAAK,qBAAuB,KAAK,SAAW,KAAK,OAAO,cAAgB,KAAK,iBAAkB,CACtH,MAAMC,EAAe,KAAK,OAAO,aAAa,cAAa,EAC3D,KAAK,eAAc,EAAG,cAAcA,EAAc,KAAK,gBAAgB,EACvEA,EAAa,YAAYC,EAAW,OAAO,CAAC,CAAC,EAC7C,KAAK,eAAc,EAAG,cAAcA,EAAW,OAAO,CAAC,EAAGA,EAAW,OAAO,CAAC,CAAC,EAC9ER,EAAQ,qBAAqBA,EAAQ,QAAQ,KAAK,OAAO,oBAAoB,EAAGQ,EAAW,OAAO,CAAC,EAAGA,EAAW,QAAQ,CAAC,CAAC,EAC3HA,EAAW,QAAQ,CAAC,EAAE,UAAS,EAC/B,MAAMC,EAAMT,EAAQ,IAAIQ,EAAW,QAAQ,CAAC,EAAG,KAAK,aAAa,GAC7DH,GAAU,KAAK,IAAII,EAAM,CAAC,GAAK,OAC/B,KAAK,cAAc,SAASD,EAAW,QAAQ,CAAC,CAAC,EACjD,KAAK,mBAAqBF,EAC1B,KAAK,iBAAmB,GACxB,KAAK,QAAQ,YAAY,CAAE,KAAM,KAAK,iBAAiB,EAAG,SAAU,KAAK,UAAW,qBAAsB,KAAK,OAAO,sBAAwB,CAC1I,KAAK,UAAU,MACnC,CAAiB,EAER,CACJ,CAQD,OAAOpE,EAASwE,EAAiBC,EAA0B,CACvD,YAAK,cAAa,EACX,MAAM,OAAOzE,EAASwE,EAAiBC,CAAwB,CACzE,CACD,OAAO,gBAAgBtF,EAAM,CACzB,OAAQA,EAAI,CACR,IAAK,QACD,MAAO,GACX,IAAK,MACD,MAAO,GAEX,IAAK,OACD,MAAO,GACX,IAAK,SACD,MAAO,GACX,IAAK,QACD,MAAO,EACd,CACD,MAAO,EACV,CACD,OAAO,iBAAiBA,EAAM,CAC1B,OAAQA,EAAI,CACR,IAAK,QACD,MAAO,GACX,IAAK,QACD,MAAO,GACX,IAAK,QACD,MAAO,GACX,IAAK,QACD,MAAO,GACX,IAAK,QACD,MAAO,GACX,IAAK,QACD,MAAO,GACX,IAAK,cACD,MAAO,GACX,IAAK,cACD,MAAO,GACX,IAAK,cACD,MAAO,GACX,IAAK,cACD,MAAO,GACX,IAAK,cACD,MAAO,IACX,IAAK,cACD,MAAO,IACX,IAAK,kBACD,MAAO,IACX,IAAK,kBACD,MAAO,IACX,IAAK,eACD,MAAO,IACX,IAAK,eACD,MAAO,IACX,IAAK,IACD,MAAO,IACX,IAAK,IACD,MAAO,IACX,IAAK,IACD,MAAO,IACX,IAAK,UACD,MAAO,IACX,IAAK,UACD,MAAO,IACX,IAAK,UACD,MAAO,IACX,IAAK,cACL,IAAK,MACD,MAAO,IACX,IAAK,gBACL,IAAK,QACD,MAAO,IACX,IAAK,eACL,IAAK,OACD,MAAO,IACX,IAAK,SACD,MAAO,IACX,IAAK,SACD,MAAO,IACX,IAAK,SACD,MAAO,IACX,IAAK,SACD,MAAO,IACX,IAAK,UACD,MAAO,IACX,IAAK,QACD,MAAO,IACX,IAAK,QACD,MAAO,IACX,IAAK,QACD,MAAO,IACX,IAAK,QACD,MAAO,IACX,IAAK,QACD,MAAO,IACX,IAAK,QACD,MAAO,IACX,IAAK,QACD,MAAO,IACX,IAAK,QACD,MAAO,IACX,IAAK,QACD,MAAO,IACX,IAAK,QACD,MAAO,IACX,IAAK,WACD,MAAO,IACX,IAAK,WACD,MAAO,IACX,IAAK,WACD,MAAO,IACX,IAAK,WACD,MAAO,IACX,IAAK,WACD,MAAO,IACX,IAAK,WACD,MAAO,IACX,IAAK,WACD,MAAO,IACX,IAAK,WACD,MAAO,IACX,IAAK,WACD,MAAO,IACX,IAAK,WACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,IACX,IAAK,YACD,MAAO,GACd,CACD,MAAO,GACV,CAMD,OAAO,YAAYuF,EAAM,CACrB,MAAMC,EAAO,IAAI,WAAWD,CAAI,EAC1BE,EAAS,IAAI,YAAa,EAAC,OAAOD,EAAK,MAAM,EAAG,KAAO,EAAE,CAAC,EAC1DE,EAAY;AAAA,EACZC,EAAiBF,EAAO,QAAQC,CAAS,EAC/C,GAAIC,EAAiB,GAAK,CAACF,EAEvB,OAAO,KAEX,MAAMG,EAAc,SAAS,yBAAyB,KAAKH,CAAM,EAAE,CAAC,CAAC,EAC/DI,EAAe,wBAAwB,KAAKJ,CAAM,EACxD,IAAIK,EAAa,EACbD,IACAC,EAAa,SAASD,EAAa,CAAC,CAAC,GAEzC,IAAIE,EAAkB,EAClBC,EAAiB,EACrB,MAAMC,EAAU,CACZ,OAAQ,EACR,IAAK,EACL,KAAM,EACN,MAAO,EACP,MAAO,EACP,OAAQ,EACR,MAAO,EACP,KAAM,CAClB,EACQ,IAAIC,GACH,SAAUA,EAAa,CACpBA,EAAYA,EAAY,OAAY,CAAC,EAAI,SACzCA,EAAYA,EAAY,MAAW,CAAC,EAAI,OAC3C,GAAEA,IAAgBA,EAAc,CAAE,EAAC,EACpC,IAAIC,EAAY,EAChB,MAAMC,EAAmB,GACnBC,EAAkB,GAClBC,EAAWb,EAAO,MAAM,EAAGE,CAAc,EAAE,MAAM;AAAA,CAAI,EAC3D,IAAIY,EAAW,EACf,UAAWC,KAAQF,EACf,GAAIE,EAAK,WAAW,WAAW,EAAG,CAC9B,KAAM,EAAGC,EAAUzG,CAAI,EAAIwG,EAAK,MAAM,GAAG,EACnC/C,EAAQa,EAAsB,iBAAiBtE,CAAI,EAErDyD,GAAS,GACT8C,EAAW,EAEN9C,GAAS,GACd8C,EAAW,EAEN9C,GAAS,KACd8C,EAAW,GAEf,MAAMG,EAAOpC,EAAsB,gBAAgBmC,CAAQ,EACvDN,GAAa,GACbE,EAAgB,KAAK,CAAE,MAAA5C,EAAO,KAAAiD,EAAM,OAAQV,CAAc,CAAE,EAC5DA,GAAkBC,EAAQQ,CAAQ,GAE7BN,GAAa,IAClBC,EAAiB,KAAK,CAAE,MAAA3C,EAAO,KAAAiD,EAAM,OAAQX,CAAe,CAAE,EAC9DA,GAAmBE,EAAQQ,CAAQ,GAElCR,EAAQQ,CAAQ,GACjBE,GAAO,KAAK,8BAA8BF,CAAQ,GAAG,CAE5D,SACQD,EAAK,WAAW,UAAU,EAAG,CAClC,KAAM,CAAG,CAAAE,CAAI,EAAIF,EAAK,MAAM,GAAG,EAC3BE,GAAQ,QACRP,EAAY,EAEPO,GAAQ,WACbP,EAAY,EAEnB,CAEL,MAAMS,EAAW,IAAI,SAASrB,EAAMI,EAAiBD,EAAU,MAAM,EAC/DmB,EAAS,IAAI,YAAYvC,EAAsB,iBAAmBsB,CAAW,EACnF,IAAIkB,EAAW,KACXC,EAAqB,EACzB,OAAIR,IAEAQ,IADuBR,EAAW,IAAMA,EAAW,GAAK,GACnB,EACrCO,EAAW,IAAI,YAAYC,EAAqBnB,CAAW,GAExD,CACH,YAAaA,EACb,WAAYE,EACZ,gBAAiBC,EACjB,eAAgBC,EAChB,iBAAkBI,EAClB,gBAAiBC,EACjB,SAAUO,EACV,OAAQC,EACR,SAAUN,EACV,mBAAoBQ,EACpB,SAAUD,CACtB,CACK,CACD,OAAO,qBAAqBrB,EAAQuB,EAAQ,CACxC,GAAI,CAACvB,EAAO,WACR,OAAO,KAEX,MAAMmB,EAAWnB,EAAO,SAClBwB,EAAmB,IAAI,MAAMxB,EAAO,UAAU,EACpD,QAAS/C,EAAI,EAAGA,EAAI+C,EAAO,WAAY/C,IAAK,CACxC,MAAMwE,EAAe,CACjB,IAAK,IAAIvC,EACT,IAAK,IAAIA,EACT,SAAU,IAAIA,EACd,SAAU,IAAIA,EACd,SAAU,IAAIA,EAAQ,EAAG,EAAG,CAAC,EAC7B,SAAU,IAAIA,EAAQ,EAAG,EAAG,CAAC,CAC7C,EACYsC,EAAiBvE,CAAC,EAAIwE,EACtB,QAASC,EAAgB,EAAGA,EAAgB1B,EAAO,gBAAgB,OAAQ0B,IAAiB,CACxF,MAAMC,EAAW3B,EAAO,gBAAgB0B,CAAa,EACrD,IAAI1D,EACJ,OAAQ2D,EAAS,KAAI,CACjB,IAAK,GACD3D,EAAQmD,EAAS,WAAWQ,EAAS,OAASJ,EAAO,MAAO,EAAI,EAChE,MACJ,QACI,QACP,CACD,OAAQI,EAAS,MAAK,CAClB,IAAK,GACDF,EAAa,IAAI,EAAIzD,EACrB,MACJ,IAAK,GACDyD,EAAa,IAAI,EAAIzD,EACrB,MACJ,IAAK,GACDyD,EAAa,IAAI,EAAIzD,EACrB,MACJ,IAAK,GACDyD,EAAa,IAAI,EAAIzD,EACrB,MACJ,IAAK,GACDyD,EAAa,IAAI,EAAIzD,EACrB,MACJ,IAAK,GACDyD,EAAa,IAAI,EAAIzD,EACrB,MACJ,IAAK,GACDyD,EAAa,SAAS,EAAIzD,EAC1B,MACJ,IAAK,GACDyD,EAAa,SAAS,EAAIzD,EAC1B,MACJ,IAAK,GACDyD,EAAa,SAAS,EAAIzD,EAC1B,MACJ,IAAK,GACDyD,EAAa,SAAS,EAAIzD,EAC1B,MACJ,IAAK,IACDyD,EAAa,SAAS,EAAIzD,EAC1B,MACJ,IAAK,IACDyD,EAAa,SAAS,EAAIzD,EAC1B,MACJ,IAAK,IACDyD,EAAa,SAAS,EAAIzD,EAC1B,MACJ,IAAK,IACDyD,EAAa,SAAS,EAAIzD,EAC1B,MACJ,IAAK,IACDyD,EAAa,SAAS,EAAIzD,EAC1B,MACJ,IAAK,IACDyD,EAAa,SAAS,EAAIzD,EAC1B,MACJ,IAAK,IACDyD,EAAa,SAAS,EAAIzD,EAC1B,MACJ,IAAK,IACDyD,EAAa,SAAS,EAAIzD,EAC1B,KACP,CACJ,CACDuD,EAAO,OAASvB,EAAO,cAC1B,CACD,OAAOwB,CACV,CACD,OAAO,UAAUxB,EAAQ4B,EAAOJ,EAAkBD,EAAQ,CACtD,MAAMM,EAAInC,EAAW,WAAW,CAAC,EAC3BoC,EAAQpC,EAAW,QAAQ,CAAC,EAC5BqC,EAAkBlD,EAAsB,iBACxCuC,EAASpB,EAAO,OAChBmB,EAAWnB,EAAO,SAClBgC,EAAW,IAAI,aAAaZ,EAAQQ,EAAQG,EAAiB,CAAC,EAC9DE,EAAQ,IAAI,aAAab,EAAQQ,EAAQG,EAAkB,GAAI,CAAC,EAChEG,EAAO,IAAI,kBAAkBd,EAAQQ,EAAQG,EAAkB,GAAI,CAAC,EACpEI,EAAM,IAAI,kBAAkBf,EAAQQ,EAAQG,EAAkB,GAAI,CAAC,EACzE,IAAIK,EAAK,KACLpC,EAAO,WACPoC,EAAK,IAAI,kBAAkBpC,EAAO,SAAU4B,EAAQ5B,EAAO,mBAAoBA,EAAO,kBAAkB,GAE5G,MAAMqC,EAAaT,GAAS,EAC5B,IAAIU,EAAK,IACLC,EAAK,EACLC,EAAK,EACLC,EAAK,EACT,MAAMC,EAAQ,GACd,QAAShB,EAAgB,EAAGA,EAAgB1B,EAAO,iBAAiB,OAAQ0B,IAAiB,CACzF,MAAMC,EAAW3B,EAAO,iBAAiB0B,CAAa,EACtD,IAAI1D,EACJ,OAAQ2D,EAAS,KAAI,CACjB,IAAK,GACD3D,EAAQmD,EAAS,WAAWI,EAAO,MAAQI,EAAS,OAAQ,EAAI,EAChE,MACJ,IAAK,GACD3D,EAAQmD,EAAS,SAASI,EAAO,MAAQI,EAAS,OAAQ,EAAI,EAC9D,MACJ,IAAK,GACD3D,EAAQmD,EAAS,UAAUI,EAAO,MAAQI,EAAS,OAAQ,EAAI,EAC/D,MACJ,IAAK,GACD3D,EAAQmD,EAAS,WAAWI,EAAO,MAAQI,EAAS,OAAQ,EAAI,EAChE,MACJ,IAAK,GACD3D,EAAQmD,EAAS,SAASI,EAAO,MAAQI,EAAS,MAAM,EACxD,MACJ,QACI,QACP,CACD,OAAQA,EAAS,MAAK,CAClB,IAAK,IACD,CACI,MAAMgB,EAAkBnB,EAAiBa,CAAU,EACnDnE,GAAaF,EAAO8D,CAAK,EACzBE,EAAS,CAAC,EAAInE,EAAO,KAAK8E,EAAgB,IAAI,EAAGA,EAAgB,IAAI,EAAGb,EAAM,CAAC,EAC/EE,EAAS,CAAC,EAAI,CAACnE,EAAO,KAAK8E,EAAgB,IAAI,EAAGA,EAAgB,IAAI,EAAGb,EAAM,CAAC,EAChFE,EAAS,CAAC,EAAInE,EAAO,KAAK8E,EAAgB,IAAI,EAAGA,EAAgB,IAAI,EAAGb,EAAM,CAAC,CAClF,CACD,MACJ,IAAK,IAEGzD,GAAUL,EAAO6D,CAAC,EAClBS,EAAKT,EAAE,EACPU,EAAKV,EAAE,EACPW,EAAKX,EAAE,EACPY,EAAKZ,EAAE,EAEX,MACJ,IAAK,IACD,CACI,MAAMc,EAAkBnB,EAAiBa,CAAU,EACnDnE,GAAaF,EAAO8D,CAAK,EACzBG,EAAM,CAAC,EAAI,KAAK,IAAIpE,EAAO,KAAK8E,EAAgB,SAAS,EAAGA,EAAgB,SAAS,EAAGb,EAAM,CAAC,CAAC,EAChGG,EAAM,CAAC,EAAI,KAAK,IAAIpE,EAAO,KAAK8E,EAAgB,SAAS,EAAGA,EAAgB,SAAS,EAAGb,EAAM,CAAC,CAAC,EAChGG,EAAM,CAAC,EAAI,KAAK,IAAIpE,EAAO,KAAK8E,EAAgB,SAAS,EAAGA,EAAgB,SAAS,EAAGb,EAAM,CAAC,CAAC,CACnG,CACD,MACJ,IAAK,IACD,CACI,MAAMa,EAAkBnB,EAAiBa,CAAU,EACnDjE,GAAWJ,EAAOkE,CAAI,EACtBA,EAAK,CAAC,EAAIrE,EAAO,KAAK8E,EAAgB,SAAS,EAAGA,EAAgB,SAAS,EAAGT,EAAK,CAAC,EAAI,GAAG,EAAI,IAC/FA,EAAK,CAAC,EAAIrE,EAAO,KAAK8E,EAAgB,SAAS,EAAGA,EAAgB,SAAS,EAAGT,EAAK,CAAC,EAAI,GAAG,EAAI,IAC/FA,EAAK,CAAC,EAAIrE,EAAO,KAAK8E,EAAgB,SAAS,EAAGA,EAAgB,SAAS,EAAGT,EAAK,CAAC,EAAI,GAAG,EAAI,GAClG,CACD,MACJ,IAAK,IACDF,EAAS,CAAC,EAAIhE,EACd,MACJ,IAAK,IACDgE,EAAS,CAAC,EAAI,CAAChE,EACf,MACJ,IAAK,IACDgE,EAAS,CAAC,EAAI,CAAChE,EACf,MACJ,IAAK,IACDiE,EAAM,CAAC,EAAI,KAAK,IAAIjE,CAAK,EACzB,MACJ,IAAK,IACDiE,EAAM,CAAC,EAAI,KAAK,IAAIjE,CAAK,EACzB,MACJ,IAAK,IACDiE,EAAM,CAAC,EAAI,KAAK,IAAIjE,CAAK,EACzB,MACJ,IAAK,IACDkE,EAAK,CAAC,EAAIlE,EACV,MACJ,IAAK,IACDkE,EAAK,CAAC,EAAIlE,EACV,MACJ,IAAK,IACDkE,EAAK,CAAC,EAAIlE,EACV,MACJ,IAAK,IACDkE,EAAK,CAAC,GAAK,GAAMrD,EAAsB,OAASb,GAAS,IACzD,MACJ,IAAK,IACDkE,EAAK,CAAC,GAAK,GAAMrD,EAAsB,OAASb,GAAS,IACzD,MACJ,IAAK,IACDkE,EAAK,CAAC,GAAK,GAAMrD,EAAsB,OAASb,GAAS,IACzD,MACJ,IAAK,IACDkE,EAAK,CAAC,GAAK,GAAMrD,EAAsB,OAASb,GAAS,IACzD,MACJ,IAAK,IACDkE,EAAK,CAAC,EAAK,GAAK,EAAI,KAAK,IAAI,CAAClE,CAAK,GAAM,IACzC,MACJ,IAAK,IACDsE,EAAKtE,EACL,MACJ,IAAK,IACDuE,EAAKvE,EACL,MACJ,IAAK,IACDwE,EAAKxE,EACL,MACJ,IAAK,IACDyE,EAAKzE,EACL,KACP,CACD,GAAIoE,GAAMT,EAAS,OAAS,IAA0BA,EAAS,OAAS,GAAyB,CAC7F,MAAMiB,EAAe/E,EAAO,MAAMG,EAAQ,MAAQ,MAAO,EAAG,GAAG,EACzD6E,EAAUlB,EAAS,MAAQ,GACjCe,EAAMG,CAAO,EAAID,CACpB,CACJ,CACD,GAAIR,EAAI,CACJ,MAAMU,EAAQ9C,EAAO,UAAY,EAAI,EAAIA,EAAO,UAAY,EAAI,EAAI,GACpE,QAAS+C,EAAI,EAAGA,EAAID,EAAOC,IACvBX,EAAGW,EAAI,EAAI,CAAC,EAAIL,EAAMK,CAAC,EACvBX,EAAGW,EAAI,EAAI,CAAC,EAAIL,EAAMK,EAAID,CAAK,EAC/BV,EAAGW,EAAI,EAAI,CAAC,EAAIL,EAAMK,EAAID,EAAQ,CAAC,CAE1C,CACDjB,EAAE,IAAIU,EAAIC,EAAIC,EAAIH,CAAE,EACpBT,EAAE,UAAS,EACXM,EAAI,CAAC,EAAIN,EAAE,EAAI,IAAM,IACrBM,EAAI,CAAC,EAAIN,EAAE,EAAI,IAAM,IACrBM,EAAI,CAAC,EAAIN,EAAE,EAAI,IAAM,IACrBM,EAAI,CAAC,EAAIN,EAAE,EAAI,IAAM,IACrBN,EAAO,OAASvB,EAAO,eAC1B,CAQD,OAAQ,wBAAwBF,EAAMkD,EAAe,GAAO,CACxD,MAAMhD,EAASnB,EAAsB,YAAYiB,CAAI,EACrD,GAAI,CAACE,EACD,MAAO,CAAE,OAAQF,GAErB,MAAMyB,EAAS,CAAE,MAAO,GAClBC,EAAmB3C,EAAsB,qBAAqBmB,EAAQuB,CAAM,EAClF,QAAStE,EAAI,EAAGA,EAAI+C,EAAO,YAAa/C,IACpC4B,EAAsB,UAAUmB,EAAQ/C,EAAGuE,EAAkBD,CAAM,EAC/DtE,EAAI4B,EAAsB,0BAA4B,GAAKmE,IAC3D,OAGR,IAAIZ,EAAK,KAET,GAAIpC,EAAO,UAAYA,EAAO,SAAU,CACpC,MAAMiD,EAAe,KAAK,KAAKjD,EAAO,mBAAqB,EAAE,EAC7D,IAAIkD,EAAc,EAClB,MAAMnD,EAAO,IAAI,WAAWC,EAAO,QAAQ,EAE3CoC,EAAK,GACL,MAAMe,EAAanD,EAAO,YACpBzE,EAAS6H,GAAY,kBAC3B,GAAI7H,EAAQ,CACR,MAAM8H,EAAQ9H,EAAO,QAAO,EAAG,eACzB+H,EAAS,KAAK,KAAKH,EAAaE,CAAK,EAE3C,QAASE,EAAe,EAAGA,EAAeN,EAAcM,IAAgB,CACpE,MAAMC,EAAU,IAAI,WAAWF,EAASD,EAAQ,EAAI,CAAC,EACrDjB,EAAG,KAAKoB,CAAO,CAClB,CACD,QAASvG,EAAI,EAAGA,EAAIkG,EAAYlG,IAC5B,QAASwG,EAAe,EAAGA,EAAezD,EAAO,mBAAoByD,IAAgB,CACjF,MAAMC,EAAU3D,EAAKmD,GAAa,EAC5BK,EAAe,KAAK,MAAME,EAAe,EAAE,EAC3CE,EAAUvB,EAAGmB,CAAY,EACzBK,EAAqBH,EAAe,GACpCI,EAAiB5G,EAAI,GAC3B0G,EAAQC,EAAqBC,CAAc,EAAIH,CAClD,CAER,CACJ,CACD,MAAO,CAAE,OAAQ1D,EAAO,OAAQ,GAAIoC,CAAE,CACzC,CAQD,OAAQ,kBAAkBtC,EAAMkD,EAAe,GAAO,CAClD,MAAMhD,EAASnB,EAAsB,YAAYiB,CAAI,EACrD,GAAI,CAACE,EACD,OAAOF,EAEX,MAAMyB,EAAS,CAAE,MAAO,GAClBC,EAAmB3C,EAAsB,qBAAqBmB,EAAQuB,CAAM,EAClF,QAAStE,EAAI,EAAGA,EAAI+C,EAAO,YAAa/C,IACpC4B,EAAsB,UAAUmB,EAAQ/C,EAAGuE,EAAkBD,CAAM,EAC/DtE,EAAI4B,EAAsB,0BAA4B,GAAKmE,IAC3D,OAGR,OAAOhD,EAAO,MACjB,CAOD,aAAa,uBAAuBF,EAAM,CACtC,OAAOgE,GAAkBjF,EAAsB,kBAAkBiB,EAAM,EAAI,EAAGiE,GAAuB,CAAE,CAC1G,CAOD,aAAa,6BAA6BjE,EAAM,CAC5C,OAAOgE,GAAkBjF,EAAsB,wBAAwBiB,EAAM,EAAI,EAAGiE,GAAuB,CAAE,CAChH,CAMD,cAAcjE,EAAM,CAChB,OAAO,KAAK,gBAAgBA,CAAI,CACnC,CAOD,cAAcf,EAAK,CACf,OAAOiF,GAAM,cAAcjF,EAAK,EAAI,EAAE,KAAK,MAAOkF,GAAc,CAC5DpF,EAAsB,6BAA6BoF,CAAS,EAAE,KAAMC,GAAe,CAC/E,KAAK,gBAAgBA,EAAW,OAAQA,EAAW,EAAE,CACrE,CAAa,CACb,CAAS,CACJ,CAKD,QAAQC,EAAc,CAClB,KAAK,sBAAsB,UAC3B,KAAK,sBAAsB,UAC3B,KAAK,iBAAiB,UACtB,KAAK,gBAAgB,UACjB,KAAK,aACL,KAAK,YAAY,QAASC,GAAc,CACpCA,EAAU,QAAO,CACjC,CAAa,EAEL,KAAK,qBAAuB,KAC5B,KAAK,qBAAuB,KAC5B,KAAK,gBAAkB,KACvB,KAAK,eAAiB,KACtB,KAAK,YAAc,KACnB,KAAK,SAAS,YACd,KAAK,QAAU,KACf,MAAM,QAAQD,EAAc,EAAI,CACnC,CACD,cAAc3G,EAAQ,CAClB,KAAK,qBAAuBA,EAAO,qBAAqB,MAAK,EAC7D,KAAK,qBAAuBA,EAAO,qBAAqB,MAAK,EAC7D,KAAK,gBAAkBA,EAAO,gBAAgB,MAAK,EACnD,KAAK,eAAiBA,EAAO,eAAe,MAAK,EAC7CA,EAAO,cACP,KAAK,YAAc,GACnB,KAAK,YAAY,QAAS4G,GAAc,CACpC,KAAK,aAAa,KAAKA,EAAU,MAAO,EACxD,CAAa,EAER,CAMD,MAAM7J,EAAO,GAAI,CACb,MAAM8J,EAAQ,IAAIxF,EAAsBtE,EAAM,OAAW,KAAK,SAAQ,CAAE,EACxE8J,EAAM,YAAY,IAAI,EACtBA,EAAM,mBAAkB,EACxBA,EAAM,aAAe,KAAK,aAC1BA,EAAM,cAAc,IAAI,EACxBA,EAAM,iBAAmBpF,GAAO,WAChCoF,EAAM,gBAAkB,KAAK,gBAC7BA,EAAM,gBAAkB,GACxBA,EAAM,mBAAkB,EACxB,MAAMC,EAAQ,KAAK,kBACnB,OAAAD,EAAM,gBAAe,EAAG,YAAYC,EAAM,QAASA,EAAM,QAAS,KAAK,eAAc,CAAE,EACvFD,EAAM,oBAAsBA,EAAM,aAClCA,EAAM,WAAW,EAAI,EACdA,CACV,CACD,WAAWzC,EAAO2C,EAASC,EAASC,EAAMC,EAAMC,EAAYC,EAASC,EAAS,CAC1E,MAAMC,EAAiBpF,EAAW,OAAO,CAAC,EACpCqF,EAAcrF,EAAW,OAAO,CAAC,EACjCsF,EAAatF,EAAW,WAAW,CAAC,EACpCuF,EAAgB,KAAK,mBAAqB,EAAI,EAC9C,EAAIV,EAAQ,EAAI3C,EAAQ,CAAC,EACzBsD,EAAI,CAACX,EAAQ,EAAI3C,EAAQ,CAAC,EAC1BuD,EAAIZ,EAAQ,EAAI3C,EAAQ,CAAC,EAC/B,KAAK,gBAAgB,EAAIA,EAAQ,CAAC,EAAI,EACtC,KAAK,gBAAgB,EAAIA,EAAQ,CAAC,EAAIsD,EACtC,KAAK,gBAAgB,EAAItD,EAAQ,CAAC,EAAIuD,EACtCP,EAAQ,0BAA0B,EAAGM,EAAGC,CAAC,EACzCN,EAAQ,0BAA0B,EAAGK,EAAGC,CAAC,EACzCH,EAAW,KAAKR,EAAQ,GAAK5C,EAAQ,GAAK,CAAC,EAAI,OAAS,OAAQ4C,EAAQ,GAAK5C,EAAQ,GAAK,CAAC,EAAI,OAAS,OAAQ4C,EAAQ,GAAK5C,EAAQ,GAAK,CAAC,EAAI,OAAS,MAAO,EAAE4C,EAAQ,GAAK5C,EAAQ,GAAK,CAAC,EAAI,OAAS,KAAK,EAC9MoD,EAAW,iBAAiBF,CAAc,EAC1C7F,GAAO,aAAasF,EAAQ,EAAI3C,EAAQ,EAAI,CAAC,EAAI,EAAG2C,EAAQ,EAAI3C,EAAQ,EAAI,CAAC,EAAI,EAAG2C,EAAQ,EAAI3C,EAAQ,EAAI,CAAC,EAAI,EAAGmD,CAAW,EAC/H,MAAMK,EAAIN,EAAe,cAAcC,EAAarF,EAAW,OAAO,CAAC,CAAC,EAAE,EACpE2F,EAAc,KAAK,gBACzBA,EAAY,CAAC,EAAID,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EACvDC,EAAY,CAAC,EAAID,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EACvDC,EAAY,CAAC,EAAID,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,EAAE,EACxDC,EAAY,CAAC,EAAID,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EACvDC,EAAY,CAAC,EAAID,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,EAAE,EACxDC,EAAY,CAAC,EAAID,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAIA,EAAE,EAAE,EAAIA,EAAE,EAAE,EAEzD,IAAIE,EAAS,KACb,QAASC,EAAW,EAAGA,EAAW,EAAGA,IACjCD,EAAS,KAAK,IAAIA,EAAQ,KAAK,IAAID,EAAYE,CAAQ,CAAC,CAAC,EAE7D,KAAK,gBAAgB,EAAI3D,EAAQ,CAAC,EAAI0D,EACtC,MAAME,EAAYF,EAClBb,EAAK7C,EAAQ,EAAI,CAAC,EAAI6D,GAAYJ,EAAY,CAAC,EAAIG,CAAS,EAC5Df,EAAK7C,EAAQ,EAAI,CAAC,EAAI6D,GAAYJ,EAAY,CAAC,EAAIG,CAAS,EAC5Df,EAAK7C,EAAQ,EAAI,CAAC,EAAI6D,GAAYJ,EAAY,CAAC,EAAIG,CAAS,EAC5Df,EAAK7C,EAAQ,EAAI,CAAC,EAAI6D,GAAYJ,EAAY,CAAC,EAAIG,CAAS,EAC5Dd,EAAK9C,EAAQqD,EAAgB,CAAC,EAAIQ,GAAYJ,EAAY,CAAC,EAAIG,CAAS,EACxEd,EAAK9C,EAAQqD,EAAgB,CAAC,EAAIQ,GAAYJ,EAAY,CAAC,EAAIG,CAAS,EAExEb,EAAW/C,EAAQ,EAAI,CAAC,EAAI4C,EAAQ,GAAK5C,EAAQ,GAAK,CAAC,EACvD+C,EAAW/C,EAAQ,EAAI,CAAC,EAAI4C,EAAQ,GAAK5C,EAAQ,GAAK,CAAC,EACvD+C,EAAW/C,EAAQ,EAAI,CAAC,EAAI4C,EAAQ,GAAK5C,EAAQ,GAAK,CAAC,EACvD+C,EAAW/C,EAAQ,EAAI,CAAC,EAAI4C,EAAQ,GAAK5C,EAAQ,GAAK,CAAC,CAC1D,CACD,gBAAgB6C,EAAMC,EAAMC,EAAYvC,EAAI,CACxC,MAAMpF,EAAc,KAAK,gBAAgB,KAAK,YAAY,EAEpD0I,EAAwB,CAAC5F,EAAMuD,EAAOC,EAAQqC,IACzC,IAAIC,GAAW9F,EAAMuD,EAAOC,EAAQqC,EAAQ,KAAK,OAAQ,GAAO,GAAO,EAAG,CAAC,EAEhFE,EAA0B,CAAC/F,EAAMuD,EAAOC,EAAQqC,IAC3C,IAAIC,GAAW9F,EAAMuD,EAAOC,EAAQqC,EAAQ,KAAK,OAAQ,GAAO,GAAO,EAAG,CAAC,EAEhFG,EAA2B,CAAChG,EAAMuD,EAAOC,EAAQqC,IAC5C,IAAIC,GAAW9F,EAAMuD,EAAOC,EAAQqC,EAAQ,KAAK,OAAQ,GAAO,GAAO,EAAG,CAAC,EAEhFI,EAA2B,CAACjG,EAAMuD,EAAOC,EAAQqC,IAC5C,IAAIC,GAAW9F,EAAMuD,EAAOC,EAAQqC,EAAQ,KAAK,OAAQ,GAAO,GAAO,EAAG,CAAC,EAEtF,GAAI,KAAK,qBAAsB,CAC3B,KAAK,sBAAwB,CAAE,KAAMlB,EAAM,KAAMC,EAAM,OAAQC,EAAY,QAAS,KAAK,gBAAiB,GAAIvC,CAAE,EAChH,MAAM4D,EAAY,aAAa,KAAK,KAAK,eAAe,EAClD7F,EAAc,KAAK,aACzB,KAAK,QAAQ,YAAY,CAAE,UAAA6F,EAAW,YAAA7F,CAAW,EAAI,CAAC6F,EAAU,MAAM,CAAC,EACvE,KAAK,cAAc,EAAI,CAC1B,MAEG,KAAK,qBAAuBD,EAAyBtB,EAAMzH,EAAY,EAAGA,EAAY,EAAG,CAAC,EAC1F,KAAK,qBAAuB+I,EAAyBrB,EAAM1H,EAAY,EAAGA,EAAY,EAAG,KAAK,mBAAqB,EAAI,CAAC,EACxH,KAAK,gBAAkB0I,EAAsB,KAAK,gBAAiB1I,EAAY,EAAGA,EAAY,EAAG,CAAC,EAClG,KAAK,eAAiB6I,EAAwBlB,EAAY3H,EAAY,EAAGA,EAAY,EAAG,CAAC,EACrFoF,IACA,KAAK,YAAc,GACnBA,EAAG,QAAS6D,GAAW,CACnB,MAAM7E,EAAS,IAAI,YAAY6E,EAAO,MAAM,EACtC7B,EAAY0B,EAAyB1E,EAAQpE,EAAY,EAAGA,EAAY,EAAG,EAAE,EACnFoH,EAAU,MAAQ,EAClBA,EAAU,MAAQ,EAClB,KAAK,YAAY,KAAKA,CAAS,CACnD,CAAiB,GAEL,KAAK,mBAAkB,CAE9B,CACD,CAAC,YAAYtE,EAAMoG,EAAS9D,EAAI,CAEvB,KAAK,uBACN,KAAK,gBAAkB,IAG3B,MAAMoC,EAAU,IAAI,WAAW1E,CAAI,EAC7ByE,EAAU,IAAI,aAAaC,EAAQ,MAAM,EAC3C,KAAK,aACL,KAAK,YAAc1E,EACfsC,IACA,KAAK,IAAMA,IAGnB,MAAMjC,EAAcqE,EAAQ,OAAS3F,EAAsB,iBACvDsB,GAAe,KAAK,cACpB,KAAK,wBAAwBA,CAAW,EAE5C,KAAK,aAAeA,EAEpB,KAAK,UAAYiC,EAAKA,EAAG,OAAS,EAClC,MAAMpF,EAAc,KAAK,gBAAgBmD,CAAW,EAC9CgG,EAAgBnJ,EAAY,EAAIA,EAAY,EAC5CoJ,EAAkBvH,EAAsB,yBAA2B7B,EAAY,EAC/EqJ,EAAyBrJ,EAAY,EAAIoJ,EAC/C,KAAK,gBAAkB,IAAI,aAAa,EAAID,CAAa,EACzD,MAAM1B,EAAO,IAAI,YAAY0B,EAAgB,CAAC,EACxCzB,EAAO,IAAI,aAAa,KAAK,mBAAqB,EAAI,GAAKyB,CAAa,EACxExB,EAAa,IAAI,WAAWwB,EAAgB,CAAC,EAC7CvB,EAAU,IAAI1F,EAAQ,OAAO,UAAW,OAAO,UAAW,OAAO,SAAS,EAC1E2F,EAAU,IAAI3F,EAAQ,CAAC,OAAO,UAAW,CAAC,OAAO,UAAW,CAAC,OAAO,SAAS,EACnF,GAAIL,EAAsB,wBAAyB,CAE/C,KAAK,gBAAgB4F,EAAMC,EAAMC,EAAYvC,CAAE,EAC/C,KAAK,WAAW,EAAI,EACpB,MAAMkE,EAAY,KAAK,KAAKtJ,EAAY,EAAIoJ,CAAe,EAC3D,QAASG,EAAY,EAAGA,EAAYD,EAAWC,IAAa,CACxD,MAAMC,EAAaD,EAAYH,EACzBK,EAAiBD,EAAaxJ,EAAY,EAChD,QAASC,EAAI,EAAGA,EAAIoJ,EAAwBpJ,IACxC,KAAK,WAAWwJ,EAAiBxJ,EAAGsH,EAASC,EAASC,EAAMC,EAAMC,EAAYC,EAASC,CAAO,EAElG,KAAK,mBAAmB,KAAK,gBAAiBJ,EAAMC,EAAMC,EAAY6B,EAAY,KAAK,IAAIJ,EAAiBpJ,EAAY,EAAIwJ,CAAU,CAAC,EAEvI,KAAK,gBAAiB,EAAC,YAAY5B,EAASC,EAAS,KAAK,eAAc,CAAE,EACtEqB,IACA,MAEP,CAED,MAAMF,EAAY,aAAa,KAAK,KAAK,eAAe,EAClD7F,EAAc,KAAK,aACzB,KAAK,QAAQ,YAAY,CAAE,UAAA6F,EAAW,YAAA7F,CAAW,EAAI,CAAC6F,EAAU,MAAM,CAAC,EACvE,KAAK,aAAe,EACvB,KACI,CACD,QAAS/I,EAAI,EAAGA,EAAIkD,EAAalD,IAC7B,KAAK,WAAWA,EAAGsH,EAASC,EAASC,EAAMC,EAAMC,EAAYC,EAASC,CAAO,EACzEqB,GAAWjJ,EAAI4B,EAAsB,kBAAoB,IACzD,OAIR,KAAK,gBAAgB4F,EAAMC,EAAMC,EAAYvC,CAAE,EAE/C,KAAK,gBAAiB,EAAC,YAAYwC,EAASC,EAAS,KAAK,eAAc,CAAE,EAC1E,KAAK,WAAW,EAAI,CACvB,CACD,KAAK,cAAc,EAAI,CAC1B,CAOD,MAAM,gBAAgB/E,EAAMsC,EAAI,CAC5B,OAAO0B,GAAkB,KAAK,YAAYhE,EAAM,GAAMsC,CAAE,EAAG2B,GAAuB,CAAE,CACvF,CAOD,WAAWjE,EAAMsC,EAAI,CACjBsE,GAAiB,KAAK,YAAY5G,EAAM,GAAOsC,CAAE,CAAC,CACrD,CAKD,qBAAsB,CAClB,YAAK,gCAAgC,EAAK,EACnC,IACV,CAED,wBAAwBjC,EAAa,EAC7B,CAAC,KAAK,aAAeA,EAAc,KAAK,YAAY,UACpD,KAAK,YAAc,IAAI,aAAaA,CAAW,EAC/C,KAAK,sBAAsB,aAAc,KAAK,YAAa,EAAG,EAAK,GAEvE,KAAK,oBAAsBA,CAC9B,CACD,mBAAmBwG,EAASlC,EAAMC,EAAMkC,EAAQC,EAAWC,EAAW1E,EAAI,CACtE,MAAM2E,EAAwB,CAACvD,EAAS1D,EAAMuD,EAAOwD,EAAWC,IAAc,CAC1E,KAAK,UAAS,EAAG,kBAAkBtD,EAAQ,mBAAoB,EAAE1D,EAAM,EAAG+G,EAAWxD,EAAOyD,EAAW,EAAG,EAAG,EAAK,CAC9H,EACc9J,EAAc,KAAK,gBAAgB,KAAK,YAAY,EACpDiI,EAAgB,KAAK,mBAAqB,EAAI,EAC9C+B,EAAaH,EAAY7J,EAAY,EACrCiK,EAAaH,EAAY9J,EAAY,EACrCkK,EAAW,IAAI,YAAYzC,EAAK,OAAQuC,EAAa,EAAI,YAAY,kBAAmBC,EAAa,CAAC,EACtGE,EAAW,IAAI,YAAYzC,EAAK,OAAQsC,EAAa/B,EAAgB,YAAY,kBAAmBgC,EAAahC,CAAa,EAC9HmC,EAAa,IAAI,WAAWR,EAAO,OAAQI,EAAa,EAAGC,EAAa,CAAC,EACzEI,EAAc,IAAI,aAAaV,EAAQ,OAAQK,EAAa,EAAI,aAAa,kBAAmBC,EAAa,CAAC,EAKpH,GAJAF,EAAsB,KAAK,qBAAsBG,EAAUlK,EAAY,EAAG6J,EAAWC,CAAS,EAC9FC,EAAsB,KAAK,qBAAsBI,EAAUnK,EAAY,EAAG6J,EAAWC,CAAS,EAC9FC,EAAsB,KAAK,gBAAiBM,EAAarK,EAAY,EAAG6J,EAAWC,CAAS,EAC5FC,EAAsB,KAAK,eAAgBK,EAAYpK,EAAY,EAAG6J,EAAWC,CAAS,EACtF1E,EACA,QAASnF,EAAI,EAAGA,EAAImF,EAAG,OAAQnF,IAAK,CAEhC,MAAMqK,EAAS,IAAI,WAAW,KAAK,IAAIrK,CAAC,EAAE,OAAQ+J,EAAa,EAAgBC,EAAa,CAAc,EAC1GF,EAAsB,KAAK,YAAY9J,CAAC,EAAGqK,EAAQtK,EAAY,EAAG6J,EAAWC,CAAS,CACzF,CAER,CACD,oBAAqB,CACjB,GAAI,CAAC,KAAK,aACN,OAEJ,KAAK,wBAAwB,KAAK,YAAY,EAE9C,KAAK,SAAS,YACd,KAAK,QAAU,IAAI,OAAO,IAAI,gBAAgB,IAAI,KAAK,CAAC,IAAKjI,EAAsB,cAAc,SAAU,EAAE,SAAS,EAAG,CACrH,KAAM,wBACT,EAAC,CAAC,EACH,KAAK,UAAY,IAAI,cAAc,KAAK,YAAY,EACpD,MAAMmH,EAAY,aAAa,KAAK,KAAK,eAAe,EAClD7F,EAAc,KAAK,aACzB,KAAK,QAAQ,YAAY,CAAE,UAAA6F,EAAW,YAAA7F,CAAW,EAAI,CAAC6F,EAAU,MAAM,CAAC,EACvE,KAAK,QAAQ,UAAauB,GAAM,CAC5B,KAAK,UAAYA,EAAE,KAAK,SACxB,MAAMC,EAAW,IAAI,YAAYD,EAAE,KAAK,SAAS,MAAM,EACvD,GAAI,KAAK,YACL,QAASxE,EAAI,EAAGA,EAAI,KAAK,aAAcA,IACnC,KAAK,YAAYA,CAAC,EAAIyE,EAAS,EAAIzE,CAAC,EAG5C,GAAI,KAAK,sBAAuB,CAC5B,MAAM/F,EAAc,KAAK,gBAAgBmD,CAAW,EACpD,KAAK,mBAAmB,KAAK,sBAAsB,QAAS,KAAK,sBAAsB,KAAM,KAAK,sBAAsB,KAAM,KAAK,sBAAsB,OAAQ,EAAGnD,EAAY,EAAG,KAAK,sBAAsB,EAAE,EAChN,KAAK,sBAAwB,IAChC,CACD,KAAK,0BAA0B,YAAY,EAC3C,KAAK,iBAAmB,GACxB,KAAK,gBAAkB,GAGnB,KAAK,eACL,KAAK,cAAc,EAAI,EACvB,KAAK,aAAe,GAEpC,CACK,CACD,gBAAgByK,EAAQ,CACpB,MAAMlM,EAAS,KAAK,OAAO,UAAS,EAC9B8H,EAAQ9H,EAAO,QAAO,EAAG,eAC/B,IAAI+H,EAAS,EACb,GAAI/H,EAAO,UAAY,GAAK,CAACA,EAAO,SAChC,KAAO8H,EAAQC,EAASmE,GACpBnE,GAAU,OAIdA,EAAS,KAAK,KAAKmE,EAASpE,CAAK,EAErC,OAAIC,EAASD,IACTnC,GAAO,MAAM,oCAAsCmC,EAAQ,KAAOC,EAAS,sBAAwBD,CAAK,EACxGC,EAASD,GAEN,IAAIqE,EAAQrE,EAAOC,CAAM,CACnC,CACL,CACAzE,EAAsB,iBAAmB,EAAI,EAAI,EAAI,EAAI,EAAI,EAC7DA,EAAsB,OAAS,mBAG/BA,EAAsB,gBAAkB,OAExCA,EAAsB,wBAA0B,MAKhDA,EAAsB,wBAA0B,EAChDA,EAAsB,cAAgB,SAAU8I,EAAM,CAClD,IAAIxH,EAAc,EACd6F,EACA4B,EACAC,EACAC,EACJH,EAAK,UAAaJ,GAAM,CAEpB,GAAIA,EAAE,KAAK,UACPvB,EAAYuB,EAAE,KAAK,UACnBpH,EAAcoH,EAAE,KAAK,gBAGpB,CACD,MAAMQ,EAAWR,EAAE,KAAK,KACxB,GAAI,CAACvB,GAAa,CAAC+B,EAEf,MAAM,IAAI,MAAM,mCAAmC,EAEvDH,EAAWL,EAAE,KAAK,SAClBM,EAAU,IAAI,YAAYD,EAAS,MAAM,EACzCE,EAAW,IAAI,aAAaF,EAAS,MAAM,EAE3C,QAAS7E,EAAI,EAAGA,EAAI5C,EAAa4C,IAC7B8E,EAAQ,EAAI9E,CAAC,EAAIA,EAErB,IAAIiF,EAAc,GACdT,EAAE,KAAK,uBACPS,EAAc,GAElB,QAASjF,EAAI,EAAGA,EAAI5C,EAAa4C,IAC7B+E,EAAS,EAAI/E,EAAI,CAAC,EAAI,KAASgF,EAAS,CAAC,EAAI/B,EAAU,EAAIjD,EAAI,CAAC,EAAIgF,EAAS,CAAC,EAAI/B,EAAU,EAAIjD,EAAI,CAAC,EAAIgF,EAAS,EAAE,EAAI/B,EAAU,EAAIjD,EAAI,CAAC,GAAKiF,EAEpJJ,EAAS,KAAI,EACbD,EAAK,YAAY,CAAE,SAAAC,CAAQ,EAAI,CAACA,EAAS,MAAM,CAAC,CACnD,CACT,CACA,EC70CO,MAAMK,EAAW,CAUpB,YAAYC,EAAeC,EAAOC,EAASC,EAAYC,EAAK,CAIxD,KAAK,IAAM,EAIX,KAAK,MAAQ,IAAIC,EAAO,EAAK,EAAK,EAAK,CAAG,EAI1C,KAAK,SAAWrJ,EAAQ,OAIxB,KAAK,SAAWA,EAAQ,OAIxB,KAAK,GAAK,IAAIwI,EAAQ,EAAK,CAAG,EAI9B,KAAK,SAAWxI,EAAQ,OAIxB,KAAK,MAAQA,EAAQ,OAMrB,KAAK,mBAAqB,GAK1B,KAAK,KAAO,EAIZ,KAAK,KAAO,EAIZ,KAAK,QAAU,EAIf,KAAK,WAAa,EAIlB,KAAK,gBAAkB,GAIvB,KAAK,gBAAkB,CAAC,EAAK,EAAK,EAAK,EAAK,EAAK,EAAK,EAAK,EAAK,CAAG,EAKnE,KAAK,SAAW,KAIhB,KAAK,gBAAkBA,EAAQ,OAC/B,KAAK,IAAMgJ,EACX,KAAK,OAASC,EACd,KAAK,QAAUC,EACf,KAAK,WAAaC,EAClB,KAAK,KAAOC,CACf,CAID,IAAI,MAAO,CACP,OAAO,KAAK,IACf,CAID,IAAI,KAAKrG,EAAO,CACZ,KAAK,KAAOA,CACf,CAID,IAAI,YAAa,CACb,OAAO,KAAK,kBACf,CAID,IAAI,WAAWJ,EAAG,CACd,KAAK,mBAAqBA,CAC7B,CAQD,eAAe2G,EAAQC,EAAU,CAC7B,GAAI,CAACD,EAAO,gBACR,MAAO,GAEX,GAAI,CAAC,KAAK,KAAK,KACX,MAAM,IAAI,MAAM,4CAA4C,EAEhE,GAAIC,EACA,OAAOD,EAAO,gBAAe,EAAG,eAAe,gBAAgB,KAAK,SAAS,IAAI,KAAK,KAAK,KAAK,QAAQ,CAAC,EAE7G,MAAME,EAAOF,EAAO,gBAAe,EAAG,YAChCG,EAAOD,EAAK,aAAa,EACzBE,EAAOF,EAAK,aAAa,EACzBG,EAAOH,EAAK,aAAa,EACzBI,EAAOJ,EAAK,aAAa,EACzBK,EAAOL,EAAK,aAAa,EACzBM,EAAON,EAAK,aAAa,EACzBO,EAAI,KAAK,SAAS,EAAI,KAAK,KAAK,KAAK,SAAS,EAC9C/D,EAAI,KAAK,SAAS,EAAI,KAAK,KAAK,KAAK,SAAS,EAC9CC,EAAI,KAAK,SAAS,EAAI,KAAK,KAAK,KAAK,SAAS,EACpD,OAAOyD,GAAQK,GAAKA,GAAKN,GAAQG,GAAQ5D,GAAKA,GAAK2D,GAAQG,GAAQ7D,GAAKA,GAAK4D,CAChF,CAKD,kBAAkBrK,EAAG,CACjB,IAAIsG,EACJ,GAAI,KAAK,mBACLA,EAAa,KAAK,uBAEjB,CACDA,EAAatF,EAAW,WAAW,CAAC,EACpC,MAAMwJ,EAAW,KAAK,SACtBC,GAAW,0BAA0BD,EAAS,EAAGA,EAAS,EAAGA,EAAS,EAAGlE,CAAU,CACtF,CACDA,EAAW,iBAAiBtG,CAAC,CAChC,CACL,CAKO,MAAM0K,EAAY,CAKrB,IAAI,SAAU,CACV,OAAO,KAAK,OACf,CACD,IAAI,QAAQC,EAAS,CACjB,KAAK,QAAUA,CAClB,CAMD,YAAYC,EAAIC,EAAa,CACzB,KAAK,QAAUD,EACf,KAAK,kBAAoBC,CAC5B,CACL,CC1KO,IAAIC,IACV,SAAUA,EAAY,CAEnBA,EAAWA,EAAW,MAAW,CAAC,EAAI,QAEtCA,EAAWA,EAAW,GAAQ,CAAC,EAAI,KAEnCA,EAAWA,EAAW,OAAY,CAAC,EAAI,SAEvCA,EAAWA,EAAW,OAAY,CAAC,EAAI,QAC3C,GAAGA,KAAeA,GAAa,CAAE,EAAC,EAU3B,MAAMC,EAAkB,CAI3B,IAAI,WAAY,CACZ,OAAO,KAAK,YACf,CAID,IAAI,QAAS,CACT,OAAO,KAAK,SACf,CAID,IAAI,KAAM,CACN,OAAO,KAAK,MACf,CASD,YAAYlP,EAAMmP,EAAWxO,EAAOyO,EAAS,CAKzC,KAAK,UAAY,IAAI,MAIrB,KAAK,YAAc,EAInB,KAAK,QAAU,EAKf,KAAK,KAAO,GACZ,KAAK,UAAY,GACjB,KAAK,WAAa,IAAI,MACtB,KAAK,SAAW,IAAI,MACpB,KAAK,SAAW,IAAI,MACpB,KAAK,QAAU,IAAI,MACnB,KAAK,KAAO,IAAI,MAChB,KAAK,WAAa,GAClB,KAAK,uBAAyB,GAC9B,KAAK,eAAiB,GACtB,KAAK,QAAU,IAAI,MACnB,KAAK,cAAgB,EACrB,KAAK,sBAAwB,GAC7B,KAAK,wBAA0B,GAC/B,KAAK,yBAA2B,GAChC,KAAK,oBAAsB,GAC3B,KAAK,SAAW,GAChB,KAAK,KAAOpP,EACZ,KAAK,MAAQmP,EACb,KAAK,OAASxO,GAASkI,GAAY,iBAC/BuG,GAAWA,EAAQ,YAAc,OACjC,KAAK,WAAaA,EAAQ,UAG1B,KAAK,WAAa,EAEzB,CAOD,eAAeC,EAAU,CACrB,OAAO,QAAQ,IAAI,KAAK,SAAS,EAAE,KAAK,KACpC,KAAK,SAAW,GACT,KAAK,WAAWA,CAAQ,EAClC,CACJ,CAID,WAAWA,EAAU,CACb,KAAK,cAAgB,GACrB,KAAK,UAAU,CAAC,EAEpB,KAAK,aAAe,IAAI,aAAa,KAAK,UAAU,EACpD,KAAK,OAAS,IAAI,aAAa,KAAK,IAAI,EACxC,KAAK,UAAY,IAAI,aAAa,KAAK,OAAO,EAC9C,MAAMzK,EAAa,IAAIC,GACvBD,EAAW,IAAI,KAAK,aAAcvD,EAAa,YAAY,EACvD,KAAK,OAAO,OAAS,GACrBuD,EAAW,IAAI,KAAK,OAAQvD,EAAa,MAAM,EAEnD,IAAIiO,EAAK,EACL,KAAK,UAAU,OAAS,IACxBA,EAAK,EACL1K,EAAW,IAAI,KAAK,UAAWvD,EAAa,SAAS,GAEzD,MAAMT,EAAO,IAAI2D,GAAK,KAAK,KAAM,KAAK,MAAM,EAC5CK,EAAW,YAAYhE,EAAM,KAAK,UAAU,EAC5C,KAAK,KAAOA,EAEZ,KAAK,WAAa,KAClB,KAAK,KAAO,KACZ,KAAK,QAAU,KACV,KAAK,aACN,KAAK,UAAU,OAAS,GAE5B,IAAI2O,EAAMF,EACV,OAAKE,IACDA,EAAM,IAAIC,GAAiB,uBAAwB,KAAK,MAAM,EAC9DD,EAAI,cAAgB,IAAIE,GAAOH,EAAIA,EAAIA,CAAE,EACzCC,EAAI,gBAAkB,GACtBA,EAAI,YAAc,GAClBA,EAAI,UAAY,KAAK,OAEzB3O,EAAK,SAAW2O,EACT,IAAI,QAASG,GAAYA,EAAQ9O,CAAI,CAAC,CAChD,CAED,aAAa+O,EAAK/B,EAAOC,EAASC,EAAY,CAC1C,MAAM8B,EAAK,IAAIlC,GAAWiC,EAAK/B,EAAOC,EAASC,EAAY,IAAI,EAC/D,YAAK,UAAU,KAAK8B,CAAE,EACfA,CACV,CACD,kBAAkBC,EAAU,CACxBA,EAAS,SAAW,IAAIlL,EAAQ,KAAK,OAAM,EAAI,KAAK,OAAQ,EAAE,KAAK,OAAQ,GAC3EkL,EAAS,MAAQ,IAAI7B,EAAO,EAAG,EAAG,EAAG,CAAC,CACzC,CACD,yBAAyB8B,EAAapB,EAAG/D,EAAG7B,EAAO,CAC/C,MAAMiH,EAAYD,EAAY,gBACxBE,EAAQrF,GAAK7B,EAAQ,GAAK4F,EAAI,EAC9BuB,EAAe,CAACD,EAAOA,EAAQ,EAAGA,EAAQ,EAAGA,EAAQ,CAAC,EACtDE,EAAWD,EAAa,CAAC,EACzBE,EAAaF,EAAa,CAAC,EAC3BG,EAAYH,EAAa,CAAC,EAC1BI,EAAaJ,EAAa,CAAC,EAC3BK,EAAcP,EAAUG,CAAQ,EAChCK,EAAgBR,EAAUI,CAAU,EACpCK,EAAeT,EAAUK,CAAS,EAClCK,EAAgBV,EAAUM,CAAU,EAC1C,OAAO,IAAIrC,EAAOsC,EAAc,IAAKC,EAAgB,IAAKC,EAAe,IAAKC,CAAa,CAC9F,CACD,oBAAoB7P,EAAMkP,EAAaY,EAAUC,EAAkBC,EAAYZ,EAAOa,EAAOC,EAAY,CACrGA,EAAaA,GAAc,EACvBJ,GACA9P,EAAK,gBAAe,EAGxB,MAAMmQ,EAAW,EADCnQ,EAAK,kBACQ,eAAe,OAC9C,IAAIoQ,EAAUpQ,EAAK,gBAAgBS,EAAa,YAAY,EAC5D,MAAM4P,EAAUrQ,EAAK,aACfsQ,EAAStQ,EAAK,gBAAgBS,EAAa,QAAUyP,EAAaA,EAAa,EAAI,GAAG,EACtFK,EAAUvQ,EAAK,gBAAgBS,EAAa,SAAS,EACrD+P,EAAQzM,EAAQ,OACtB/D,EAAK,mBAAkB,EACvB,MAAMyQ,EAAazQ,EAAK,iBACxB,GAAI,CAACyQ,EAAW,aAAc,CAC1BL,EAAUA,EAAQ,MAAM,CAAC,EACzB,QAASM,EAAI,EAAGA,EAAIN,EAAQ,OAAS,EAAGM,IACpC3M,EAAQ,oCAAoCqM,EAAQ,EAAIM,CAAC,EAAGN,EAAQ,EAAIM,EAAI,CAAC,EAAGN,EAAQ,EAAIM,EAAI,CAAC,EAAGD,EAAYD,CAAK,EACrHJ,EAAQ,EAAIM,CAAC,EAAIF,EAAM,EACvBJ,EAAQ,EAAIM,EAAI,CAAC,EAAIF,EAAM,EAC3BJ,EAAQ,EAAIM,EAAI,CAAC,EAAIF,EAAM,CAElC,CACD,IAAIG,EAAY,EACZC,EAAM,EACNC,EAAM,EACNC,EAAM,EACNC,EAAM,EACNC,EAAM,EACNC,EAAM,EACNC,EAAM,EACNC,EAAM,EACNC,EAAM,EACNC,EAAM,EACNC,EAAM,EACNC,EAAM,EACV,MAAMC,EAAUzN,EAAQ,OAClB0N,EAAU1N,EAAQ,OAClB2N,EAAU3N,EAAQ,OAClB4N,EAAO5N,EAAQ,OACf6N,EAAO7N,EAAQ,OACrB,IAAI8N,EAAO,EACPC,EAAO,EACPC,EAAO,EACPC,GAAO,EACPC,GAAO,EACPC,GAAO,EACX,MAAMC,GAAM5F,EAAQ,OACd6F,EAAM7F,EAAQ,OACd8F,GAAM9F,EAAQ,OACd+F,GAAQ/F,EAAQ,OAChBgG,GAAQhG,EAAQ,OACtB,IAAIiG,GAAQ,EACRC,GAAQ,EACRC,GAAQ,EACRC,GAAQ,EACRC,GAAQ,EACRC,GAAQ,EACRC,GAAQ,EACRC,GAAQ,EACRC,GAAQ,EACRC,GAAQ,EACRC,GAAQ,EACRC,GAAQ,EACZ,MAAMC,GAAOC,GAAQ,OACfC,GAAOD,GAAQ,OACfE,GAAOF,GAAQ,OACfG,GAAUH,GAAQ,OAClBI,GAAUJ,GAAQ,OACxB,IAAIK,EAAQ,EACRC,GAAK,EACT1D,EAAQA,GAAgB,EACxB,IAAI2D,GACAC,GACAC,EAAW,IAAIT,GAAQ,EAAG,EAAG,EAAG,CAAC,EACjClQ,GAAOY,EAAQ,OACfgQ,GAAOhQ,EAAQ,OACfiQ,GAASjQ,EAAQ,OACjBkQ,GAAQ,EACRC,GAAgBnQ,EAAQ,OACxBoQ,GAAM,EACNC,GAAW,EACf,MAAMC,GAAM,IAAIC,GAAIvQ,EAAQ,KAAI,EAAI,IAAIA,EAAQ,EAAG,EAAG,CAAC,CAAC,EACxD,IAAIwQ,GACAC,GAAYzQ,EAAQ,OACxB,QAAS0C,EAAQ,EAAGA,EAAQ4J,EAAQ,OAAS,EAAG5J,IAAS,CACrDmK,EAAMP,EAAQ,EAAI5J,CAAK,EACvBoK,EAAMR,EAAQ,EAAI5J,EAAQ,CAAC,EAC3BqK,EAAMT,EAAQ,EAAI5J,EAAQ,CAAC,EAC3BsK,EAAMX,EAAQ,EAAIQ,CAAG,EACrBI,EAAMZ,EAAQ,EAAIQ,EAAM,CAAC,EACzBK,EAAMb,EAAQ,EAAIQ,EAAM,CAAC,EACzBM,EAAMd,EAAQ,EAAIS,CAAG,EACrBM,EAAMf,EAAQ,EAAIS,EAAM,CAAC,EACzBO,EAAMhB,EAAQ,EAAIS,EAAM,CAAC,EACzBQ,EAAMjB,EAAQ,EAAIU,CAAG,EACrBQ,EAAMlB,EAAQ,EAAIU,EAAM,CAAC,EACzBS,EAAMnB,EAAQ,EAAIU,EAAM,CAAC,EACzBU,EAAQ,IAAIT,EAAKC,EAAKC,CAAG,EACzBQ,EAAQ,IAAIP,EAAKC,EAAKC,CAAG,EACzBM,EAAQ,IAAIL,EAAKC,EAAKC,CAAG,EACzBE,EAAQ,cAAcD,EAASG,CAAI,EACnCD,EAAQ,cAAcD,EAASG,CAAI,EAC/BtB,IACAuB,EAAOvB,EAAO,EAAIM,CAAG,EACrBkB,EAAOxB,EAAO,EAAIM,EAAM,CAAC,EACzBmB,EAAOzB,EAAO,EAAIO,CAAG,EACrBmB,GAAO1B,EAAO,EAAIO,EAAM,CAAC,EACzBoB,GAAO3B,EAAO,EAAIQ,CAAG,EACrBoB,GAAO5B,EAAO,EAAIQ,EAAM,CAAC,EACzBqB,GAAI,IAAIN,EAAMC,CAAI,EAClBM,EAAI,IAAIL,EAAMC,EAAI,EAClBK,GAAI,IAAIJ,GAAMC,EAAI,EAClBE,EAAI,cAAcD,GAAKG,EAAK,EAC5BD,GAAI,cAAcD,EAAKG,EAAK,GAE5BhC,GAAWR,IACXyC,GAAQjC,EAAQ,EAAIK,CAAG,EACvB6B,GAAQlC,EAAQ,EAAIK,EAAM,CAAC,EAC3B8B,GAAQnC,EAAQ,EAAIK,EAAM,CAAC,EAC3B+B,GAAQpC,EAAQ,EAAIK,EAAM,CAAC,EAC3BgC,GAAQrC,EAAQ,EAAIM,CAAG,EACvBgC,GAAQtC,EAAQ,EAAIM,EAAM,CAAC,EAC3BiC,GAAQvC,EAAQ,EAAIM,EAAM,CAAC,EAC3BkC,GAAQxC,EAAQ,EAAIM,EAAM,CAAC,EAC3BmC,GAAQzC,EAAQ,EAAIO,CAAG,EACvBmC,GAAQ1C,EAAQ,EAAIO,EAAM,CAAC,EAC3BoC,GAAQ3C,EAAQ,EAAIO,EAAM,CAAC,EAC3BqC,GAAQ5C,EAAQ,EAAIO,EAAM,CAAC,EAC3BsC,GAAK,IAAIZ,GAAOC,GAAOC,GAAOC,EAAK,EACnCW,GAAK,IAAIV,GAAOC,GAAOC,GAAOC,EAAK,EACnCQ,GAAK,IAAIP,GAAOC,GAAOC,GAAOC,EAAK,EACnCG,GAAK,cAAcF,GAAMI,EAAO,EAChCD,GAAK,cAAcD,GAAMG,EAAO,GAEpC,IAAIvL,GACAC,GACAsM,GACAC,GACAC,GACAC,GACAC,GACAC,GACJ,MAAMC,GAAc,IAAIlG,GAAO,EAAG,EAAG,CAAC,EAChCmG,GAAY,IAAInG,GAAO,EAAG,EAAG,CAAC,EACpC,IAAIoG,GACAhG,EACJ,QAASnN,GAAI,EAAGA,GAAIoN,EAAY,cAAczI,CAAK,EAAG3E,KAClD6O,EAAY,KAAK,UAAU,OAC3B,KAAK,aAAaA,EAAWzB,EAAa,KAAK,cAAezI,EAAQ3E,EAAC,EACvEmN,EAAW,KAAK,UAAU0B,CAAS,EAEnC+C,EAAQ,KAAK,KAAKwB,EAAY,EAAG,CAAC,CAAC,EACnCvB,GAAKuB,EAAY,EAAG,CAAC,EACrBtB,GAAapC,EAAQ,IAAIG,EAAK,MAAM+B,CAAK,CAAC,EAAE,IAAI9B,EAAK,MAAM8B,EAAQC,EAAE,CAAC,EAClE7D,IACA3M,GAAOnD,EAAK,eAAeyG,CAAK,EAAE,YAAY,MAAM,EAAE,EACtDsN,GAAOpC,EAAK,MAAO,EAAC,UAAS,EAC7BqC,GAASjQ,EAAQ,MAAMZ,GAAM4Q,EAAI,EACjCE,GAAQiB,EAAY,EAAG,EAAI,KAAK,EAAE,EAClChB,GAAgBH,GAAK,MAAM,KAAK,IAAIE,EAAK,CAAC,EAAE,IAAID,GAAO,MAAM,KAAK,IAAIC,EAAK,CAAC,CAAC,EAC7EA,GAAQiB,EAAY,GAAK,KAAK,GAAK,CAAC,EACpCV,GAAYN,GAAc,MAAM,KAAK,IAAID,EAAK,CAAC,EAAE,IAAI9Q,GAAK,MAAM,KAAK,IAAI8Q,EAAK,CAAC,CAAC,EAChFI,GAAI,OAAST,GAAW,IAAIY,GAAU,MAAM,IAAO,CAAC,EACpDH,GAAI,UAAYG,GAChBH,GAAI,OAASlE,EACboE,GAAWF,GAAI,eAAerU,CAAI,EAC9BuU,GAAS,MACTH,GAAWG,GAAS,YAAY,SAASX,EAAU,EAAE,SACrDO,GAAMe,EAAY,EAAG,CAAC,EAAId,GAC1BR,GAAW,WAAWY,GAAU,MAAML,EAAG,CAAC,IAGlDlF,EAAS,SAAW2E,GAAW,QAC/B,KAAK,WAAW,KAAK3E,EAAS,SAAS,EAAGA,EAAS,SAAS,EAAGA,EAAS,SAAS,CAAC,EAC9Ec,IAAqB,OACjBO,IACAuD,GAAU1B,GAAI,IAAIG,GAAM,MAAMoB,CAAK,CAAC,EAAE,IAAInB,GAAM,MAAMmB,EAAQC,EAAE,CAAC,EAC7D5D,EAEIC,GAAcd,EAAY,kBAAoB,MAC9ChH,GAAQgH,EAAY,eACpB/G,GAAS+G,EAAY,gBACrB+F,GAAc,KAAK,yBAAyB/F,EAAa,KAAK,MAAM2E,GAAQ,EAAI3L,EAAK,EAAG,KAAK,MAAM2L,GAAQ,EAAI1L,EAAM,EAAGD,EAAK,EAC7H+G,EAAS,MAAQgG,GACjB,KAAK,QAAQ,KAAKA,GAAY,EAAGA,GAAY,EAAGA,GAAY,EAAGA,GAAY,CAAC,GAGxE1E,GAEAuD,EAAWV,GAAK,IAAII,GAAQ,MAAME,CAAK,CAAC,EAAE,IAAID,GAAQ,MAAMC,EAAQC,EAAE,CAAC,EACvE1E,EAAS,MAAQ,IAAI7B,EAAO0G,EAAS,EAAGA,EAAS,EAAGA,EAAS,EAAGA,EAAS,CAAC,EAC1E,KAAK,QAAQ,KAAKA,EAAS,EAAGA,EAAS,EAAGA,EAAS,EAAGA,EAAS,CAAC,IAGhEA,EAAWV,GAAK,IAAI,KAAK,OAAQ,EAAE,KAAK,OAAQ,EAAE,KAAK,OAAQ,EAAE,CAAC,EAClEnE,EAAS,MAAQ,IAAI7B,EAAO0G,EAAS,EAAGA,EAAS,EAAGA,EAAS,EAAGA,EAAS,CAAC,EAC1E,KAAK,QAAQ,KAAKA,EAAS,EAAGA,EAAS,EAAGA,EAAS,EAAGA,EAAS,CAAC,IAMxE7E,EAAS,GAAK4E,GAAQ,QACtB,KAAK,KAAK,KAAK5E,EAAS,GAAG,EAAGA,EAAS,GAAG,CAAC,KAK/CG,GACA2F,GAAY,IAAI3F,EAAM,EAAGA,EAAM,EAAGA,EAAM,CAAC,EACzCqF,GAASS,EAAY,CAACjF,EAAOA,CAAK,EAClCyE,GAASQ,EAAY,CAACjF,EAAOA,CAAK,EAClC6E,GAASC,GAAY,QACrBJ,GAAIG,GAAO,EACXF,GAAIE,GAAO,EAAIL,GACfI,GAAIC,GAAO,EAAIJ,GACXE,GAAI,IACJA,GAAI,GAEJA,GAAI,IACJA,GAAI,GAEJC,GAAI,IACJA,GAAI,GAEJA,GAAI,IACJA,GAAI,GAERhG,GAAO,cAAc8F,GAAGC,GAAGC,GAAGG,EAAS,EACvClB,EAAS,IAAIkB,GAAU,EAAGA,GAAU,EAAGA,GAAU,EAAG,CAAC,GAGrDlB,EAAWV,GAAK,IAAI,KAAK,OAAQ,EAAE,KAAK,OAAQ,EAAE,KAAK,OAAQ,EAAE,CAAC,EAEtEnE,EAAS,MAAQ,IAAI7B,EAAO0G,EAAS,EAAGA,EAAS,EAAGA,EAAS,EAAGA,EAAS,CAAC,EAC1E,KAAK,QAAQ,KAAKA,EAAS,EAAGA,EAAS,EAAGA,EAAS,EAAGA,EAAS,CAAC,EAG3E,CACJ,CAGD,kBAAkB9T,EAAMkP,EAAaY,EAAU,CAC3C,GAAI9P,EAAK,WAAa,KAAM,CACxB+F,GAAO,KAAK/F,EAAK,KAAO,kBAAkB,EAC1CkP,EAAY,gBAAkB,KAC9B,KAAK,oBAAoBlP,EAAMkP,EAAaY,EAAU,GAAM,EAAK,EACjE,MACH,CAED,MAAMqF,EADMnV,EAAK,SACO,oBACxB,GAAImV,EAAY,SAAW,EAAG,CAC1BpP,GAAO,KAAK/F,EAAK,KAAO,wBAAwB,EAChDkP,EAAY,gBAAkB,KAC9B,KAAK,oBAAoBlP,EAAMkP,EAAaY,EAAU,GAAM,EAAK,EACjE,MACH,CACD,MAAMsF,EAAQpV,EAAK,QACnBoV,EAAM,WAAW,EAAK,EACtB,KAAK,UAAU,KAAK,IAAI,QAAStG,GAAY,CACzCuG,GAAY,aAAaF,EAAa,IAAM,CACxC,IAAIG,EAAIpG,EAAY,WAChBoG,EAAI,IACJA,EAAI,GAEJA,EAAIH,EAAY,OAAS,IACzBG,EAAIH,EAAY,OAAS,GAE7B,MAAMI,EAAW,IAAM,CACnBrG,EAAY,eAAiBiG,EAAYG,CAAC,EAAE,QAAS,EAAC,MACtDpG,EAAY,gBAAkBiG,EAAYG,CAAC,EAAE,QAAS,EAAC,OACvD,KAAK,oBAAoBF,EAAOlG,EAAaY,EAAU,GAAM,GAAM,OAAW,OAAWqF,EAAYG,CAAC,EAAE,gBAAgB,EACxHF,EAAM,QAAO,EACbtG,GACpB,EACgBI,EAAY,gBAAkB,KAC9B,MAAMsG,EAAcL,EAAYG,CAAC,EAAE,WAAU,EACxCE,EAIDA,EAAY,KAAM7Q,GAAS,CACvBuK,EAAY,gBAAkBvK,EAC9B4Q,GACxB,CAAqB,EANDA,GAQpB,CAAa,CACJ,EAAC,CACL,CAED,kBAAkBE,EAAU5K,EAAW6B,EAAS,CAC5C,IAAIkE,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACJ,MAAMC,EAAUzN,EAAQ,OAClB0N,EAAU1N,EAAQ,OAClB2N,EAAU3N,EAAQ,OAClB4N,EAAO5N,EAAQ,OACf6N,EAAO7N,EAAQ,OACf2R,EAAS3R,EAAQ,OACvB,IAAI4R,EACJ,MAAMC,EAAkB,GACxB,IAAIC,EAAc,EAClB,MAAMC,EAAWpJ,EAAQ,OAAS,EAElC,QAASjG,EAAQ,EAAGA,EAAQqP,EAAUrP,IAClCmK,EAAMlE,EAAQ,EAAIjG,CAAK,EACvBoK,EAAMnE,EAAQ,EAAIjG,EAAQ,CAAC,EAC3BqK,EAAMpE,EAAQ,EAAIjG,EAAQ,CAAC,EAC3BsK,EAAMlG,EAAU,EAAI+F,CAAG,EACvBI,EAAMnG,EAAU,EAAI+F,EAAM,CAAC,EAC3BK,EAAMpG,EAAU,EAAI+F,EAAM,CAAC,EAC3BM,EAAMrG,EAAU,EAAIgG,CAAG,EACvBM,EAAMtG,EAAU,EAAIgG,EAAM,CAAC,EAC3BO,EAAMvG,EAAU,EAAIgG,EAAM,CAAC,EAC3BQ,EAAMxG,EAAU,EAAIiG,CAAG,EACvBQ,EAAMzG,EAAU,EAAIiG,EAAM,CAAC,EAC3BS,EAAM1G,EAAU,EAAIiG,EAAM,CAAC,EAC3BU,EAAQ,IAAIT,EAAKC,EAAKC,CAAG,EACzBQ,EAAQ,IAAIP,EAAKC,EAAKC,CAAG,EACzBM,EAAQ,IAAIL,EAAKC,EAAKC,CAAG,EACzBE,EAAQ,cAAcD,EAASG,CAAI,EACnCD,EAAQ,cAAcD,EAASG,CAAI,EACnC7N,EAAQ,WAAW4N,EAAMC,EAAM8D,CAAM,EACrCC,EAAO,GAAMD,EAAO,SACpBG,GAAeF,EACfC,EAAgBnP,CAAK,EAAIoP,EAE7B,MAAME,EAAU,IAAI,MAAMD,CAAQ,EAClC,IAAIE,EAAkBP,EACtB,QAAShP,EAAQqP,EAAW,EAAGrP,EAAQ,EAAGA,IAAS,CAC/C,MAAMwP,EAAiBL,EAAgBnP,CAAK,EAC5C,GAAIwP,IAAmB,EAEnBF,EAAQtP,CAAK,EAAI,MAEhB,CAED,MAAMyP,GADOD,EAAiBL,EAAgBnP,EAAQ,CAAC,GACfwP,EAAkBD,EACpDG,EAAU,KAAK,MAAMD,CAAuB,EAC5CE,EAAWF,EAA0BC,EACrCE,EAAa,EAAO,KAAK,OAAQ,EAAGD,GACpCE,EAAcH,EAAUE,EAC9BN,EAAQtP,CAAK,EAAI6P,EACjBN,GAAmBM,CACtB,CACJ,CACD,OAAAP,EAAQ,CAAC,EAAIC,EACND,CACV,CAOD,UAAUQ,EAAIC,EAAgB,KAAK,kBAAmB,CAClD,MAAMtH,EAAc,IAAIjB,GAAY,KAAK,cAAeuI,CAAa,EACrE,IAAIxH,EAEAD,EAAM,KAAK,YACf,QAASjN,EAAI,EAAGA,EAAIyU,EAAIzU,IACpBkN,EAAK,KAAK,aAAaD,EAAKG,EAAa,KAAK,cAAepN,CAAC,EAC1DoN,GAAeA,EAAY,mBAC3BA,EAAY,kBAAkBF,EAAID,EAAKjN,CAAC,EAE5C,KAAK,WAAW,KAAKkN,EAAG,SAAS,EAAGA,EAAG,SAAS,EAAGA,EAAG,SAAS,CAAC,EAC5DA,EAAG,OACH,KAAK,QAAQ,KAAKA,EAAG,MAAM,EAAGA,EAAG,MAAM,EAAGA,EAAG,MAAM,EAAGA,EAAG,MAAM,CAAC,EAEhEA,EAAG,IACH,KAAK,KAAK,KAAKA,EAAG,GAAG,EAAGA,EAAG,GAAG,CAAC,EAEnCD,IAEJ,YAAK,aAAewH,EACpB,KAAK,gBACE,KAAK,aACf,CAUD,iBAAiBvW,EAAMuW,EAAIE,EAAWrH,EAAOa,EAAO,CAChD,IAAIyG,EAAUD,GAAwB,GAClC,MAAMC,CAAO,GAAKA,EAAU,GAAKA,EAAU,KAC3CA,EAAU,GAEd,MAAMtG,EAAUpQ,EAAK,gBAAgBS,EAAa,YAAY,EACxD4P,EAAUrQ,EAAK,aACrB,KAAK,QAAQ,KAAK,KAAK,aAAa,EACpC,MAAMkP,EAAc,IAAIjB,GAAY,KAAK,cAAe,IAAI,EAQ5D,OAPAiB,EAAY,cAAgB,KAAK,kBAAkBqH,EAAInG,EAASC,CAAO,EACnEqG,IAAY,EACZxH,EAAY,WAAaE,GAAgB,EAGzCA,EAAQA,GAAgB,IAAIhC,EAAO,EAAG,EAAG,EAAG,CAAC,EAEzCsJ,EAAO,CACX,IAAK,GACD,KAAK,kBAAkB1W,EAAMkP,EAAa,EAAK,EAC/C,MACJ,IAAK,GACD,KAAK,oBAAoBlP,EAAMkP,EAAa,GAAO,GAAO,EAAK,EAC/D,MACJ,IAAK,GACD,KAAK,oBAAoBlP,EAAMkP,EAAa,EAAK,EACjD,MACJ,IAAK,GACD,KAAK,oBAAoBlP,EAAMkP,EAAa,GAAO,OAAW,OAAWE,EAAOa,CAAK,EACrF,KACP,CACD,YAAK,aAAesG,EACpB,KAAK,gBACE,KAAK,cAAgB,CAC/B,CAUD,gBAAgBvW,EAAMuW,EAAIE,EAAWrH,EAAOa,EAAO,CAC/C,IAAIyG,EAAUD,GAAwB,GAClC,MAAMC,CAAO,GAAKA,EAAU,GAAKA,EAAU,KAC3CA,EAAU,GAEd,MAAMtG,EAAUpQ,EAAK,gBAAgBS,EAAa,YAAY,EACxD4P,EAAUrQ,EAAK,aACrB,KAAK,QAAQ,KAAK,KAAK,aAAa,EACpC,MAAMkP,EAAc,IAAIjB,GAAY,KAAK,cAAe,IAAI,EAQ5D,OAPAiB,EAAY,cAAgB,KAAK,kBAAkBqH,EAAInG,EAASC,CAAO,EACnEqG,IAAY,EACZxH,EAAY,WAAaE,GAAgB,EAGzCA,EAAQA,GAAgB,IAAIhC,EAAO,EAAG,EAAG,EAAG,CAAC,EAEzCsJ,EAAO,CACX,IAAK,GACD,KAAK,kBAAkB1W,EAAMkP,EAAa,EAAI,EAC9C,MACJ,IAAK,GACD,KAAK,oBAAoBlP,EAAMkP,EAAa,GAAM,GAAO,EAAK,EAC9D,MACJ,IAAK,GACD,KAAK,oBAAoBlP,EAAMkP,EAAa,EAAI,EAChD,MACJ,IAAK,GACD,KAAK,oBAAoBlP,EAAMkP,EAAa,GAAM,OAAW,OAAWE,EAAOa,CAAK,EACpF,KACP,CACD,YAAK,aAAesG,EACpB,KAAK,gBACE,KAAK,cAAgB,CAC/B,CAUD,aAAaI,EAAQ,EAAGC,EAAM,KAAK,YAAc,EAAGC,EAAS,GAAM,CAC/D,GAAI,CAAC,KAAK,YAAc,CAAC,KAAK,SAC1B,OAAO,KAGX,KAAK,sBAAsBF,EAAOC,EAAKC,CAAM,EAC7C,MAAMC,EAAYvS,EAAW,OAAO,CAAC,EAC/BvE,EAAO,KAAK,KACZ+W,EAAW,KAAK,UAChBC,EAAc,KAAK,aACnBC,EAAQ,KAAK,OACbC,EAAc3S,EAAW,QACzB4S,EAAWD,EAAY,CAAC,EAAE,eAAe,EAAK,EAAK,CAAG,EACtDE,EAAWF,EAAY,CAAC,EAAE,eAAe,EAAK,EAAK,CAAG,EACtDG,EAAWH,EAAY,CAAC,EAAE,eAAe,EAAK,EAAK,CAAG,EACtDzN,EAAUyN,EAAY,CAAC,EAAE,OAAO,OAAO,SAAS,EAChDxN,EAAUwN,EAAY,CAAC,EAAE,OAAO,CAAC,OAAO,SAAS,EACvDpT,GAAO,cAAcgT,CAAS,EAC9B,IAAI/H,EAAM,EAKV,GAJI,KAAK,MAAM,qBACX,KAAK,oBAAsB,IAE/B6H,EAAMA,GAAO,KAAK,YAAc,KAAK,YAAc,EAAIA,EACnD,KAAK,sBACDD,GAAS,GAAKC,GAAO,KAAK,YAAc,GAAG,CAE3C,MAAMU,EAAe,KAAK,MAAM,gBAAe,EAC3CA,IACA7N,EAAQ,SAAS6N,EAAa,OAAO,EACrC5N,EAAQ,SAAS4N,EAAa,OAAO,EAE5C,CAELvI,EAAM,EACN,IAAIwI,EAAS,EACTC,EAAS,EACTC,EAAS,EAEb,QAAS/G,EAAIiG,EAAOjG,GAAKkG,EAAKlG,IAAK,CAC/B,MAAMzB,EAAW,KAAK,UAAUyB,CAAC,EACjC3B,EAAME,EAAS,IACfsI,EAAS,EAAIxI,EACbyI,EAAS,EAAIzI,EACb0I,EAAS,EAAI1I,EAEb,KAAK,eAAeE,CAAQ,EAC5B,MAAMyI,EAAyBzI,EAAS,gBAClC0I,EAAmB1I,EAAS,SAC5B2I,EAAyB3I,EAAS,gBAKxC,GAJI,KAAK,0BACLA,EAAS,kBAAkB6H,CAAS,EAEd7H,EAAS,WAAa,KACzB,CACnB,MAAM4I,EAAS,KAAK,UAAU5I,EAAS,QAAQ,EACzC6I,EAAuBD,EAAO,gBAC9BE,GAAuBF,EAAO,gBAC9BG,GAAWL,EAAiB,EAAIG,EAAqB,CAAC,EAAIH,EAAiB,EAAIG,EAAqB,CAAC,EAAIH,EAAiB,EAAIG,EAAqB,CAAC,EACpJG,GAAWN,EAAiB,EAAIG,EAAqB,CAAC,EAAIH,EAAiB,EAAIG,EAAqB,CAAC,EAAIH,EAAiB,EAAIG,EAAqB,CAAC,EACpJI,GAAWP,EAAiB,EAAIG,EAAqB,CAAC,EAAIH,EAAiB,EAAIG,EAAqB,CAAC,EAAIH,EAAiB,EAAIG,EAAqB,CAAC,EAI1J,GAHAF,EAAuB,EAAIG,GAAqB,EAAIE,GACpDL,EAAuB,EAAIG,GAAqB,EAAIC,GACpDJ,EAAuB,EAAIG,GAAqB,EAAIG,GAChD,KAAK,yBAA0B,CAC/B,MAAMC,EAAkBrB,EAAU,EAClCY,EAAuB,CAAC,EACpBS,EAAgB,CAAC,EAAIL,EAAqB,CAAC,EAAIK,EAAgB,CAAC,EAAIL,EAAqB,CAAC,EAAIK,EAAgB,CAAC,EAAIL,EAAqB,CAAC,EAC7IJ,EAAuB,CAAC,EACpBS,EAAgB,CAAC,EAAIL,EAAqB,CAAC,EAAIK,EAAgB,CAAC,EAAIL,EAAqB,CAAC,EAAIK,EAAgB,CAAC,EAAIL,EAAqB,CAAC,EAC7IJ,EAAuB,CAAC,EACpBS,EAAgB,CAAC,EAAIL,EAAqB,CAAC,EAAIK,EAAgB,CAAC,EAAIL,EAAqB,CAAC,EAAIK,EAAgB,CAAC,EAAIL,EAAqB,CAAC,EAC7IJ,EAAuB,CAAC,EACpBS,EAAgB,CAAC,EAAIL,EAAqB,CAAC,EAAIK,EAAgB,CAAC,EAAIL,EAAqB,CAAC,EAAIK,EAAgB,CAAC,EAAIL,EAAqB,CAAC,EAC7IJ,EAAuB,CAAC,EACpBS,EAAgB,CAAC,EAAIL,EAAqB,CAAC,EAAIK,EAAgB,CAAC,EAAIL,EAAqB,CAAC,EAAIK,EAAgB,CAAC,EAAIL,EAAqB,CAAC,EAC7IJ,EAAuB,CAAC,EACpBS,EAAgB,CAAC,EAAIL,EAAqB,CAAC,EAAIK,EAAgB,CAAC,EAAIL,EAAqB,CAAC,EAAIK,EAAgB,CAAC,EAAIL,EAAqB,CAAC,EAC7IJ,EAAuB,CAAC,EACpBS,EAAgB,CAAC,EAAIL,EAAqB,CAAC,EAAIK,EAAgB,CAAC,EAAIL,EAAqB,CAAC,EAAIK,EAAgB,EAAE,EAAIL,EAAqB,CAAC,EAC9IJ,EAAuB,CAAC,EACpBS,EAAgB,CAAC,EAAIL,EAAqB,CAAC,EAAIK,EAAgB,CAAC,EAAIL,EAAqB,CAAC,EAAIK,EAAgB,EAAE,EAAIL,EAAqB,CAAC,EAC9IJ,EAAuB,CAAC,EACpBS,EAAgB,CAAC,EAAIL,EAAqB,CAAC,EAAIK,EAAgB,CAAC,EAAIL,EAAqB,CAAC,EAAIK,EAAgB,EAAE,EAAIL,EAAqB,CAAC,CACjJ,CACJ,SAEGF,EAAuB,EAAI,EAC3BA,EAAuB,EAAI,EAC3BA,EAAuB,EAAI,EACvB,KAAK,yBAA0B,CAC/B,MAAMO,EAAkBrB,EAAU,EAClCY,EAAuB,CAAC,EAAIS,EAAgB,CAAC,EAC7CT,EAAuB,CAAC,EAAIS,EAAgB,CAAC,EAC7CT,EAAuB,CAAC,EAAIS,EAAgB,CAAC,EAC7CT,EAAuB,CAAC,EAAIS,EAAgB,CAAC,EAC7CT,EAAuB,CAAC,EAAIS,EAAgB,CAAC,EAC7CT,EAAuB,CAAC,EAAIS,EAAgB,CAAC,EAC7CT,EAAuB,CAAC,EAAIS,EAAgB,CAAC,EAC7CT,EAAuB,CAAC,EAAIS,EAAgB,CAAC,EAC7CT,EAAuB,CAAC,EAAIS,EAAgB,EAAE,CACjD,CAEL,MAAMC,EAAuBlB,EAAY,EAAE,EACvCjI,EAAS,mBACTmJ,EAAqB,OAAO,CAAG,EAG/BA,EAAqB,SAASnJ,EAAS,KAAK,EAGhD,MAAMoJ,EAAYnB,EAAY,CAAC,EAC/BmB,EAAU,SAASpJ,EAAS,QAAQ,EACpC,MAAMqJ,EAAUD,EAAU,EAAIpJ,EAAS,MAAM,EACvCsJ,EAAUF,EAAU,EAAIpJ,EAAS,MAAM,EACvCuJ,EAAUH,EAAU,EAAIpJ,EAAS,MAAM,EAC7C,IAAIgJ,EAAWK,EAAUZ,EAAuB,CAAC,EAAIa,EAAUb,EAAuB,CAAC,EAAIc,EAAUd,EAAuB,CAAC,EACzHM,EAAWM,EAAUZ,EAAuB,CAAC,EAAIa,EAAUb,EAAuB,CAAC,EAAIc,EAAUd,EAAuB,CAAC,EACzHQ,EAAWI,EAAUZ,EAAuB,CAAC,EAAIa,EAAUb,EAAuB,CAAC,EAAIc,EAAUd,EAAuB,CAAC,EAC7HO,GAAYG,EAAqB,EACjCJ,GAAYI,EAAqB,EACjCF,GAAYE,EAAqB,EACjC,MAAMK,EAAMzB,EAAYO,CAAM,EAAIK,EAAuB,EAAIT,EAAS,EAAIc,EAAWb,EAAS,EAAIY,EAAWX,EAAS,EAAIa,EACpHQ,EAAM1B,EAAYO,EAAS,CAAC,EAAIK,EAAuB,EAAIT,EAAS,EAAIc,EAAWb,EAAS,EAAIY,EAAWX,EAAS,EAAIa,EACxHS,EAAM3B,EAAYO,EAAS,CAAC,EAAIK,EAAuB,EAAIT,EAAS,EAAIc,EAAWb,EAAS,EAAIY,EAAWX,EAAS,EAAIa,EAK9H,GAJI,KAAK,sBACLzO,EAAQ,0BAA0BgP,EAAIC,EAAIC,CAAE,EAC5CjP,EAAQ,0BAA0B+O,EAAIC,EAAIC,CAAE,GAE5C,KAAK,uBAAyB1J,EAAS,MAAO,CAC9C,MAAMG,EAAQH,EAAS,MACjB8H,EAAW,KAAK,UACtBA,EAASS,CAAM,EAAIpI,EAAM,EACzB2H,EAASS,EAAS,CAAC,EAAIpI,EAAM,EAC7B2H,EAASS,EAAS,CAAC,EAAIpI,EAAM,EAC7B2H,EAASS,EAAS,CAAC,EAAIpI,EAAM,CAChC,CACD,GAAI,KAAK,yBAA2BH,EAAS,GAAI,CAC7C,MAAM2J,EAAK3J,EAAS,GACdgI,EAAQ,KAAK,OACnBA,EAAMQ,CAAM,EAAImB,EAAG,EACnB3B,EAAMQ,EAAS,CAAC,EAAImB,EAAG,CAC1B,CACJ,CAED,OAAI5Y,IACI6W,IACI,KAAK,uBACL7W,EAAK,mBAAmBS,EAAa,UAAWsW,EAAU,GAAO,EAAK,EAEtE,KAAK,yBACL/W,EAAK,mBAAmBS,EAAa,OAAQwW,EAAO,GAAO,EAAK,EAEpEjX,EAAK,mBAAmBS,EAAa,aAAcuW,EAAa,GAAO,EAAK,GAE5E,KAAK,sBACDhX,EAAK,gBACLA,EAAK,gBAAe,EAAG,YAAYyJ,EAASC,EAAS1J,EAAK,YAAY,EAGtEA,EAAK,kBAAkByJ,EAASC,EAAS1J,EAAK,YAAY,IAItE,KAAK,qBAAqB2W,EAAOC,EAAKC,CAAM,EACrC,IACV,CAID,SAAU,CACN,KAAK,MAAM,UACX,KAAK,KAAO,KAEZ,KAAK,WAAa,KAClB,KAAK,SAAW,KAChB,KAAK,SAAW,KAChB,KAAK,KAAO,KACZ,KAAK,QAAU,KACf,KAAK,WAAa,KAClB,KAAK,aAAe,KACpB,KAAK,OAAS,KACd,KAAK,UAAY,IACpB,CAMD,oBAAqB,CACjB,OAAK,KAAK,wBACN,KAAK,MAAM,sBAER,IACV,CAOD,iBAAiBgC,EAAM,CACnB,GAAI,CAAC,KAAK,KACN,OAEJ,MAAMC,EAAMD,EAAO,EACnB,KAAK,KAAK,kBAAkB,IAAI9U,EAAQ,CAAC+U,EAAK,CAACA,EAAK,CAACA,CAAG,EAAG,IAAI/U,EAAQ+U,EAAKA,EAAKA,CAAG,CAAC,CACxF,CAKD,IAAI,iBAAkB,CAClB,OAAO,KAAK,cACf,CAKD,IAAI,gBAAgBC,EAAK,CAChB,KAAK,OAGV,KAAK,eAAiBA,EACtB,KAAK,KAAK,yBAA2BA,EACxC,CAOD,IAAI,wBAAwBA,EAAK,CAC7B,KAAK,yBAA2BA,CACnC,CAMD,IAAI,qBAAqBA,EAAK,CAC1B,KAAK,sBAAwBA,CAChC,CACD,IAAI,uBAAuBA,EAAK,CAC5B,KAAK,wBAA0BA,CAClC,CAMD,IAAI,sBAAuB,CACvB,OAAO,KAAK,qBACf,CAMD,IAAI,wBAAyB,CACzB,OAAO,KAAK,uBACf,CAID,IAAI,mBAAmBA,EAAK,CACxB,KAAK,oBAAsBA,CAC9B,CAID,IAAI,oBAAqB,CACrB,OAAO,KAAK,mBACf,CASD,eAAgB,CAAG,CAQnB,gBAAgB9J,EAAU,CACtB,OAAOA,CACV,CASD,eAAeA,EAAU,CACrB,OAAOA,CACV,CASD,sBAAsB0H,EAAOqC,EAAMnC,EAAQ,CAAG,CAU9C,qBAAqBF,EAAOqC,EAAMnC,EAAQ,CAAG,CACjD,CC98BA,IAAIoC,IACH,SAAUA,EAAM,CACbA,EAAKA,EAAK,MAAW,CAAC,EAAI,QAC1BA,EAAKA,EAAK,WAAgB,CAAC,EAAI,aAC/BA,EAAKA,EAAK,KAAU,CAAC,EAAI,OACzBA,EAAKA,EAAK,OAAY,CAAC,EAAI,QAC/B,GAAGA,KAASA,GAAO,CAAE,EAAC,EAMf,MAAMC,CAAgB,CAKzB,YAAYC,EAAiBD,EAAgB,uBAAwB,CAIjE,KAAK,KAAOE,GAAwB,KACpC,KAAK,gBAAkB,KAKvB,KAAK,WAAaA,GAAwB,WAC1C,KAAK,gBAAkBD,CAC1B,CAED,aAAa3K,EAAS,CAClB,OAAO,IAAI0K,EAAgB1K,EAAQ4K,GAAwB,IAAI,CAAC,CACnE,CAWD,MAAM,gBAAgBC,EAAatZ,EAAO4E,EAAMrC,EAASgX,EAAYC,EAAU,CAC3E,OAAO,KAAK,OAAOF,EAAatZ,EAAO4E,EAAMrC,CAAO,EAAE,KAAMkX,IACjD,CACH,OAAQA,EACR,gBAAiB,CAAE,EACnB,UAAW,CAAE,EACb,gBAAiB,CAAE,EACnB,eAAgB,CAAE,EAClB,WAAY,CAAE,EACd,OAAQ,CAAE,EACV,eAAgB,CAAE,CAClC,EACS,CACJ,CACD,OAAO,iBAAiBC,EAAY9U,EAAM,CACtC,GAAI,CAACA,EAAK,WACN,MAAO,GAEX,MAAM0E,EAAU,IAAI,WAAW1E,CAAI,EAC7ByE,EAAU,IAAI,aAAazE,CAAI,EAE/B+U,EAAY,EAAI,EAAI,EAAI,EAAI,EAAI,EAChC1U,EAAcqE,EAAQ,OAASqQ,EAC/BC,EAAiB,SAAU1K,EAAUnN,EAAG,CAC1C,MAAMgM,EAAI1E,EAAQ,EAAItH,EAAI,CAAC,EACrBiI,EAAIX,EAAQ,EAAItH,EAAI,CAAC,EACrBkI,EAAIZ,EAAQ,EAAItH,EAAI,CAAC,EAC3BmN,EAAS,SAAW,IAAIlL,EAAQ+J,EAAG/D,EAAGC,CAAC,EACvC,MAAM4P,EAAIvQ,EAAQqQ,EAAY5X,EAAI,GAAK,CAAC,EAAI,IACtC,EAAIuH,EAAQqQ,EAAY5X,EAAI,GAAK,CAAC,EAAI,IACtCuB,EAAIgG,EAAQqQ,EAAY5X,EAAI,GAAK,CAAC,EAAI,IAC5CmN,EAAS,MAAQ,IAAI7B,EAAOwM,EAAG,EAAGvW,EAAG,CAAC,CAClD,EACQ,OAAAoW,EAAW,UAAUzU,EAAa2U,CAAc,EACzC,EACV,CACD,OAAO,WAAW5Z,EAAO8Z,EAAW,CAChC,MAAM7Z,EAAO,IAAI2D,GAAK,UAAW5D,CAAK,EAChCsJ,EAAU,IAAI,WAAWwQ,EAAU,IAAI,EACvCzQ,EAAU,IAAI,aAAayQ,EAAU,IAAI,EACzCH,EAAY,EAAI,EAAI,EAAI,EAAI,EAAI,EAChC1U,EAAcqE,EAAQ,OAASqQ,EAC/B7O,EAAY,GACZ7G,EAAa,IAAIC,GACvB,QAASnC,EAAI,EAAGA,EAAIkD,EAAalD,IAAK,CAClC,MAAMgM,EAAI1E,EAAQ,EAAItH,EAAI,CAAC,EACrBiI,EAAIX,EAAQ,EAAItH,EAAI,CAAC,EACrBkI,EAAIZ,EAAQ,EAAItH,EAAI,CAAC,EAC3B+I,EAAU,KAAKiD,EAAG/D,EAAGC,CAAC,CACzB,CACD,GAAI6P,EAAU,gBAAiB,CAC3B,MAAMpO,EAAS,IAAI,aAAazG,EAAc,CAAC,EAC/C,QAASlD,EAAI,EAAGA,EAAIkD,EAAalD,IAAK,CAClC,MAAM8X,EAAIvQ,EAAQqQ,EAAY5X,EAAI,GAAK,CAAC,EAAI,IACtCgY,EAAIzQ,EAAQqQ,EAAY5X,EAAI,GAAK,CAAC,EAAI,IACtCuB,EAAIgG,EAAQqQ,EAAY5X,EAAI,GAAK,CAAC,EAAI,IAC5C2J,EAAO3J,EAAI,EAAI,CAAC,EAAI8X,EACpBnO,EAAO3J,EAAI,EAAI,CAAC,EAAIgY,EACpBrO,EAAO3J,EAAI,EAAI,CAAC,EAAIuB,EACpBoI,EAAO3J,EAAI,EAAI,CAAC,EAAI,CACvB,CACDkC,EAAW,OAASyH,CACvB,CACD,OAAAzH,EAAW,UAAY6G,EACvB7G,EAAW,QAAU6V,EAAU,MAC/B7V,EAAW,YAAYhE,CAAI,EACpBA,CACV,CACD,UAAU2E,EAAM5E,EAAO,CACnB,MAAM6E,EAAO,IAAI,WAAWD,CAAI,EAC1BoV,EAAU,IAAI,YAAYpV,EAAK,MAAM,EAAG,EAAE,CAAC,EAE3CqD,EAAa+R,EAAQ,CAAC,EACtBpU,EAAWf,EAAK,EAAE,EAClBoV,EAAiBpV,EAAK,EAAE,EAI9B,GAFiBA,EAAK,EAAE,GAERmV,EAAQ,CAAC,GAAK,YAAcA,EAAQ,CAAC,GAAK,EAEtD,OAAO,IAAI,QAASjL,GAAY,CAC5BA,EAAQ,CAAE,KAAM,EAAqB,KAAM7I,EAAQ,gBAAiB,EAAK,CAAE,CAC3F,CAAa,EAEL,MAAMW,EAAkB,EAAI,EAAI,EAAI,EAAI,EAAI,EACtCX,EAAS,IAAI,YAAYW,EAAkBoB,CAAU,EACrDiS,EAAgB,GAAO,GAAKD,GAC5BE,EAAY,IAAI,WAAW,CAAC,EAC5BC,EAAY,IAAI,WAAWD,EAAU,MAAM,EAC3CE,EAAmB,SAAUC,EAAIjU,EAAQ,CAC3C,OAAA+T,EAAU,CAAC,EAAIE,EAAGjU,EAAS,CAAC,EAC5B+T,EAAU,CAAC,EAAIE,EAAGjU,EAAS,CAAC,EAC5B+T,EAAU,CAAC,EAAIE,EAAGjU,EAAS,CAAC,EAC5B+T,EAAU,CAAC,EAAIE,EAAGjU,EAAS,CAAC,EAAI,IAAO,IAAO,EACvC8T,EAAU,CAAC,EAAID,CAClC,EACQ,IAAIK,EAAa,GACjB,MAAMzT,EAAW,IAAI,aAAaZ,CAAM,EAClCa,EAAQ,IAAI,aAAab,CAAM,EAC/Bc,EAAO,IAAI,kBAAkBd,CAAM,EACnCe,EAAM,IAAI,kBAAkBf,CAAM,EACxC,IAAIsU,EAAiB,EACjBC,EAAmB,EAClB,KAAK,gBAAgB,QACtBD,EAAiB,GACjBC,EAAmB,KAGvB,QAAS1Y,EAAI,EAAGA,EAAIkG,EAAYlG,IAC5B+E,EAAS/E,EAAI,EAAI,CAAC,EAAIsY,EAAiBxV,EAAM0V,EAAa,CAAC,EAC3DzT,EAAS/E,EAAI,EAAI,CAAC,EAAIyY,EAAiBH,EAAiBxV,EAAM0V,EAAa,CAAC,EAC5EzT,EAAS/E,EAAI,EAAI,CAAC,EAAIyY,EAAiBH,EAAiBxV,EAAM0V,EAAa,CAAC,EAC5EA,GAAc,EAGlB,MAAMG,EAAQ,KACd,QAAS3Y,EAAI,EAAGA,EAAIkG,EAAYlG,IAAK,CACjC,QAAS4Y,EAAY,EAAGA,EAAY,EAAGA,IAAa,CAMhD,MAAM7X,GALY+B,EAAK0V,EAAatS,EAAalG,EAAI,EAAI4Y,CAAS,EAKvC,QAAU,IAAO,KAC5C3T,EAAKjF,EAAI,GAAK,GAAK4Y,CAAS,EAAIhY,EAAO,OAAO,GAAM+X,EAAQ5X,GAAS,IAAK,EAAG,GAAG,CACnF,CACDkE,EAAKjF,EAAI,GAAK,GAAK,CAAC,EAAI8C,EAAK0V,EAAaxY,CAAC,CAC9C,CACDwY,GAActS,EAAa,EAE3B,QAASlG,EAAI,EAAGA,EAAIkG,EAAYlG,IAC5BgF,EAAMhF,EAAI,EAAI,EAAI,CAAC,EAAI,KAAK,IAAI8C,EAAK0V,EAAa,CAAC,EAAI,GAAO,EAAI,EAClExT,EAAMhF,EAAI,EAAI,EAAI,CAAC,EAAI,KAAK,IAAI8C,EAAK0V,EAAa,CAAC,EAAI,GAAO,EAAI,EAClExT,EAAMhF,EAAI,EAAI,EAAI,CAAC,EAAI,KAAK,IAAI8C,EAAK0V,EAAa,CAAC,EAAI,GAAO,EAAI,EAClEA,GAAc,EAGlB,QAASxY,EAAI,EAAGA,EAAIkG,EAAYlG,IAAK,CACjC,MAAMgM,EAAIlJ,EAAK0V,EAAa,CAAC,EACvBvQ,EAAInF,EAAK0V,EAAa,CAAC,EAAIC,EAAiBC,EAC5CxQ,EAAIpF,EAAK0V,EAAa,CAAC,EAAIC,EAAiBC,EAC5CG,EAAK7M,EAAI,MAAQ,EACjB8M,EAAK7Q,EAAI,MAAQ,EACjB8Q,EAAK7Q,EAAI,MAAQ,EACvBhD,EAAIlF,EAAI,GAAK,GAAK,CAAC,EAAIgM,EACvB9G,EAAIlF,EAAI,GAAK,GAAK,CAAC,EAAIiI,EACvB/C,EAAIlF,EAAI,GAAK,GAAK,CAAC,EAAIkI,EACvB,MAAM6K,EAAI,GAAK8F,EAAKA,EAAKC,EAAKA,EAAKC,EAAKA,GACxC7T,EAAIlF,EAAI,GAAK,GAAK,CAAC,EAAI,MAAQ,KAAK,KAAK+S,EAAI,EAAI,EAAIA,CAAC,EAAI,MAC1DyF,GAAc,CACjB,CAED,GAAI3U,EAAU,CAKV,MAAMmV,IAFiBnV,EAAW,IAAMA,EAAW,GAAK,GAEf,EACnCmC,EAAe,KAAK,KAAKgT,EAAmB,EAAE,EACpD,IAAI/S,EAAcuS,EAElB,MAAMrT,EAAK,GAELiB,EADSnI,EAAM,YACA,QAAO,EAAG,eACzBoI,EAAS,KAAK,KAAKH,EAAaE,CAAK,EAE3C,QAASE,EAAe,EAAGA,EAAeN,EAAcM,IAAgB,CACpE,MAAMC,EAAU,IAAI,WAAWF,EAASD,EAAQ,EAAI,CAAC,EACrDjB,EAAG,KAAKoB,CAAO,CAClB,CACD,QAASvG,EAAI,EAAGA,EAAIkG,EAAYlG,IAC5B,QAASwG,EAAe,EAAGA,EAAewS,EAAkBxS,IAAgB,CACxE,MAAMC,EAAU3D,EAAKmD,GAAa,EAC5BK,EAAe,KAAK,MAAME,EAAe,EAAE,EAC3CE,EAAUvB,EAAGmB,CAAY,EACzBK,EAAqBH,EAAe,GACpCI,EAAiB5G,EAAI,GAC3B0G,EAAQC,EAAqBC,CAAc,EAAIH,CAClD,CAEL,OAAO,IAAI,QAASuG,GAAY,CAC5BA,EAAQ,CAAE,KAAM,EAAoB,KAAM7I,EAAQ,gBAAiB,GAAO,GAAIgB,CAAE,CAAE,CAClG,CAAa,CACJ,CACD,OAAO,IAAI,QAAS6H,GAAY,CAC5BA,EAAQ,CAAE,KAAM,EAAoB,KAAM7I,EAAQ,gBAAiB,EAAK,CAAE,CACtF,CAAS,CACJ,CACD,OAAOoT,EAAatZ,EAAO4E,EAAMrC,EAAS,CACtC,MAAMyY,EAAqB,GACrBC,EAAiB,IAAI,eAAe,CACtC,MAAMC,EAAY,CACdA,EAAW,QAAQ,IAAI,WAAWtW,CAAI,CAAC,EACvCsW,EAAW,MAAK,CACnB,CACb,CAAS,EAEKC,EAAsB,IAAI,oBAAoB,MAAM,EACpDC,EAAqBH,EAAe,YAAYE,CAAmB,EACzE,OAAO,IAAI,QAASpM,GAAY,CAC5B,IAAI,SAASqM,CAAkB,EAC1B,YAAa,EACb,KAAMlV,GAAW,CAClB,KAAK,UAAUA,EAAQlG,CAAK,EAAE,KAAMqb,GAAc,CAC9Crb,EAAM,uBAAyB,CAAC,CAAC,KAAK,gBACtC,MAAMsb,EAAoB,IAAI3X,EAAsB,oBAAqB,KAAM3D,EAAO,KAAK,gBAAgB,SAAS,EACpHsb,EAAkB,iBAAmB,KAAK,gBAC1CN,EAAmB,KAAKM,CAAiB,EACzCA,EAAkB,WAAWD,EAAU,KAAMA,EAAU,EAAE,EACzDrb,EAAM,uBAAyB,GAC/B+O,EAAQiM,CAAkB,CAC9C,CAAiB,CACjB,CAAa,EACI,MAAM,IAAM,CAEb7B,EAAgB,mBAAmBvU,CAAI,EAAE,KAAK,MAAOkV,GAAc,CAE/D,OADA9Z,EAAM,uBAAyB,CAAC,CAAC,KAAK,gBAC9B8Z,EAAU,KAAI,CAClB,IAAK,GACD,CACI,MAAMwB,EAAoB,IAAI3X,EAAsB,oBAAqB,KAAM3D,EAAO,KAAK,gBAAgB,SAAS,EACpHsb,EAAkB,iBAAmB,KAAK,gBAC1CN,EAAmB,KAAKM,CAAiB,EACzCA,EAAkB,WAAWxB,EAAU,KAAMA,EAAU,EAAE,CAC5D,CACD,MACJ,IAAK,GACD,CACI,MAAMJ,EAAa,IAAInL,GAAkB,aAAc,EAAGvO,CAAK,EAC3DmZ,EAAgB,iBAAiBO,EAAYI,EAAU,IAAI,EAC3D,MAAMJ,EAAW,eAAc,EAAG,KAAMzZ,GAAS,CAC7C+a,EAAmB,KAAK/a,CAAI,CACpE,CAAqC,EAGDyZ,EAAW,QAAO,CAEzB,CACD,MACJ,IAAK,GAEG,GAAII,EAAU,MACVkB,EAAmB,KAAK7B,EAAgB,WAAWnZ,EAAO8Z,CAAS,CAAC,MAGpE,OAAM,IAAI,MAAM,6CAA6C,EAGrE,MACJ,QACI,MAAM,IAAI,MAAM,wBAAwB,CAC/C,CACD9Z,EAAM,uBAAyB,GAC/B+O,EAAQiM,CAAkB,CAC9C,CAAiB,CACjB,CAAa,CACb,CAAS,CACJ,CAQD,wBAAwBhb,EAAO4E,EAAMrC,EAAS,CAC1C,MAAMgZ,EAAY,IAAIC,GAAexb,CAAK,EAC1C,YAAK,gBAAkBub,EAChB,KAAK,gBAAgB,KAAMvb,EAAO4E,EAAMrC,CAAO,EACjD,KAAMU,IACPA,EAAO,OAAO,QAAShD,GAASsb,EAAU,OAAO,KAAKtb,CAAI,CAAC,EAE3D,KAAK,gBAAkB,KAChBsb,EACV,EACI,MAAOE,GAAO,CACf,WAAK,gBAAkB,KACjBA,CAClB,CAAS,CACJ,CAQD,UAAUzb,EAAO4E,EAAMrC,EAAS,CAE5B,OAAO,KAAK,gBAAgB,KAAMvC,EAAO4E,EAAMrC,CAAO,EAAE,KAAK,IAAM,CAE3E,CAAS,CACJ,CAQD,OAAO,mBAAmBqC,EAAM,CAC5B,MAAMC,EAAO,IAAI,WAAWD,CAAI,EAC1BE,EAAS,IAAI,YAAa,EAAC,OAAOD,EAAK,MAAM,EAAG,KAAO,EAAE,CAAC,EAC1DE,EAAY;AAAA,EACZC,EAAiBF,EAAO,QAAQC,CAAS,EAC/C,GAAIC,EAAiB,GAAK,CAACF,EAEvB,OAAO,IAAI,QAASiK,GAAY,CAC5BA,EAAQ,CAAE,KAAM,EAAoB,KAAMnK,CAAM,EAChE,CAAa,EAEL,MAAMK,EAAc,SAAS,yBAAyB,KAAKH,CAAM,EAAE,CAAC,CAAC,EAC/D4W,EAAc,uBAAuB,KAAK5W,CAAM,EACtD,IAAI6W,EAAY,EACZD,IACAC,EAAY,SAASD,EAAY,CAAC,CAAC,GAEvC,MAAMxW,EAAe,wBAAwB,KAAKJ,CAAM,EACxD,IAAIK,EAAa,EACbD,IACAC,EAAa,SAASD,EAAa,CAAC,CAAC,GAEzC,IAAIE,EAAkB,EAClBC,EAAiB,EACrB,MAAMC,EAAU,CACZ,OAAQ,EACR,IAAK,EACL,KAAM,EACN,MAAO,EACP,MAAO,EACP,OAAQ,EACR,MAAO,EACP,KAAM,CAClB,EACQ,IAAIC,GACH,SAAUA,EAAa,CACpBA,EAAYA,EAAY,OAAY,CAAC,EAAI,SACzCA,EAAYA,EAAY,MAAW,CAAC,EAAI,OAC3C,GAAEA,IAAgBA,EAAc,CAAE,EAAC,EACpC,IAAIC,EAAY,EAChB,MAAMC,EAAmB,GAEnBE,EAAWb,EAAO,MAAM,EAAGE,CAAc,EAAE,MAAM;AAAA,CAAI,EAC3D,UAAWa,KAAQF,EACf,GAAIE,EAAK,WAAW,WAAW,EAAG,CAC9B,KAAM,EAAGE,EAAM1G,CAAI,EAAIwG,EAAK,MAAM,GAAG,EACjCL,GAAa,EAEbH,GAAkBC,EAAQS,CAAI,EAEzBP,GAAa,IAClBC,EAAiB,KAAK,CAAE,KAAApG,EAAM,KAAA0G,EAAM,OAAQX,CAAe,CAAE,EAC7DA,GAAmBE,EAAQS,CAAI,GAE9BT,EAAQS,CAAI,GACbC,GAAO,KAAK,8BAA8BD,CAAI,GAAG,CAExD,SACQF,EAAK,WAAW,UAAU,EAAG,CAClC,KAAM,CAAG,CAAAE,CAAI,EAAIF,EAAK,MAAM,GAAG,EAC3BE,GAAQ,QACRP,EAAY,EAEPO,GAAQ,WACbP,EAAY,EAEnB,CAEL,MAAMoW,EAAkBxW,EAClByW,EAAiBxW,EACvB,OAAO1B,EAAsB,6BAA6BiB,CAAI,EAAE,KAAMoE,GAAe,CACjF,MAAM/C,EAAW,IAAI,SAASrB,EAAMI,EAAiBD,EAAU,MAAM,EACrE,IAAIsB,EAASwV,EAAiB1W,EAAayW,EAAkB3W,EAE7D,MAAM6W,EAAQ,GACd,GAAIH,EACA,QAAS5Z,EAAI,EAAGA,EAAI4Z,EAAW5Z,IAAK,CAChC,MAAMga,EAAkB9V,EAAS,SAASI,CAAM,EAChD,GAAI0V,GAAmB,EAGvB,CAAA1V,GAAU,EACV,QAASwB,EAAI,EAAGA,EAAIkU,EAAiBlU,IAAK,CACtC,MAAMmU,EAAc/V,EAAS,UAAUI,GAAU,EAAIwB,GAAK,EAAG,EAAI,EACjEiU,EAAM,KAAKE,CAAW,CACzB,CACD3V,GAAU,GACb,CAGL,GAAIlB,EACA,OAAO,IAAI,QAAS4J,GAAY,CAC5BA,EAAQ,CAAE,KAAM,EAAoB,KAAM/F,EAAW,OAAQ,GAAIA,EAAW,GAAI,MAAO8S,EAAO,gBAAiB,EAAO,EAC1I,CAAiB,EAIL,IAAIG,EAAgB,EAChBC,EAAqB,EACzB,MAAMC,EAAkB,CAAC,IAAK,IAAK,IAAK,UAAW,UAAW,UAAW,UAAW,QAAS,QAAS,QAAS,OAAO,EAChHC,EAAuB,CAAC,MAAO,QAAS,OAAQ,SAAU,SAAU,QAAQ,EAClF,QAAS5V,EAAgB,EAAGA,EAAgBf,EAAiB,OAAQe,IAAiB,CAClF,MAAMC,EAAWhB,EAAiBe,CAAa,EAC3C2V,EAAgB,SAAS1V,EAAS,IAAI,GACtCwV,IAEAG,EAAqB,SAAS3V,EAAS,IAAI,GAC3CyV,GAEP,CACD,MAAMG,EAAyBJ,GAAiBE,EAAgB,QAAUD,GAAsB,EAC1FI,EAAcX,EAAY,EAAoBU,EAAyB,EAAqB,EAElG,OAAO,IAAI,QAAStN,GAAY,CAC5BA,EAAQ,CAAE,KAAMuN,EAAa,KAAMtT,EAAW,OAAQ,GAAIA,EAAW,GAAI,MAAO8S,EAAO,gBAAiB,CAAC,CAACI,CAAkB,CAAE,CAC9I,CAAa,CACb,CAAS,CACJ,CACL,CACA/C,EAAgB,uBAAyB,CACrC,UAAW,GACX,MAAO,EACX,EAEAoD,GAA0B,IAAIpD,CAAiB", "names": ["name", "shader", "ShaderStore", "gaussianSplattingPixelShader", "gaussianSplattingVertexShader", "gaussianSplattingPixelShaderWGSL", "gaussianSplattingVertexShaderWGSL", "GaussianSplattingMaterialDefines", "MaterialDefines", "GaussianSplattingMaterial", "PushMaterial", "scene", "mesh", "<PERSON><PERSON><PERSON>", "drawWrapper", "defines", "engine", "PrepareDefinesForMisc", "PrepareDefinesForFrameBoundValues", "PrepareDefinesForAttributes", "attribs", "VertexBuffer", "PrepareAttributesForInstances", "uniforms", "samplers", "uniformBuffers", "PrepareUniformsAndSamplersList", "addClipPlaneUniforms", "join", "effect", "__vitePreload", "gaussianSplatting_fragment", "gaussianSplatting_fragment$1", "camera", "render<PERSON>idth", "renderHeight", "numberOfRigs", "focal", "t", "Camera", "gsMesh", "textureSize", "i", "world", "bindClipPlane", "BindFogParameters", "BindLogDepth", "SerializationHelper", "serializationObject", "source", "rootUrl", "RegisterClass", "HCF", "functions.HighestCommonFactor", "<PERSON><PERSON><PERSON>", "functions", "unpackUnorm", "value", "bits", "unpack111011", "result", "unpack8888", "unpackRot", "norm", "a", "b", "c", "m", "PLYType", "PLYValue", "GaussianSplattingMesh", "<PERSON><PERSON>", "url", "keepInRam", "Matrix", "Vector3", "vertexData", "VertexData", "<PERSON><PERSON><PERSON>", "completeCheck", "forced", "frameId", "cameraMatrix", "TmpVectors", "dot", "enableAlphaMode", "effectiveMeshReplacement", "data", "ubuf", "header", "headerEnd", "headerEndIndex", "vertexCount", "chunkElement", "chunkCount", "rowVertexOffset", "rowChunkOffset", "offsets", "ElementMode", "chunkMode", "vertexProperties", "chunkProperties", "filtered", "shDegree", "prop", "typeName", "type", "<PERSON><PERSON>", "dataView", "buffer", "s<PERSON><PERSON><PERSON><PERSON>", "shCoefficientCount", "offset", "compressedChunks", "currentChunk", "propertyIndex", "property", "index", "q", "temp3", "rowOutputLength", "position", "scale", "rgba", "rot", "sh", "chunkIndex", "r0", "r1", "r2", "r3", "plySH", "compressedChunk", "clampedValue", "shIndex", "sh<PERSON><PERSON>", "j", "useCoroutine", "textureCount", "shIndexRead", "splatCount", "EngineStore", "width", "height", "textureIndex", "texture", "shIndexWrite", "shValue", "shArray", "byteIndexInTexture", "offsetPerSplat", "runCoroutineAsync", "createYieldingScheduler", "Tools", "ply<PERSON><PERSON><PERSON>", "splatsData", "doNotRecurse", "shTexture", "newGS", "binfo", "f<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "covA", "covB", "colorArray", "minimum", "maximum", "matrixRotation", "matrixScale", "quaternion", "covBSItemSize", "y", "z", "M", "covariances", "factor", "covIndex", "transform", "ToHalfFloat", "createTextureFromData", "format", "RawTexture", "createTextureFromDataU8", "createTextureFromDataU32", "createTextureFromDataF16", "positions", "shData", "isAsync", "textureLength", "lineCountUpdate", "textureLengthPerUpdate", "partCount", "partIndex", "updateLine", "splatIndexBase", "runCoroutineSync", "centers", "colors", "lineStart", "lineCount", "updateTextureFromData", "texelStart", "texelCount", "cov<PERSON><PERSON><PERSON>", "covB<PERSON>iew", "colorsView", "centers<PERSON>iew", "sh<PERSON>iew", "e", "indexMix", "length", "Vector2", "self", "depthMix", "indices", "floatMix", "viewProj", "depthFactor", "CloudPoint", "particleIndex", "group", "groupId", "idxInGroup", "pcs", "Color4", "target", "isSphere", "bbox", "maxX", "minX", "maxY", "minY", "maxZ", "minZ", "x", "rotation", "Quaternion", "PointsGroup", "groupID", "id", "posFunction", "PointColor", "PointsCloudSystem", "pointSize", "options", "material", "ec", "mat", "StandardMaterial", "Color3", "resolve", "idx", "cp", "particle", "pointsGroup", "imageData", "color", "colorIndices", "redIndex", "greenIndex", "blueIndex", "alphaIndex", "redForCoord", "greenForCoord", "blueForCoord", "alphaForCoord", "isVolume", "colorFromTexture", "hasTexture", "range", "uvSetIndex", "diameter", "meshPos", "meshInd", "meshUV", "meshCol", "place", "meshMatrix", "p", "idxPoints", "id0", "id1", "id2", "v0X", "v0Y", "v0Z", "v1X", "v1Y", "v1Z", "v2X", "v2Y", "v2Z", "vertex0", "vertex1", "vertex2", "vec0", "vec1", "uv0X", "uv0Y", "uv1X", "uv1Y", "uv2X", "uv2Y", "uv0", "uv1", "uv2", "uvec0", "uvec1", "col0X", "col0Y", "col0Z", "col0A", "col1X", "col1Y", "col1Z", "col1A", "col2X", "col2Y", "col2Z", "col2A", "col0", "Vector4", "col1", "col2", "colvec0", "colvec1", "lamda", "mu", "facetPoint", "uvPoint", "colPoint", "tang", "biNorm", "angle", "facetPlaneVec", "gap", "distance", "ray", "<PERSON>", "pickInfo", "direction", "deltaS", "deltaV", "h", "s", "v", "hsvCol", "statedColor", "colPoint3", "pointColors", "RandomRange", "textureList", "clone", "BaseTexture", "n", "finalize", "dataPromise", "nbPoints", "normal", "area", "cumulativeAreas", "surfaceArea", "nbFacets", "density", "remainingPoints", "cumulativeArea", "facetPointsWithFraction", "floored", "fraction", "extraPoint", "facetPoints", "nb", "pointFunction", "colorWith", "colored", "start", "end", "update", "rotMatrix", "colors32", "positions32", "uvs32", "tempVectors", "camAxisX", "camAxisY", "camAxisZ", "boundingInfo", "pindex", "cindex", "uindex", "particleRotationMatrix", "particlePosition", "particleGlobalPosition", "parent", "parentRotationMatrix", "parentGlobalPosition", "rotatedY", "rotatedX", "rotatedZ", "rotMatrixValues", "pivotBackTranslation", "tmpVertex", "vertexX", "vertexY", "vertexZ", "px", "py", "pz", "uv", "size", "vis", "val", "stop", "Mode", "SPLATFileLoader", "loadingOptions", "SPLATFileLoaderMetadata", "meshesNames", "onProgress", "fileName", "meshes", "pointcloud", "<PERSON><PERSON><PERSON><PERSON>", "pointcloudfunc", "r", "parsedPLY", "g", "ubufu32", "fractionalBits", "positionScale", "int32View", "uint8View", "read24bComponent", "u8", "byteOffset", "coordinateSign", "quaternionOffset", "SH_C0", "component", "nx", "ny", "nz", "shComponentCount", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "readableStream", "controller", "decompressionStream", "decompressedStream", "parsedSPZ", "gaussianSplatting", "container", "<PERSON>setC<PERSON><PERSON>", "ex", "faceElement", "faceCount", "rowVertexLength", "rowChunkLength", "faces", "faceVertexCount", "vertexIndex", "propertyCount", "propertyColorCount", "splatProperties", "splatColorProperties", "hasMandatoryProperties", "currentMode", "RegisterSceneLoaderPlugin"], "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Shaders/ShadersInclude/gaussianSplattingFragmentDeclaration.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Shaders/gaussianSplatting.fragment.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Shaders/ShadersInclude/gaussianSplattingVertexDeclaration.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Shaders/ShadersInclude/gaussianSplattingUboDeclaration.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Shaders/ShadersInclude/gaussianSplatting.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Shaders/gaussianSplatting.vertex.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/gaussianSplattingFragmentDeclaration.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/ShadersWGSL/gaussianSplatting.fragment.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/gaussianSplatting.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/ShadersWGSL/gaussianSplatting.vertex.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Materials/GaussianSplatting/gaussianSplattingMaterial.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Maths/math.scalar.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Meshes/GaussianSplatting/gaussianSplattingMesh.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Particles/cloudPoint.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Particles/pointsCloudSystem.js", "../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/SPLAT/splatFileLoader.js"], "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../../Engines/shaderStore.js\";\nimport \"./logDepthFragment.js\";\nimport \"./fogFragment.js\";\nconst name = \"gaussianSplattingFragmentDeclaration\";\nconst shader = `vec4 gaussianColor(vec4 inColor)\n{float A=-dot(vPosition,vPosition);if (A<-4.0) discard;float B=exp(A)*inColor.a;\n#include<logDepthFragment>\nvec3 color=inColor.rgb;\n#ifdef FOG\n#include<fogFragment>\n#endif\nreturn vec4(color,B);}\n`;\n// Sideeffect\nif (!ShaderStore.IncludesShadersStore[name]) {\n    ShaderStore.IncludesShadersStore[name] = shader;\n}\n/** @internal */\nexport const gaussianSplattingFragmentDeclaration = { name, shader };\n//# sourceMappingURL=gaussianSplattingFragmentDeclaration.js.map", "// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore.js\";\nimport \"./ShadersInclude/clipPlaneFragmentDeclaration.js\";\nimport \"./ShadersInclude/logDepthDeclaration.js\";\nimport \"./ShadersInclude/fogFragmentDeclaration.js\";\nimport \"./ShadersInclude/gaussianSplattingFragmentDeclaration.js\";\nimport \"./ShadersInclude/clipPlaneFragment.js\";\nconst name = \"gaussianSplattingPixelShader\";\nconst shader = `#include<clipPlaneFragmentDeclaration>\n#include<logDepthDeclaration>\n#include<fogFragmentDeclaration>\nvarying vec4 vColor;varying vec2 vPosition;\n#include<gaussianSplattingFragmentDeclaration>\nvoid main () { \n#include<clipPlaneFragment>\ngl_FragColor=gaussianColor(vColor);}\n`;\n// Sideeffect\nif (!ShaderStore.ShadersStore[name]) {\n    ShaderStore.ShadersStore[name] = shader;\n}\n/** @internal */\nexport const gaussianSplattingPixelShader = { name, shader };\n//# sourceMappingURL=gaussianSplatting.fragment.js.map", "// Do not edit.\nimport { ShaderStore } from \"../../Engines/shaderStore.js\";\nconst name = \"gaussianSplattingVertexDeclaration\";\nconst shader = `attribute vec2 position;uniform mat4 view;uniform mat4 projection;uniform mat4 world;uniform vec4 vEyePosition;`;\n// Sideeffect\nif (!ShaderStore.IncludesShadersStore[name]) {\n    ShaderStore.IncludesShadersStore[name] = shader;\n}\n/** @internal */\nexport const gaussianSplattingVertexDeclaration = { name, shader };\n//# sourceMappingURL=gaussianSplattingVertexDeclaration.js.map", "// Do not edit.\nimport { ShaderStore } from \"../../Engines/shaderStore.js\";\nimport \"./sceneUboDeclaration.js\";\nimport \"./meshUboDeclaration.js\";\nconst name = \"gaussianSplattingUboDeclaration\";\nconst shader = `#include<sceneUboDeclaration>\n#include<meshUboDeclaration>\nattribute vec2 position;`;\n// Sideeffect\nif (!ShaderStore.IncludesShadersStore[name]) {\n    ShaderStore.IncludesShadersStore[name] = shader;\n}\n/** @internal */\nexport const gaussianSplattingUboDeclaration = { name, shader };\n//# sourceMappingURL=gaussianSplattingUboDeclaration.js.map", "// Do not edit.\nimport { ShaderStore } from \"../../Engines/shaderStore.js\";\nconst name = \"gaussianSplatting\";\nconst shader = `#if !defined(WEBGL2) && !defined(WEBGPU) && !defined(NATIVE)\nmat3 transpose(mat3 matrix) {return mat3(matrix[0][0],matrix[1][0],matrix[2][0],\nmatrix[0][1],matrix[1][1],matrix[2][1],\nmatrix[0][2],matrix[1][2],matrix[2][2]);}\n#endif\nvec2 getDataUV(float index,vec2 textureSize) {float y=floor(index/textureSize.x);float x=index-y*textureSize.x;return vec2((x+0.5)/textureSize.x,(y+0.5)/textureSize.y);}\n#if SH_DEGREE>0\nivec2 getDataUVint(float index,vec2 textureSize) {float y=floor(index/textureSize.x);float x=index-y*textureSize.x;return ivec2(uint(x+0.5),uint(y+0.5));}\n#endif\nstruct Splat {vec4 center;vec4 color;vec4 covA;vec4 covB;\n#if SH_DEGREE>0\nuvec4 sh0; \n#endif\n#if SH_DEGREE>1\nuvec4 sh1;\n#endif\n#if SH_DEGREE>2\nuvec4 sh2;\n#endif\n};Splat readSplat(float splatIndex)\n{Splat splat;vec2 splatUV=getDataUV(splatIndex,dataTextureSize);splat.center=texture2D(centersTexture,splatUV);splat.color=texture2D(colorsTexture,splatUV);splat.covA=texture2D(covariancesATexture,splatUV)*splat.center.w;splat.covB=texture2D(covariancesBTexture,splatUV)*splat.center.w;\n#if SH_DEGREE>0\nivec2 splatUVint=getDataUVint(splatIndex,dataTextureSize);splat.sh0=texelFetch(shTexture0,splatUVint,0);\n#endif\n#if SH_DEGREE>1\nsplat.sh1=texelFetch(shTexture1,splatUVint,0);\n#endif\n#if SH_DEGREE>2\nsplat.sh2=texelFetch(shTexture2,splatUVint,0);\n#endif\nreturn splat;}\n#if defined(WEBGL2) || defined(WEBGPU) || defined(NATIVE)\nvec3 computeColorFromSHDegree(vec3 dir,const vec3 sh[16])\n{const float SH_C0=0.28209479;const float SH_C1=0.48860251;float SH_C2[5];SH_C2[0]=1.092548430;SH_C2[1]=-1.09254843;SH_C2[2]=0.315391565;SH_C2[3]=-1.09254843;SH_C2[4]=0.546274215;float SH_C3[7];SH_C3[0]=-0.59004358;SH_C3[1]=2.890611442;SH_C3[2]=-0.45704579;SH_C3[3]=0.373176332;SH_C3[4]=-0.45704579;SH_C3[5]=1.445305721;SH_C3[6]=-0.59004358;vec3 result=/*SH_C0**/sh[0];\n#if SH_DEGREE>0\nfloat x=dir.x;float y=dir.y;float z=dir.z;result+=- SH_C1*y*sh[1]+SH_C1*z*sh[2]-SH_C1*x*sh[3];\n#if SH_DEGREE>1\nfloat xx=x*x,yy=y*y,zz=z*z;float xy=x*y,yz=y*z,xz=x*z;result+=\nSH_C2[0]*xy*sh[4] +\nSH_C2[1]*yz*sh[5] +\nSH_C2[2]*(2.0*zz-xx-yy)*sh[6] +\nSH_C2[3]*xz*sh[7] +\nSH_C2[4]*(xx-yy)*sh[8];\n#if SH_DEGREE>2\nresult+=\nSH_C3[0]*y*(3.0*xx-yy)*sh[9] +\nSH_C3[1]*xy*z*sh[10] +\nSH_C3[2]*y*(4.0*zz-xx-yy)*sh[11] +\nSH_C3[3]*z*(2.0*zz-3.0*xx-3.0*yy)*sh[12] +\nSH_C3[4]*x*(4.0*zz-xx-yy)*sh[13] +\nSH_C3[5]*z*(xx-yy)*sh[14] +\nSH_C3[6]*x*(xx-3.0*yy)*sh[15];\n#endif\n#endif\n#endif\nreturn result;}\nvec4 decompose(uint value)\n{vec4 components=vec4(\nfloat((value ) & 255u),\nfloat((value>>uint( 8)) & 255u),\nfloat((value>>uint(16)) & 255u),\nfloat((value>>uint(24)) & 255u));return components*vec4(2./255.)-vec4(1.);}\nvec3 computeSH(Splat splat,vec3 color,vec3 dir)\n{vec3 sh[16];sh[0]=color;\n#if SH_DEGREE>0\nvec4 sh00=decompose(splat.sh0.x);vec4 sh01=decompose(splat.sh0.y);vec4 sh02=decompose(splat.sh0.z);sh[1]=vec3(sh00.x,sh00.y,sh00.z);sh[2]=vec3(sh00.w,sh01.x,sh01.y);sh[3]=vec3(sh01.z,sh01.w,sh02.x);\n#endif\n#if SH_DEGREE>1\nvec4 sh03=decompose(splat.sh0.w);vec4 sh04=decompose(splat.sh1.x);vec4 sh05=decompose(splat.sh1.y);sh[4]=vec3(sh02.y,sh02.z,sh02.w);sh[5]=vec3(sh03.x,sh03.y,sh03.z);sh[6]=vec3(sh03.w,sh04.x,sh04.y);sh[7]=vec3(sh04.z,sh04.w,sh05.x);sh[8]=vec3(sh05.y,sh05.z,sh05.w);\n#endif\n#if SH_DEGREE>2\nvec4 sh06=decompose(splat.sh1.z);vec4 sh07=decompose(splat.sh1.w);vec4 sh08=decompose(splat.sh2.x);vec4 sh09=decompose(splat.sh2.y);vec4 sh10=decompose(splat.sh2.z);vec4 sh11=decompose(splat.sh2.w);sh[9]=vec3(sh06.x,sh06.y,sh06.z);sh[10]=vec3(sh06.w,sh07.x,sh07.y);sh[11]=vec3(sh07.z,sh07.w,sh08.x);sh[12]=vec3(sh08.y,sh08.z,sh08.w);sh[13]=vec3(sh09.x,sh09.y,sh09.z);sh[14]=vec3(sh09.w,sh10.x,sh10.y);sh[15]=vec3(sh10.z,sh10.w,sh11.x); \n#endif\nreturn computeColorFromSHDegree(dir,sh);}\n#else\nvec3 computeSH(Splat splat,vec3 color,vec3 dir)\n{return color;}\n#endif\nvec4 gaussianSplatting(vec2 meshPos,vec3 worldPos,vec2 scale,vec3 covA,vec3 covB,mat4 worldMatrix,mat4 viewMatrix,mat4 projectionMatrix)\n{mat4 modelView=viewMatrix*worldMatrix;vec4 camspace=viewMatrix*vec4(worldPos,1.);vec4 pos2d=projectionMatrix*camspace;float bounds=1.2*pos2d.w;if (pos2d.z<-pos2d.w || pos2d.x<-bounds || pos2d.x>bounds\n|| pos2d.y<-bounds || pos2d.y>bounds) {return vec4(0.0,0.0,2.0,1.0);}\nmat3 Vrk=mat3(\ncovA.x,covA.y,covA.z,\ncovA.y,covB.x,covB.y,\ncovA.z,covB.y,covB.z\n);mat3 J=mat3(\nfocal.x/camspace.z,0.,-(focal.x*camspace.x)/(camspace.z*camspace.z),\n0.,focal.y/camspace.z,-(focal.y*camspace.y)/(camspace.z*camspace.z),\n0.,0.,0.\n);mat3 invy=mat3(1,0,0,0,-1,0,0,0,1);mat3 T=invy*transpose(mat3(modelView))*J;mat3 cov2d=transpose(T)*Vrk*T;float mid=(cov2d[0][0]+cov2d[1][1])/2.0;float radius=length(vec2((cov2d[0][0]-cov2d[1][1])/2.0,cov2d[0][1]));float epsilon=0.0001;float lambda1=mid+radius+epsilon,lambda2=mid-radius+epsilon;if (lambda2<0.0)\n{return vec4(0.0,0.0,2.0,1.0);}\nvec2 diagonalVector=normalize(vec2(cov2d[0][1],lambda1-cov2d[0][0]));vec2 majorAxis=min(sqrt(2.0*lambda1),1024.0)*diagonalVector;vec2 minorAxis=min(sqrt(2.0*lambda2),1024.0)*vec2(diagonalVector.y,-diagonalVector.x);vec2 vCenter=vec2(pos2d);return vec4(\nvCenter \n+ ((meshPos.x*majorAxis\n+ meshPos.y*minorAxis)*invViewport*pos2d.w)*scale,pos2d.zw);}`;\n// Sideeffect\nif (!ShaderStore.IncludesShadersStore[name]) {\n    ShaderStore.IncludesShadersStore[name] = shader;\n}\n/** @internal */\nexport const gaussianSplatting = { name, shader };\n//# sourceMappingURL=gaussianSplatting.js.map", "// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore.js\";\nimport \"./ShadersInclude/gaussianSplattingVertexDeclaration.js\";\nimport \"./ShadersInclude/gaussianSplattingUboDeclaration.js\";\nimport \"./ShadersInclude/clipPlaneVertexDeclaration.js\";\nimport \"./ShadersInclude/fogVertexDeclaration.js\";\nimport \"./ShadersInclude/logDepthDeclaration.js\";\nimport \"./ShadersInclude/helperFunctions.js\";\nimport \"./ShadersInclude/gaussianSplatting.js\";\nimport \"./ShadersInclude/clipPlaneVertex.js\";\nimport \"./ShadersInclude/fogVertex.js\";\nimport \"./ShadersInclude/logDepthVertex.js\";\nconst name = \"gaussianSplattingVertexShader\";\nconst shader = `#include<__decl__gaussianSplattingVertex>\n#ifdef LOGARITHMICDEPTH\n#extension GL_EXT_frag_depth : enable\n#endif\n#include<clipPlaneVertexDeclaration>\n#include<fogVertexDeclaration>\n#include<logDepthDeclaration>\n#include<helperFunctions>\nattribute float splatIndex;uniform vec2 invViewport;uniform vec2 dataTextureSize;uniform vec2 focal;uniform sampler2D covariancesATexture;uniform sampler2D covariancesBTexture;uniform sampler2D centersTexture;uniform sampler2D colorsTexture;\n#if SH_DEGREE>0\nuniform highp usampler2D shTexture0;\n#endif\n#if SH_DEGREE>1\nuniform highp usampler2D shTexture1;\n#endif\n#if SH_DEGREE>2\nuniform highp usampler2D shTexture2;\n#endif\nvarying vec4 vColor;varying vec2 vPosition;\n#include<gaussianSplatting>\nvoid main () {Splat splat=readSplat(splatIndex);vec3 covA=splat.covA.xyz;vec3 covB=vec3(splat.covA.w,splat.covB.xy);vec4 worldPos=world*vec4(splat.center.xyz,1.0);vColor=splat.color;vPosition=position;\n#if SH_DEGREE>0\nmat3 worldRot=mat3(world);mat3 normWorldRot=inverseMat3(worldRot);vec3 dir=normalize(normWorldRot*(worldPos.xyz-vEyePosition.xyz));dir*=vec3(1.,1.,-1.); \nvColor.xyz=computeSH(splat,splat.color.xyz,dir);\n#endif\ngl_Position=gaussianSplatting(position,worldPos.xyz,vec2(1.,1.),covA,covB,world,view,projection);\n#include<clipPlaneVertex>\n#include<fogVertex>\n#include<logDepthVertex>\n}\n`;\n// Sideeffect\nif (!ShaderStore.ShadersStore[name]) {\n    ShaderStore.ShadersStore[name] = shader;\n}\n/** @internal */\nexport const gaussianSplattingVertexShader = { name, shader };\n//# sourceMappingURL=gaussianSplatting.vertex.js.map", "// Do not edit.\nimport { ShaderStore } from \"../../Engines/shaderStore.js\";\nimport \"./logDepthFragment.js\";\nimport \"./fogFragment.js\";\nconst name = \"gaussianSplattingFragmentDeclaration\";\nconst shader = `fn gaussianColor(inColor: vec4f,inPosition: vec2f)->vec4f\n{var A : f32=-dot(inPosition,inPosition);if (A>-4.0)\n{var B: f32=exp(A)*inColor.a;\n#include<logDepthFragment>\nvar color: vec3f=inColor.rgb;\n#ifdef FOG\n#include<fogFragment>\n#endif\nreturn vec4f(color,B);} else {return vec4f(0.0);}}\n`;\n// Sideeffect\nif (!ShaderStore.IncludesShadersStoreWGSL[name]) {\n    ShaderStore.IncludesShadersStoreWGSL[name] = shader;\n}\n/** @internal */\nexport const gaussianSplattingFragmentDeclarationWGSL = { name, shader };\n//# sourceMappingURL=gaussianSplattingFragmentDeclaration.js.map", "// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore.js\";\nimport \"./ShadersInclude/clipPlaneFragmentDeclaration.js\";\nimport \"./ShadersInclude/logDepthDeclaration.js\";\nimport \"./ShadersInclude/fogFragmentDeclaration.js\";\nimport \"./ShadersInclude/gaussianSplattingFragmentDeclaration.js\";\nimport \"./ShadersInclude/clipPlaneFragment.js\";\nconst name = \"gaussianSplattingPixelShader\";\nconst shader = `#include<clipPlaneFragmentDeclaration>\n#include<logDepthDeclaration>\n#include<fogFragmentDeclaration>\nvarying vColor: vec4f;varying vPosition: vec2f;\n#include<gaussianSplattingFragmentDeclaration>\n@fragment\nfn main(input: FragmentInputs)->FragmentOutputs {\n#include<clipPlaneFragment>\nfragmentOutputs.color=gaussianColor(input.vColor,input.vPosition);}\n`;\n// Sideeffect\nif (!ShaderStore.ShadersStoreWGSL[name]) {\n    ShaderStore.ShadersStoreWGSL[name] = shader;\n}\n/** @internal */\nexport const gaussianSplattingPixelShaderWGSL = { name, shader };\n//# sourceMappingURL=gaussianSplatting.fragment.js.map", "// Do not edit.\nimport { ShaderStore } from \"../../Engines/shaderStore.js\";\nconst name = \"gaussianSplatting\";\nconst shader = `fn getDataUV(index: f32,dataTextureSize: vec2f)->vec2<f32> {let y: f32=floor(index/dataTextureSize.x);let x: f32=index-y*dataTextureSize.x;return vec2f((x+0.5),(y+0.5));}\nstruct Splat {center: vec4f,\ncolor: vec4f,\ncovA: vec4f,\ncovB: vec4f,\n#if SH_DEGREE>0\nsh0: vec4<u32>,\n#endif\n#if SH_DEGREE>1\nsh1: vec4<u32>,\n#endif\n#if SH_DEGREE>2\nsh2: vec4<u32>,\n#endif\n};fn readSplat(splatIndex: f32,dataTextureSize: vec2f)->Splat {var splat: Splat;let splatUV=getDataUV(splatIndex,dataTextureSize);let splatUVi32=vec2<i32>(i32(splatUV.x),i32(splatUV.y));splat.center=textureLoad(centersTexture,splatUVi32,0);splat.color=textureLoad(colorsTexture,splatUVi32,0);splat.covA=textureLoad(covariancesATexture,splatUVi32,0)*splat.center.w;splat.covB=textureLoad(covariancesBTexture,splatUVi32,0)*splat.center.w;\n#if SH_DEGREE>0\nsplat.sh0=textureLoad(shTexture0,splatUVi32,0);\n#endif\n#if SH_DEGREE>1\nsplat.sh1=textureLoad(shTexture1,splatUVi32,0);\n#endif\n#if SH_DEGREE>2\nsplat.sh2=textureLoad(shTexture2,splatUVi32,0);\n#endif\nreturn splat;}\nfn computeColorFromSHDegree(dir: vec3f,sh: array<vec3<f32>,16>)->vec3f\n{let SH_C0: f32=0.28209479;let SH_C1: f32=0.48860251;var SH_C2: array<f32,5>=array<f32,5>(\n1.092548430,\n-1.09254843,\n0.315391565,\n-1.09254843,\n0.546274215\n);var SH_C3: array<f32,7>=array<f32,7>(\n-0.59004358,\n2.890611442,\n-0.45704579,\n0.373176332,\n-0.45704579,\n1.445305721,\n-0.59004358\n);var result: vec3f=/*SH_C0**/sh[0];\n#if SH_DEGREE>0\nlet x: f32=dir.x;let y: f32=dir.y;let z: f32=dir.z;result+=-SH_C1*y*sh[1]+SH_C1*z*sh[2]-SH_C1*x*sh[3];\n#if SH_DEGREE>1\nlet xx: f32=x*x;let yy: f32=y*y;let zz: f32=z*z;let xy: f32=x*y;let yz: f32=y*z;let xz: f32=x*z;result+=\nSH_C2[0]*xy*sh[4] +\nSH_C2[1]*yz*sh[5] +\nSH_C2[2]*(2.0f*zz-xx-yy)*sh[6] +\nSH_C2[3]*xz*sh[7] +\nSH_C2[4]*(xx-yy)*sh[8];\n#if SH_DEGREE>2\nresult+=\nSH_C3[0]*y*(3.0f*xx-yy)*sh[9] +\nSH_C3[1]*xy*z*sh[10] +\nSH_C3[2]*y*(4.0f*zz-xx-yy)*sh[11] +\nSH_C3[3]*z*(2.0f*zz-3.0f*xx-3.0f*yy)*sh[12] +\nSH_C3[4]*x*(4.0f*zz-xx-yy)*sh[13] +\nSH_C3[5]*z*(xx-yy)*sh[14] +\nSH_C3[6]*x*(xx-3.0f*yy)*sh[15];\n#endif\n#endif\n#endif\nreturn result;}\nfn decompose(value: u32)->vec4f\n{let components : vec4f=vec4f(\nf32((value ) & 255u),\nf32((value>>u32( 8)) & 255u),\nf32((value>>u32(16)) & 255u),\nf32((value>>u32(24)) & 255u));return components*vec4f(2./255.)-vec4f(1.);}\nfn computeSH(splat: Splat,color: vec3f,dir: vec3f)->vec3f\n{var sh: array<vec3<f32>,16>;sh[0]=color;\n#if SH_DEGREE>0\nlet sh00: vec4f=decompose(splat.sh0.x);let sh01: vec4f=decompose(splat.sh0.y);let sh02: vec4f=decompose(splat.sh0.z);sh[1]=vec3f(sh00.x,sh00.y,sh00.z);sh[2]=vec3f(sh00.w,sh01.x,sh01.y);sh[3]=vec3f(sh01.z,sh01.w,sh02.x);\n#endif\n#if SH_DEGREE>1\nlet sh03: vec4f=decompose(splat.sh0.w);let sh04: vec4f=decompose(splat.sh1.x);let sh05: vec4f=decompose(splat.sh1.y);sh[4]=vec3f(sh02.y,sh02.z,sh02.w);sh[5]=vec3f(sh03.x,sh03.y,sh03.z);sh[6]=vec3f(sh03.w,sh04.x,sh04.y);sh[7]=vec3f(sh04.z,sh04.w,sh05.x);sh[8]=vec3f(sh05.y,sh05.z,sh05.w);\n#endif\n#if SH_DEGREE>2\nlet sh06: vec4f=decompose(splat.sh1.z);let sh07: vec4f=decompose(splat.sh1.w);let sh08: vec4f=decompose(splat.sh2.x);let sh09: vec4f=decompose(splat.sh2.y);let sh10: vec4f=decompose(splat.sh2.z);let sh11: vec4f=decompose(splat.sh2.w);sh[9]=vec3f(sh06.x,sh06.y,sh06.z);sh[10]=vec3f(sh06.w,sh07.x,sh07.y);sh[11]=vec3f(sh07.z,sh07.w,sh08.x);sh[12]=vec3f(sh08.y,sh08.z,sh08.w);sh[13]=vec3f(sh09.x,sh09.y,sh09.z);sh[14]=vec3f(sh09.w,sh10.x,sh10.y);sh[15]=vec3f(sh10.z,sh10.w,sh11.x); \n#endif\nreturn computeColorFromSHDegree(dir,sh);}\nfn gaussianSplatting(\nmeshPos: vec2<f32>,\nworldPos: vec3<f32>,\nscale: vec2<f32>,\ncovA: vec3<f32>,\ncovB: vec3<f32>,\nworldMatrix: mat4x4<f32>,\nviewMatrix: mat4x4<f32>,\nprojectionMatrix: mat4x4<f32>,\nfocal: vec2f,\ninvViewport: vec2f\n)->vec4f {let modelView=viewMatrix*worldMatrix;let camspace=viewMatrix*vec4f(worldPos,1.0);let pos2d=projectionMatrix*camspace;let bounds=1.2*pos2d.w;if (pos2d.z<0. || pos2d.x<-bounds || pos2d.x>bounds || pos2d.y<-bounds || pos2d.y>bounds) {return vec4f(0.0,0.0,2.0,1.0);}\nlet Vrk=mat3x3<f32>(\ncovA.x,covA.y,covA.z,\ncovA.y,covB.x,covB.y,\ncovA.z,covB.y,covB.z\n);let J=mat3x3<f32>(\nfocal.x/camspace.z,0.0,-(focal.x*camspace.x)/(camspace.z*camspace.z),\n0.0,focal.y/camspace.z,-(focal.y*camspace.y)/(camspace.z*camspace.z),\n0.0,0.0,0.0\n);let invy=mat3x3<f32>(\n1.0,0.0,0.0,\n0.0,-1.0,0.0,\n0.0,0.0,1.0\n);let T=invy*transpose(mat3x3<f32>(\nmodelView[0].xyz,\nmodelView[1].xyz,\nmodelView[2].xyz))*J;let cov2d=transpose(T)*Vrk*T;let mid=(cov2d[0][0]+cov2d[1][1])/2.0;let radius=length(vec2<f32>((cov2d[0][0]-cov2d[1][1])/2.0,cov2d[0][1]));let lambda1=mid+radius;let lambda2=mid-radius;if (lambda2<0.0) {return vec4f(0.0,0.0,2.0,1.0);}\nlet diagonalVector=normalize(vec2<f32>(cov2d[0][1],lambda1-cov2d[0][0]));let majorAxis=min(sqrt(2.0*lambda1),1024.0)*diagonalVector;let minorAxis=min(sqrt(2.0*lambda2),1024.0)*vec2<f32>(diagonalVector.y,-diagonalVector.x);let vCenter=vec2<f32>(pos2d.x,pos2d.y);return vec4f(\nvCenter+((meshPos.x*majorAxis+meshPos.y*minorAxis)*invViewport*pos2d.w)*scale,\npos2d.z,\npos2d.w\n);}\n`;\n// Sideeffect\nif (!ShaderStore.IncludesShadersStoreWGSL[name]) {\n    ShaderStore.IncludesShadersStoreWGSL[name] = shader;\n}\n/** @internal */\nexport const gaussianSplattingWGSL = { name, shader };\n//# sourceMappingURL=gaussianSplatting.js.map", "// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore.js\";\nimport \"./ShadersInclude/sceneUboDeclaration.js\";\nimport \"./ShadersInclude/meshUboDeclaration.js\";\nimport \"./ShadersInclude/helperFunctions.js\";\nimport \"./ShadersInclude/clipPlaneVertexDeclaration.js\";\nimport \"./ShadersInclude/fogVertexDeclaration.js\";\nimport \"./ShadersInclude/logDepthDeclaration.js\";\nimport \"./ShadersInclude/gaussianSplatting.js\";\nimport \"./ShadersInclude/clipPlaneVertex.js\";\nimport \"./ShadersInclude/fogVertex.js\";\nimport \"./ShadersInclude/logDepthVertex.js\";\nconst name = \"gaussianSplattingVertexShader\";\nconst shader = `#include<sceneUboDeclaration>\n#include<meshUboDeclaration>\n#include<helperFunctions>\n#include<clipPlaneVertexDeclaration>\n#include<fogVertexDeclaration>\n#include<logDepthDeclaration>\nattribute splatIndex: f32;attribute position: vec2f;uniform invViewport: vec2f;uniform dataTextureSize: vec2f;uniform focal: vec2f;var covariancesATexture: texture_2d<f32>;var covariancesBTexture: texture_2d<f32>;var centersTexture: texture_2d<f32>;var colorsTexture: texture_2d<f32>;\n#if SH_DEGREE>0\nvar shTexture0: texture_2d<u32>;\n#endif\n#if SH_DEGREE>1\nvar shTexture1: texture_2d<u32>;\n#endif\n#if SH_DEGREE>2\nvar shTexture2: texture_2d<u32>;\n#endif\nvarying vColor: vec4f;varying vPosition: vec2f;\n#include<gaussianSplatting>\n@vertex\nfn main(input : VertexInputs)->FragmentInputs {var splat: Splat=readSplat(input.splatIndex,uniforms.dataTextureSize);var covA: vec3f=splat.covA.xyz;var covB: vec3f=vec3f(splat.covA.w,splat.covB.xy);let worldPos: vec4f=mesh.world*vec4f(splat.center.xyz,1.0);vertexOutputs.vPosition=input.position;\n#if SH_DEGREE>0\nlet worldRot: mat3x3f= mat3x3f(mesh.world[0].xyz,mesh.world[1].xyz,mesh.world[2].xyz);let normWorldRot: mat3x3f=inverseMat3(worldRot);var dir: vec3f=normalize(normWorldRot*(worldPos.xyz-scene.vEyePosition.xyz));dir*=vec3f(1.,1.,-1.); \nvertexOutputs.vColor=vec4f(computeSH(splat,splat.color.xyz,dir),1.0);\n#else\nvertexOutputs.vColor=splat.color;\n#endif\nvertexOutputs.position=gaussianSplatting(input.position,worldPos.xyz,vec2f(1.0,1.0),covA,covB,mesh.world,scene.view,scene.projection,uniforms.focal,uniforms.invViewport);\n#include<clipPlaneVertex>\n#include<fogVertex>\n#include<logDepthVertex>\n}\n`;\n// Sideeffect\nif (!ShaderStore.ShadersStoreWGSL[name]) {\n    ShaderStore.ShadersStoreWGSL[name] = shader;\n}\n/** @internal */\nexport const gaussianSplattingVertexShaderWGSL = { name, shader };\n//# sourceMappingURL=gaussianSplatting.vertex.js.map", "import { SerializationHelper } from \"../../Misc/decorators.serialization.js\";\nimport { VertexBuffer } from \"../../Buffers/buffer.js\";\nimport { MaterialDefines } from \"../../Materials/materialDefines.js\";\nimport { PushMaterial } from \"../../Materials/pushMaterial.js\";\nimport { RegisterClass } from \"../../Misc/typeStore.js\";\nimport { addClipPlaneUniforms, bindClipPlane } from \"../clipPlaneMaterialHelper.js\";\nimport { Camera } from \"../../Cameras/camera.js\";\nimport \"../../Shaders/gaussianSplatting.fragment.js\";\nimport \"../../Shaders/gaussianSplatting.vertex.js\";\nimport \"../../ShadersWGSL/gaussianSplatting.fragment.js\";\nimport \"../../ShadersWGSL/gaussianSplatting.vertex.js\";\nimport { BindFogParameters, BindLogDepth, PrepareAttributesForInstances, PrepareDefinesForAttributes, PrepareDefinesForFrameBoundValues, PrepareDefinesForMisc, PrepareUniformsAndSamplersList, } from \"../materialHelper.functions.js\";\n/**\n * @internal\n */\nclass GaussianSplattingMaterialDefines extends MaterialDefines {\n    /**\n     * Constructor of the defines.\n     */\n    constructor() {\n        super();\n        this.FOG = false;\n        this.THIN_INSTANCES = true;\n        this.LOGARITHMICDEPTH = false;\n        this.CLIPPLANE = false;\n        this.CLIPPLANE2 = false;\n        this.CLIPPLANE3 = false;\n        this.CLIPPLANE4 = false;\n        this.CLIPPLANE5 = false;\n        this.CLIPPLANE6 = false;\n        this.SH_DEGREE = 0;\n        this.rebuild();\n    }\n}\n/**\n * GaussianSplattingMaterial material used to render Gaussian Splatting\n * @experimental\n */\nexport class GaussianSplattingMaterial extends PushMaterial {\n    /**\n     * Instantiates a Gaussian Splatting Material in the given scene\n     * @param name The friendly name of the material\n     * @param scene The scene to add the material to\n     */\n    constructor(name, scene) {\n        super(name, scene);\n        this.backFaceCulling = false;\n    }\n    /**\n     * Gets a boolean indicating that current material needs to register RTT\n     */\n    get hasRenderTargetTextures() {\n        return false;\n    }\n    /**\n     * Specifies whether or not this material should be rendered in alpha test mode.\n     * @returns false\n     */\n    needAlphaTesting() {\n        return false;\n    }\n    /**\n     * Specifies whether or not this material should be rendered in alpha blend mode.\n     * @returns true\n     */\n    needAlphaBlending() {\n        return true;\n    }\n    /**\n     * Checks whether the material is ready to be rendered for a given mesh.\n     * @param mesh The mesh to render\n     * @param subMesh The submesh to check against\n     * @returns true if all the dependencies are ready (Textures, Effects...)\n     */\n    isReadyForSubMesh(mesh, subMesh) {\n        const useInstances = true;\n        const drawWrapper = subMesh._drawWrapper;\n        if (drawWrapper.effect && this.isFrozen) {\n            if (drawWrapper._wasPreviouslyReady && drawWrapper._wasPreviouslyUsingInstances === useInstances) {\n                return true;\n            }\n        }\n        if (!subMesh.materialDefines) {\n            subMesh.materialDefines = new GaussianSplattingMaterialDefines();\n        }\n        const scene = this.getScene();\n        const defines = subMesh.materialDefines;\n        if (this._isReadyForSubMesh(subMesh)) {\n            return true;\n        }\n        const engine = scene.getEngine();\n        // Misc.\n        PrepareDefinesForMisc(mesh, scene, this._useLogarithmicDepth, this.pointsCloud, this.fogEnabled, false, defines);\n        // Values that need to be evaluated on every frame\n        PrepareDefinesForFrameBoundValues(scene, engine, this, defines, useInstances, null, true);\n        // Attribs\n        PrepareDefinesForAttributes(mesh, defines, false, false);\n        // SH is disabled for webGL1\n        if (engine.version > 1 || engine.isWebGPU) {\n            defines[\"SH_DEGREE\"] = mesh.shDegree;\n        }\n        // Get correct effect\n        if (defines.isDirty) {\n            defines.markAsProcessed();\n            scene.resetCachedMaterial();\n            //Attributes\n            const attribs = [VertexBuffer.PositionKind, \"splatIndex\"];\n            PrepareAttributesForInstances(attribs, defines);\n            const uniforms = [\"world\", \"view\", \"projection\", \"vFogInfos\", \"vFogColor\", \"logarithmicDepthConstant\", \"invViewport\", \"dataTextureSize\", \"focal\", \"vEyePosition\"];\n            const samplers = [\"covariancesATexture\", \"covariancesBTexture\", \"centersTexture\", \"colorsTexture\", \"shTexture0\", \"shTexture1\", \"shTexture2\"];\n            const uniformBuffers = [\"Scene\", \"Mesh\"];\n            PrepareUniformsAndSamplersList({\n                uniformsNames: uniforms,\n                uniformBuffersNames: uniformBuffers,\n                samplers: samplers,\n                defines: defines,\n            });\n            addClipPlaneUniforms(uniforms);\n            const join = defines.toString();\n            const effect = scene.getEngine().createEffect(\"gaussianSplatting\", {\n                attributes: attribs,\n                uniformsNames: uniforms,\n                uniformBuffersNames: uniformBuffers,\n                samplers: samplers,\n                defines: join,\n                onCompiled: this.onCompiled,\n                onError: this.onError,\n                indexParameters: {},\n                shaderLanguage: this._shaderLanguage,\n                extraInitializationsAsync: async () => {\n                    if (this._shaderLanguage === 1 /* ShaderLanguage.WGSL */) {\n                        await Promise.all([import(\"../../ShadersWGSL/gaussianSplatting.fragment.js\"), import(\"../../ShadersWGSL/gaussianSplatting.vertex.js\")]);\n                    }\n                    else {\n                        await Promise.all([import(\"../../Shaders/gaussianSplatting.fragment.js\"), import(\"../../Shaders/gaussianSplatting.vertex.js\")]);\n                    }\n                },\n            }, engine);\n            subMesh.setEffect(effect, defines, this._materialContext);\n        }\n        if (!subMesh.effect || !subMesh.effect.isReady()) {\n            return false;\n        }\n        defines._renderId = scene.getRenderId();\n        drawWrapper._wasPreviouslyReady = true;\n        drawWrapper._wasPreviouslyUsingInstances = useInstances;\n        return true;\n    }\n    /**\n     * Bind material effect for a specific Gaussian Splatting mesh\n     * @param mesh Gaussian splatting mesh\n     * @param effect Splatting material or node material\n     * @param scene scene that contains mesh and camera used for rendering\n     */\n    static BindEffect(mesh, effect, scene) {\n        const engine = scene.getEngine();\n        const camera = scene.activeCamera;\n        const renderWidth = engine.getRenderWidth();\n        const renderHeight = engine.getRenderHeight();\n        // check if rigcamera, get number of rigs\n        const numberOfRigs = camera?.rigParent?.rigCameras.length || 1;\n        effect.setFloat2(\"invViewport\", 1 / (renderWidth / numberOfRigs), 1 / renderHeight);\n        let focal = 1000;\n        if (camera) {\n            /*\n            more explicit version:\n            const t = camera.getProjectionMatrix().m[5];\n            const FovY = Math.atan(1.0 / t) * 2.0;\n            focal = renderHeight / 2.0 / Math.tan(FovY / 2.0);\n            Using a shorter version here to not have tan(atan) and 2.0 factor\n            */\n            const t = camera.getProjectionMatrix().m[5];\n            if (camera.fovMode == Camera.FOVMODE_VERTICAL_FIXED) {\n                focal = (renderHeight * t) / 2.0;\n            }\n            else {\n                focal = (renderWidth * t) / 2.0;\n            }\n        }\n        effect.setFloat2(\"focal\", focal, focal);\n        const gsMesh = mesh;\n        if (gsMesh.covariancesATexture) {\n            const textureSize = gsMesh.covariancesATexture.getSize();\n            effect.setFloat2(\"dataTextureSize\", textureSize.width, textureSize.height);\n            effect.setTexture(\"covariancesATexture\", gsMesh.covariancesATexture);\n            effect.setTexture(\"covariancesBTexture\", gsMesh.covariancesBTexture);\n            effect.setTexture(\"centersTexture\", gsMesh.centersTexture);\n            effect.setTexture(\"colorsTexture\", gsMesh.colorsTexture);\n            if (gsMesh.shTextures) {\n                for (let i = 0; i < gsMesh.shTextures?.length; i++) {\n                    effect.setTexture(`shTexture${i}`, gsMesh.shTextures[i]);\n                }\n            }\n        }\n    }\n    /**\n     * Binds the submesh to this material by preparing the effect and shader to draw\n     * @param world defines the world transformation matrix\n     * @param mesh defines the mesh containing the submesh\n     * @param subMesh defines the submesh to bind the material to\n     */\n    bindForSubMesh(world, mesh, subMesh) {\n        const scene = this.getScene();\n        const defines = subMesh.materialDefines;\n        if (!defines) {\n            return;\n        }\n        const effect = subMesh.effect;\n        if (!effect) {\n            return;\n        }\n        this._activeEffect = effect;\n        // Matrices Mesh.\n        mesh.getMeshUniformBuffer().bindToEffect(effect, \"Mesh\");\n        mesh.transferToEffect(world);\n        // Bind data\n        const mustRebind = this._mustRebind(scene, effect, subMesh, mesh.visibility);\n        if (mustRebind) {\n            this.bindView(effect);\n            this.bindViewProjection(effect);\n            GaussianSplattingMaterial.BindEffect(mesh, this._activeEffect, scene);\n            // Clip plane\n            bindClipPlane(effect, this, scene);\n        }\n        else if (scene.getEngine()._features.needToAlwaysBindUniformBuffers) {\n            this._needToBindSceneUbo = true;\n        }\n        // Fog\n        BindFogParameters(scene, mesh, effect);\n        // Log. depth\n        if (this.useLogarithmicDepth) {\n            BindLogDepth(defines, effect, scene);\n        }\n        this._afterBind(mesh, this._activeEffect, subMesh);\n    }\n    /**\n     * Clones the material.\n     * @param name The cloned name.\n     * @returns The cloned material.\n     */\n    clone(name) {\n        return SerializationHelper.Clone(() => new GaussianSplattingMaterial(name, this.getScene()), this);\n    }\n    /**\n     * Serializes the current material to its JSON representation.\n     * @returns The JSON representation.\n     */\n    serialize() {\n        const serializationObject = super.serialize();\n        serializationObject.customType = \"BABYLON.GaussianSplattingMaterial\";\n        return serializationObject;\n    }\n    /**\n     * Gets the class name of the material\n     * @returns \"GaussianSplattingMaterial\"\n     */\n    getClassName() {\n        return \"GaussianSplattingMaterial\";\n    }\n    /**\n     * Parse a JSON input to create back a Gaussian Splatting material.\n     * @param source The JSON data to parse\n     * @param scene The scene to create the parsed material in\n     * @param rootUrl The root url of the assets the material depends upon\n     * @returns the instantiated GaussianSplattingMaterial.\n     */\n    static Parse(source, scene, rootUrl) {\n        return SerializationHelper.Parse(() => new GaussianSplattingMaterial(source.name, scene), source, scene, rootUrl);\n    }\n}\nRegisterClass(\"BABYLON.GaussianSplattingMaterial\", GaussianSplattingMaterial);\n//# sourceMappingURL=gaussianSplattingMaterial.js.map", "/* eslint-disable @typescript-eslint/naming-convention */\nimport * as functions from \"./math.scalar.functions.js\";\nconst HCF = functions.HighestCommonFactor;\n/**\n * Scalar computation library\n */\nexport const Scalar = {\n    ...functions,\n    /**\n     * Two pi constants convenient for computation.\n     */\n    TwoPi: Math.PI * 2,\n    /**\n     * Returns -1 if value is negative and +1 is value is positive.\n     * @param value the value\n     * @returns the value itself if it's equal to zero.\n     */\n    Sign: Math.sign,\n    /**\n     * the log2 of value.\n     * @param value the value to compute log2 of\n     * @returns the log2 of value.\n     */\n    Log2: Math.log2,\n    /**\n     * Returns the highest common factor of two integers.\n     * @param a first parameter\n     * @param b second parameter\n     * @returns HCF of a and b\n     */\n    HCF,\n};\n/* eslint-enable @typescript-eslint/naming-convention */\n//# sourceMappingURL=math.scalar.js.map", "import { SubMesh } from \"../subMesh.js\";\nimport { Mesh } from \"../mesh.js\";\nimport { VertexData } from \"../mesh.vertexData.js\";\nimport { Matrix, TmpVectors, Vector2, Vector3 } from \"../../Maths/math.vector.js\";\nimport { Logger } from \"../../Misc/logger.js\";\nimport { GaussianSplattingMaterial } from \"../../Materials/GaussianSplatting/gaussianSplattingMaterial.js\";\nimport { RawTexture } from \"../../Materials/Textures/rawTexture.js\";\n\nimport { Tools } from \"../../Misc/tools.js\";\nimport \"../thinInstanceMesh.js\";\nimport { ToHalfFloat } from \"../../Misc/textureTools.js\";\nimport { Scalar } from \"../../Maths/math.scalar.js\";\nimport { runCoroutineSync, runCoroutineAsync, createYieldingScheduler } from \"../../Misc/coroutine.js\";\nimport { EngineStore } from \"../../Engines/engineStore.js\";\n// @internal\nconst unpackUnorm = (value, bits) => {\n    const t = (1 << bits) - 1;\n    return (value & t) / t;\n};\n// @internal\nconst unpack111011 = (value, result) => {\n    result.x = unpackUnorm(value >>> 21, 11);\n    result.y = unpackUnorm(value >>> 11, 10);\n    result.z = unpackUnorm(value, 11);\n};\n// @internal\nconst unpack8888 = (value, result) => {\n    result[0] = unpackUnorm(value >>> 24, 8) * 255;\n    result[1] = unpackUnorm(value >>> 16, 8) * 255;\n    result[2] = unpackUnorm(value >>> 8, 8) * 255;\n    result[3] = unpackUnorm(value, 8) * 255;\n};\n// @internal\n// unpack quaternion with 2,10,10,10 format (largest element, 3x10bit element)\nconst unpackRot = (value, result) => {\n    const norm = 1.0 / (Math.sqrt(2) * 0.5);\n    const a = (unpackUnorm(value >>> 20, 10) - 0.5) * norm;\n    const b = (unpackUnorm(value >>> 10, 10) - 0.5) * norm;\n    const c = (unpackUnorm(value, 10) - 0.5) * norm;\n    const m = Math.sqrt(1.0 - (a * a + b * b + c * c));\n    switch (value >>> 30) {\n        case 0:\n            result.set(m, a, b, c);\n            break;\n        case 1:\n            result.set(a, m, b, c);\n            break;\n        case 2:\n            result.set(a, b, m, c);\n            break;\n        case 3:\n            result.set(a, b, c, m);\n            break;\n    }\n};\n/**\n * Representation of the types\n */\nvar PLYType;\n(function (PLYType) {\n    PLYType[PLYType[\"FLOAT\"] = 0] = \"FLOAT\";\n    PLYType[PLYType[\"INT\"] = 1] = \"INT\";\n    PLYType[PLYType[\"UINT\"] = 2] = \"UINT\";\n    PLYType[PLYType[\"DOUBLE\"] = 3] = \"DOUBLE\";\n    PLYType[PLYType[\"UCHAR\"] = 4] = \"UCHAR\";\n    PLYType[PLYType[\"UNDEFINED\"] = 5] = \"UNDEFINED\";\n})(PLYType || (PLYType = {}));\n/**\n * Usage types of the PLY values\n */\nvar PLYValue;\n(function (PLYValue) {\n    PLYValue[PLYValue[\"MIN_X\"] = 0] = \"MIN_X\";\n    PLYValue[PLYValue[\"MIN_Y\"] = 1] = \"MIN_Y\";\n    PLYValue[PLYValue[\"MIN_Z\"] = 2] = \"MIN_Z\";\n    PLYValue[PLYValue[\"MAX_X\"] = 3] = \"MAX_X\";\n    PLYValue[PLYValue[\"MAX_Y\"] = 4] = \"MAX_Y\";\n    PLYValue[PLYValue[\"MAX_Z\"] = 5] = \"MAX_Z\";\n    PLYValue[PLYValue[\"MIN_SCALE_X\"] = 6] = \"MIN_SCALE_X\";\n    PLYValue[PLYValue[\"MIN_SCALE_Y\"] = 7] = \"MIN_SCALE_Y\";\n    PLYValue[PLYValue[\"MIN_SCALE_Z\"] = 8] = \"MIN_SCALE_Z\";\n    PLYValue[PLYValue[\"MAX_SCALE_X\"] = 9] = \"MAX_SCALE_X\";\n    PLYValue[PLYValue[\"MAX_SCALE_Y\"] = 10] = \"MAX_SCALE_Y\";\n    PLYValue[PLYValue[\"MAX_SCALE_Z\"] = 11] = \"MAX_SCALE_Z\";\n    PLYValue[PLYValue[\"PACKED_POSITION\"] = 12] = \"PACKED_POSITION\";\n    PLYValue[PLYValue[\"PACKED_ROTATION\"] = 13] = \"PACKED_ROTATION\";\n    PLYValue[PLYValue[\"PACKED_SCALE\"] = 14] = \"PACKED_SCALE\";\n    PLYValue[PLYValue[\"PACKED_COLOR\"] = 15] = \"PACKED_COLOR\";\n    PLYValue[PLYValue[\"X\"] = 16] = \"X\";\n    PLYValue[PLYValue[\"Y\"] = 17] = \"Y\";\n    PLYValue[PLYValue[\"Z\"] = 18] = \"Z\";\n    PLYValue[PLYValue[\"SCALE_0\"] = 19] = \"SCALE_0\";\n    PLYValue[PLYValue[\"SCALE_1\"] = 20] = \"SCALE_1\";\n    PLYValue[PLYValue[\"SCALE_2\"] = 21] = \"SCALE_2\";\n    PLYValue[PLYValue[\"DIFFUSE_RED\"] = 22] = \"DIFFUSE_RED\";\n    PLYValue[PLYValue[\"DIFFUSE_GREEN\"] = 23] = \"DIFFUSE_GREEN\";\n    PLYValue[PLYValue[\"DIFFUSE_BLUE\"] = 24] = \"DIFFUSE_BLUE\";\n    PLYValue[PLYValue[\"OPACITY\"] = 25] = \"OPACITY\";\n    PLYValue[PLYValue[\"F_DC_0\"] = 26] = \"F_DC_0\";\n    PLYValue[PLYValue[\"F_DC_1\"] = 27] = \"F_DC_1\";\n    PLYValue[PLYValue[\"F_DC_2\"] = 28] = \"F_DC_2\";\n    PLYValue[PLYValue[\"F_DC_3\"] = 29] = \"F_DC_3\";\n    PLYValue[PLYValue[\"ROT_0\"] = 30] = \"ROT_0\";\n    PLYValue[PLYValue[\"ROT_1\"] = 31] = \"ROT_1\";\n    PLYValue[PLYValue[\"ROT_2\"] = 32] = \"ROT_2\";\n    PLYValue[PLYValue[\"ROT_3\"] = 33] = \"ROT_3\";\n    PLYValue[PLYValue[\"MIN_COLOR_R\"] = 34] = \"MIN_COLOR_R\";\n    PLYValue[PLYValue[\"MIN_COLOR_G\"] = 35] = \"MIN_COLOR_G\";\n    PLYValue[PLYValue[\"MIN_COLOR_B\"] = 36] = \"MIN_COLOR_B\";\n    PLYValue[PLYValue[\"MAX_COLOR_R\"] = 37] = \"MAX_COLOR_R\";\n    PLYValue[PLYValue[\"MAX_COLOR_G\"] = 38] = \"MAX_COLOR_G\";\n    PLYValue[PLYValue[\"MAX_COLOR_B\"] = 39] = \"MAX_COLOR_B\";\n    PLYValue[PLYValue[\"SH_0\"] = 40] = \"SH_0\";\n    PLYValue[PLYValue[\"SH_1\"] = 41] = \"SH_1\";\n    PLYValue[PLYValue[\"SH_2\"] = 42] = \"SH_2\";\n    PLYValue[PLYValue[\"SH_3\"] = 43] = \"SH_3\";\n    PLYValue[PLYValue[\"SH_4\"] = 44] = \"SH_4\";\n    PLYValue[PLYValue[\"SH_5\"] = 45] = \"SH_5\";\n    PLYValue[PLYValue[\"SH_6\"] = 46] = \"SH_6\";\n    PLYValue[PLYValue[\"SH_7\"] = 47] = \"SH_7\";\n    PLYValue[PLYValue[\"SH_8\"] = 48] = \"SH_8\";\n    PLYValue[PLYValue[\"SH_9\"] = 49] = \"SH_9\";\n    PLYValue[PLYValue[\"SH_10\"] = 50] = \"SH_10\";\n    PLYValue[PLYValue[\"SH_11\"] = 51] = \"SH_11\";\n    PLYValue[PLYValue[\"SH_12\"] = 52] = \"SH_12\";\n    PLYValue[PLYValue[\"SH_13\"] = 53] = \"SH_13\";\n    PLYValue[PLYValue[\"SH_14\"] = 54] = \"SH_14\";\n    PLYValue[PLYValue[\"SH_15\"] = 55] = \"SH_15\";\n    PLYValue[PLYValue[\"SH_16\"] = 56] = \"SH_16\";\n    PLYValue[PLYValue[\"SH_17\"] = 57] = \"SH_17\";\n    PLYValue[PLYValue[\"SH_18\"] = 58] = \"SH_18\";\n    PLYValue[PLYValue[\"SH_19\"] = 59] = \"SH_19\";\n    PLYValue[PLYValue[\"SH_20\"] = 60] = \"SH_20\";\n    PLYValue[PLYValue[\"SH_21\"] = 61] = \"SH_21\";\n    PLYValue[PLYValue[\"SH_22\"] = 62] = \"SH_22\";\n    PLYValue[PLYValue[\"SH_23\"] = 63] = \"SH_23\";\n    PLYValue[PLYValue[\"SH_24\"] = 64] = \"SH_24\";\n    PLYValue[PLYValue[\"SH_25\"] = 65] = \"SH_25\";\n    PLYValue[PLYValue[\"SH_26\"] = 66] = \"SH_26\";\n    PLYValue[PLYValue[\"SH_27\"] = 67] = \"SH_27\";\n    PLYValue[PLYValue[\"SH_28\"] = 68] = \"SH_28\";\n    PLYValue[PLYValue[\"SH_29\"] = 69] = \"SH_29\";\n    PLYValue[PLYValue[\"SH_30\"] = 70] = \"SH_30\";\n    PLYValue[PLYValue[\"SH_31\"] = 71] = \"SH_31\";\n    PLYValue[PLYValue[\"SH_32\"] = 72] = \"SH_32\";\n    PLYValue[PLYValue[\"SH_33\"] = 73] = \"SH_33\";\n    PLYValue[PLYValue[\"SH_34\"] = 74] = \"SH_34\";\n    PLYValue[PLYValue[\"SH_35\"] = 75] = \"SH_35\";\n    PLYValue[PLYValue[\"SH_36\"] = 76] = \"SH_36\";\n    PLYValue[PLYValue[\"SH_37\"] = 77] = \"SH_37\";\n    PLYValue[PLYValue[\"SH_38\"] = 78] = \"SH_38\";\n    PLYValue[PLYValue[\"SH_39\"] = 79] = \"SH_39\";\n    PLYValue[PLYValue[\"SH_40\"] = 80] = \"SH_40\";\n    PLYValue[PLYValue[\"SH_41\"] = 81] = \"SH_41\";\n    PLYValue[PLYValue[\"SH_42\"] = 82] = \"SH_42\";\n    PLYValue[PLYValue[\"SH_43\"] = 83] = \"SH_43\";\n    PLYValue[PLYValue[\"SH_44\"] = 84] = \"SH_44\";\n    PLYValue[PLYValue[\"UNDEFINED\"] = 85] = \"UNDEFINED\";\n})(PLYValue || (PLYValue = {}));\n/**\n * Class used to render a gaussian splatting mesh\n */\nexport class GaussianSplattingMesh extends Mesh {\n    /**\n     * SH degree. 0 = no sh (default). 1 = 3 parameters. 2 = 8 parameters. 3 = 15 parameters.\n     */\n    get shDegree() {\n        return this._shDegree;\n    }\n    /**\n     * returns the splats data array buffer that contains in order : postions (3 floats), size (3 floats), color (4 bytes), orientation quaternion (4 bytes)\n     */\n    get splatsData() {\n        return this._splatsData;\n    }\n    /**\n     * Gets the covariancesA texture\n     */\n    get covariancesATexture() {\n        return this._covariancesATexture;\n    }\n    /**\n     * Gets the covariancesB texture\n     */\n    get covariancesBTexture() {\n        return this._covariancesBTexture;\n    }\n    /**\n     * Gets the centers texture\n     */\n    get centersTexture() {\n        return this._centersTexture;\n    }\n    /**\n     * Gets the colors texture\n     */\n    get colorsTexture() {\n        return this._colorsTexture;\n    }\n    /**\n     * Gets the SH textures\n     */\n    get shTextures() {\n        return this._shTextures;\n    }\n    /**\n     * set rendering material\n     */\n    set material(value) {\n        this._material = value;\n        this._material.backFaceCulling = true;\n        this._material.cullBackFaces = false;\n        value.resetDrawCache();\n    }\n    /**\n     * get rendering material\n     */\n    get material() {\n        return this._material;\n    }\n    /**\n     * Creates a new gaussian splatting mesh\n     * @param name defines the name of the mesh\n     * @param url defines the url to load from (optional)\n     * @param scene defines the hosting scene (optional)\n     * @param keepInRam keep datas in ram for editing purpose\n     */\n    constructor(name, url = null, scene = null, keepInRam = false) {\n        super(name, scene);\n        this._vertexCount = 0;\n        this._worker = null;\n        this._frameIdLastUpdate = -1;\n        this._modelViewMatrix = Matrix.Identity();\n        this._canPostToWorker = true;\n        this._readyToDisplay = false;\n        this._covariancesATexture = null;\n        this._covariancesBTexture = null;\n        this._centersTexture = null;\n        this._colorsTexture = null;\n        this._splatPositions = null;\n        this._splatIndex = null;\n        this._shTextures = null;\n        this._splatsData = null;\n        this._sh = null;\n        this._keepInRam = false;\n        this._delayedTextureUpdate = null;\n        this._oldDirection = new Vector3();\n        this._useRGBACovariants = false;\n        this._material = null;\n        this._tmpCovariances = [0, 0, 0, 0, 0, 0];\n        this._sortIsDirty = false;\n        this._shDegree = 0;\n        const vertexData = new VertexData();\n        // Use an intanced quad or triangle. Triangle might be a bit faster because of less shader invocation but I didn't see any difference.\n        // Keeping both and use triangle for now.\n        // for quad, use following lines\n        //vertexData.positions = [-2, -2, 0, 2, -2, 0, 2, 2, 0, -2, 2, 0];\n        //vertexData.indices = [0, 1, 2, 0, 2, 3];\n        vertexData.positions = [-3, -2, 0, 3, -2, 0, 0, 4, 0];\n        vertexData.indices = [0, 1, 2];\n        vertexData.applyToMesh(this);\n        this.subMeshes = [];\n        // for quad, use following line\n        //new SubMesh(0, 0, 4, 0, 6, this);\n        new SubMesh(0, 0, 3, 0, 3, this);\n        this.setEnabled(false);\n        // webGL2 and webGPU support for RG texture with float16 is fine. not webGL1\n        this._useRGBACovariants = !this.getEngine().isWebGPU && this.getEngine().version === 1.0;\n        this._keepInRam = keepInRam;\n        if (url) {\n            this.loadFileAsync(url);\n        }\n        this._material = new GaussianSplattingMaterial(this.name + \"_material\", this._scene);\n    }\n    /**\n     * Returns the class name\n     * @returns \"GaussianSplattingMesh\"\n     */\n    getClassName() {\n        return \"GaussianSplattingMesh\";\n    }\n    /**\n     * Returns the total number of vertices (splats) within the mesh\n     * @returns the total number of vertices\n     */\n    getTotalVertices() {\n        return this._vertexCount;\n    }\n    /**\n     * Is this node ready to be used/rendered\n     * @param completeCheck defines if a complete check (including materials and lights) has to be done (false by default)\n     * @returns true when ready\n     */\n    isReady(completeCheck = false) {\n        if (!super.isReady(completeCheck, true)) {\n            return false;\n        }\n        if (!this._readyToDisplay) {\n            // mesh is ready when worker has done at least 1 sorting\n            this._postToWorker(true);\n            return false;\n        }\n        return true;\n    }\n    /** @internal */\n    _postToWorker(forced = false) {\n        const frameId = this.getScene().getFrameId();\n        if ((forced || frameId !== this._frameIdLastUpdate) && this._worker && this._scene.activeCamera && this._canPostToWorker) {\n            const cameraMatrix = this._scene.activeCamera.getViewMatrix();\n            this.getWorldMatrix().multiplyToRef(cameraMatrix, this._modelViewMatrix);\n            cameraMatrix.invertToRef(TmpVectors.Matrix[0]);\n            this.getWorldMatrix().multiplyToRef(TmpVectors.Matrix[0], TmpVectors.Matrix[1]);\n            Vector3.TransformNormalToRef(Vector3.Forward(this._scene.useRightHandedSystem), TmpVectors.Matrix[1], TmpVectors.Vector3[2]);\n            TmpVectors.Vector3[2].normalize();\n            const dot = Vector3.Dot(TmpVectors.Vector3[2], this._oldDirection);\n            if (forced || Math.abs(dot - 1) >= 0.01) {\n                this._oldDirection.copyFrom(TmpVectors.Vector3[2]);\n                this._frameIdLastUpdate = frameId;\n                this._canPostToWorker = false;\n                this._worker.postMessage({ view: this._modelViewMatrix.m, depthMix: this._depthMix, useRightHandedSystem: this._scene.useRightHandedSystem }, [\n                    this._depthMix.buffer,\n                ]);\n            }\n        }\n    }\n    /**\n     * Triggers the draw call for the mesh. Usually, you don't need to call this method by your own because the mesh rendering is handled by the scene rendering manager\n     * @param subMesh defines the subMesh to render\n     * @param enableAlphaMode defines if alpha mode can be changed\n     * @param effectiveMeshReplacement defines an optional mesh used to provide info for the rendering\n     * @returns the current mesh\n     */\n    render(subMesh, enableAlphaMode, effectiveMeshReplacement) {\n        this._postToWorker();\n        return super.render(subMesh, enableAlphaMode, effectiveMeshReplacement);\n    }\n    static _TypeNameToEnum(name) {\n        switch (name) {\n            case \"float\":\n                return 0 /* PLYType.FLOAT */;\n            case \"int\":\n                return 1 /* PLYType.INT */;\n                break;\n            case \"uint\":\n                return 2 /* PLYType.UINT */;\n            case \"double\":\n                return 3 /* PLYType.DOUBLE */;\n            case \"uchar\":\n                return 4 /* PLYType.UCHAR */;\n        }\n        return 5 /* PLYType.UNDEFINED */;\n    }\n    static _ValueNameToEnum(name) {\n        switch (name) {\n            case \"min_x\":\n                return 0 /* PLYValue.MIN_X */;\n            case \"min_y\":\n                return 1 /* PLYValue.MIN_Y */;\n            case \"min_z\":\n                return 2 /* PLYValue.MIN_Z */;\n            case \"max_x\":\n                return 3 /* PLYValue.MAX_X */;\n            case \"max_y\":\n                return 4 /* PLYValue.MAX_Y */;\n            case \"max_z\":\n                return 5 /* PLYValue.MAX_Z */;\n            case \"min_scale_x\":\n                return 6 /* PLYValue.MIN_SCALE_X */;\n            case \"min_scale_y\":\n                return 7 /* PLYValue.MIN_SCALE_Y */;\n            case \"min_scale_z\":\n                return 8 /* PLYValue.MIN_SCALE_Z */;\n            case \"max_scale_x\":\n                return 9 /* PLYValue.MAX_SCALE_X */;\n            case \"max_scale_y\":\n                return 10 /* PLYValue.MAX_SCALE_Y */;\n            case \"max_scale_z\":\n                return 11 /* PLYValue.MAX_SCALE_Z */;\n            case \"packed_position\":\n                return 12 /* PLYValue.PACKED_POSITION */;\n            case \"packed_rotation\":\n                return 13 /* PLYValue.PACKED_ROTATION */;\n            case \"packed_scale\":\n                return 14 /* PLYValue.PACKED_SCALE */;\n            case \"packed_color\":\n                return 15 /* PLYValue.PACKED_COLOR */;\n            case \"x\":\n                return 16 /* PLYValue.X */;\n            case \"y\":\n                return 17 /* PLYValue.Y */;\n            case \"z\":\n                return 18 /* PLYValue.Z */;\n            case \"scale_0\":\n                return 19 /* PLYValue.SCALE_0 */;\n            case \"scale_1\":\n                return 20 /* PLYValue.SCALE_1 */;\n            case \"scale_2\":\n                return 21 /* PLYValue.SCALE_2 */;\n            case \"diffuse_red\":\n            case \"red\":\n                return 22 /* PLYValue.DIFFUSE_RED */;\n            case \"diffuse_green\":\n            case \"green\":\n                return 23 /* PLYValue.DIFFUSE_GREEN */;\n            case \"diffuse_blue\":\n            case \"blue\":\n                return 24 /* PLYValue.DIFFUSE_BLUE */;\n            case \"f_dc_0\":\n                return 26 /* PLYValue.F_DC_0 */;\n            case \"f_dc_1\":\n                return 27 /* PLYValue.F_DC_1 */;\n            case \"f_dc_2\":\n                return 28 /* PLYValue.F_DC_2 */;\n            case \"f_dc_3\":\n                return 29 /* PLYValue.F_DC_3 */;\n            case \"opacity\":\n                return 25 /* PLYValue.OPACITY */;\n            case \"rot_0\":\n                return 30 /* PLYValue.ROT_0 */;\n            case \"rot_1\":\n                return 31 /* PLYValue.ROT_1 */;\n            case \"rot_2\":\n                return 32 /* PLYValue.ROT_2 */;\n            case \"rot_3\":\n                return 33 /* PLYValue.ROT_3 */;\n            case \"min_r\":\n                return 34 /* PLYValue.MIN_COLOR_R */;\n            case \"min_g\":\n                return 35 /* PLYValue.MIN_COLOR_G */;\n            case \"min_b\":\n                return 36 /* PLYValue.MIN_COLOR_B */;\n            case \"max_r\":\n                return 37 /* PLYValue.MAX_COLOR_R */;\n            case \"max_g\":\n                return 38 /* PLYValue.MAX_COLOR_G */;\n            case \"max_b\":\n                return 39 /* PLYValue.MAX_COLOR_B */;\n            case \"f_rest_0\":\n                return 40 /* PLYValue.SH_0 */;\n            case \"f_rest_1\":\n                return 41 /* PLYValue.SH_1 */;\n            case \"f_rest_2\":\n                return 42 /* PLYValue.SH_2 */;\n            case \"f_rest_3\":\n                return 43 /* PLYValue.SH_3 */;\n            case \"f_rest_4\":\n                return 44 /* PLYValue.SH_4 */;\n            case \"f_rest_5\":\n                return 45 /* PLYValue.SH_5 */;\n            case \"f_rest_6\":\n                return 46 /* PLYValue.SH_6 */;\n            case \"f_rest_7\":\n                return 47 /* PLYValue.SH_7 */;\n            case \"f_rest_8\":\n                return 48 /* PLYValue.SH_8 */;\n            case \"f_rest_9\":\n                return 49 /* PLYValue.SH_9 */;\n            case \"f_rest_10\":\n                return 50 /* PLYValue.SH_10 */;\n            case \"f_rest_11\":\n                return 51 /* PLYValue.SH_11 */;\n            case \"f_rest_12\":\n                return 52 /* PLYValue.SH_12 */;\n            case \"f_rest_13\":\n                return 53 /* PLYValue.SH_13 */;\n            case \"f_rest_14\":\n                return 54 /* PLYValue.SH_14 */;\n            case \"f_rest_15\":\n                return 55 /* PLYValue.SH_15 */;\n            case \"f_rest_16\":\n                return 56 /* PLYValue.SH_16 */;\n            case \"f_rest_17\":\n                return 57 /* PLYValue.SH_17 */;\n            case \"f_rest_18\":\n                return 58 /* PLYValue.SH_18 */;\n            case \"f_rest_19\":\n                return 59 /* PLYValue.SH_19 */;\n            case \"f_rest_20\":\n                return 60 /* PLYValue.SH_20 */;\n            case \"f_rest_21\":\n                return 61 /* PLYValue.SH_21 */;\n            case \"f_rest_22\":\n                return 62 /* PLYValue.SH_22 */;\n            case \"f_rest_23\":\n                return 63 /* PLYValue.SH_23 */;\n            case \"f_rest_24\":\n                return 64 /* PLYValue.SH_24 */;\n            case \"f_rest_25\":\n                return 65 /* PLYValue.SH_25 */;\n            case \"f_rest_26\":\n                return 66 /* PLYValue.SH_26 */;\n            case \"f_rest_27\":\n                return 67 /* PLYValue.SH_27 */;\n            case \"f_rest_28\":\n                return 68 /* PLYValue.SH_28 */;\n            case \"f_rest_29\":\n                return 69 /* PLYValue.SH_29 */;\n            case \"f_rest_30\":\n                return 70 /* PLYValue.SH_30 */;\n            case \"f_rest_31\":\n                return 71 /* PLYValue.SH_31 */;\n            case \"f_rest_32\":\n                return 72 /* PLYValue.SH_32 */;\n            case \"f_rest_33\":\n                return 73 /* PLYValue.SH_33 */;\n            case \"f_rest_34\":\n                return 74 /* PLYValue.SH_34 */;\n            case \"f_rest_35\":\n                return 75 /* PLYValue.SH_35 */;\n            case \"f_rest_36\":\n                return 76 /* PLYValue.SH_36 */;\n            case \"f_rest_37\":\n                return 77 /* PLYValue.SH_37 */;\n            case \"f_rest_38\":\n                return 78 /* PLYValue.SH_38 */;\n            case \"f_rest_39\":\n                return 79 /* PLYValue.SH_39 */;\n            case \"f_rest_40\":\n                return 80 /* PLYValue.SH_40 */;\n            case \"f_rest_41\":\n                return 81 /* PLYValue.SH_41 */;\n            case \"f_rest_42\":\n                return 82 /* PLYValue.SH_42 */;\n            case \"f_rest_43\":\n                return 83 /* PLYValue.SH_43 */;\n            case \"f_rest_44\":\n                return 84 /* PLYValue.SH_44 */;\n        }\n        return 85 /* PLYValue.UNDEFINED */;\n    }\n    /**\n     * Parse a PLY file header and returns metas infos on splats and chunks\n     * @param data the loaded buffer\n     * @returns a PLYHeader\n     */\n    static ParseHeader(data) {\n        const ubuf = new Uint8Array(data);\n        const header = new TextDecoder().decode(ubuf.slice(0, 1024 * 10));\n        const headerEnd = \"end_header\\n\";\n        const headerEndIndex = header.indexOf(headerEnd);\n        if (headerEndIndex < 0 || !header) {\n            // standard splat\n            return null;\n        }\n        const vertexCount = parseInt(/element vertex (\\d+)\\n/.exec(header)[1]);\n        const chunkElement = /element chunk (\\d+)\\n/.exec(header);\n        let chunkCount = 0;\n        if (chunkElement) {\n            chunkCount = parseInt(chunkElement[1]);\n        }\n        let rowVertexOffset = 0;\n        let rowChunkOffset = 0;\n        const offsets = {\n            double: 8,\n            int: 4,\n            uint: 4,\n            float: 4,\n            short: 2,\n            ushort: 2,\n            uchar: 1,\n            list: 0,\n        };\n        let ElementMode;\n        (function (ElementMode) {\n            ElementMode[ElementMode[\"Vertex\"] = 0] = \"Vertex\";\n            ElementMode[ElementMode[\"Chunk\"] = 1] = \"Chunk\";\n        })(ElementMode || (ElementMode = {}));\n        let chunkMode = 1 /* ElementMode.Chunk */;\n        const vertexProperties = [];\n        const chunkProperties = [];\n        const filtered = header.slice(0, headerEndIndex).split(\"\\n\");\n        let shDegree = 0;\n        for (const prop of filtered) {\n            if (prop.startsWith(\"property \")) {\n                const [, typeName, name] = prop.split(\" \");\n                const value = GaussianSplattingMesh._ValueNameToEnum(name);\n                // SH degree 1,2 or 3 for 9, 24 or 45 values\n                if (value >= 84 /* PLYValue.SH_44 */) {\n                    shDegree = 3;\n                }\n                else if (value >= 64 /* PLYValue.SH_24 */) {\n                    shDegree = 2;\n                }\n                else if (value >= 48 /* PLYValue.SH_8 */) {\n                    shDegree = 1;\n                }\n                const type = GaussianSplattingMesh._TypeNameToEnum(typeName);\n                if (chunkMode == 1 /* ElementMode.Chunk */) {\n                    chunkProperties.push({ value, type, offset: rowChunkOffset });\n                    rowChunkOffset += offsets[typeName];\n                }\n                else if (chunkMode == 0 /* ElementMode.Vertex */) {\n                    vertexProperties.push({ value, type, offset: rowVertexOffset });\n                    rowVertexOffset += offsets[typeName];\n                }\n                if (!offsets[typeName]) {\n                    Logger.Warn(`Unsupported property type: ${typeName}.`);\n                }\n            }\n            else if (prop.startsWith(\"element \")) {\n                const [, type] = prop.split(\" \");\n                if (type == \"chunk\") {\n                    chunkMode = 1 /* ElementMode.Chunk */;\n                }\n                else if (type == \"vertex\") {\n                    chunkMode = 0 /* ElementMode.Vertex */;\n                }\n            }\n        }\n        const dataView = new DataView(data, headerEndIndex + headerEnd.length);\n        const buffer = new ArrayBuffer(GaussianSplattingMesh._RowOutputLength * vertexCount);\n        let shBuffer = null;\n        let shCoefficientCount = 0;\n        if (shDegree) {\n            const shVectorCount = (shDegree + 1) * (shDegree + 1) - 1;\n            shCoefficientCount = shVectorCount * 3;\n            shBuffer = new ArrayBuffer(shCoefficientCount * vertexCount);\n        }\n        return {\n            vertexCount: vertexCount,\n            chunkCount: chunkCount,\n            rowVertexLength: rowVertexOffset,\n            rowChunkLength: rowChunkOffset,\n            vertexProperties: vertexProperties,\n            chunkProperties: chunkProperties,\n            dataView: dataView,\n            buffer: buffer,\n            shDegree: shDegree,\n            shCoefficientCount: shCoefficientCount,\n            shBuffer: shBuffer,\n        };\n    }\n    static _GetCompressedChunks(header, offset) {\n        if (!header.chunkCount) {\n            return null;\n        }\n        const dataView = header.dataView;\n        const compressedChunks = new Array(header.chunkCount);\n        for (let i = 0; i < header.chunkCount; i++) {\n            const currentChunk = {\n                min: new Vector3(),\n                max: new Vector3(),\n                minScale: new Vector3(),\n                maxScale: new Vector3(),\n                minColor: new Vector3(0, 0, 0),\n                maxColor: new Vector3(1, 1, 1),\n            };\n            compressedChunks[i] = currentChunk;\n            for (let propertyIndex = 0; propertyIndex < header.chunkProperties.length; propertyIndex++) {\n                const property = header.chunkProperties[propertyIndex];\n                let value;\n                switch (property.type) {\n                    case 0 /* PLYType.FLOAT */:\n                        value = dataView.getFloat32(property.offset + offset.value, true);\n                        break;\n                    default:\n                        continue;\n                }\n                switch (property.value) {\n                    case 0 /* PLYValue.MIN_X */:\n                        currentChunk.min.x = value;\n                        break;\n                    case 1 /* PLYValue.MIN_Y */:\n                        currentChunk.min.y = value;\n                        break;\n                    case 2 /* PLYValue.MIN_Z */:\n                        currentChunk.min.z = value;\n                        break;\n                    case 3 /* PLYValue.MAX_X */:\n                        currentChunk.max.x = value;\n                        break;\n                    case 4 /* PLYValue.MAX_Y */:\n                        currentChunk.max.y = value;\n                        break;\n                    case 5 /* PLYValue.MAX_Z */:\n                        currentChunk.max.z = value;\n                        break;\n                    case 6 /* PLYValue.MIN_SCALE_X */:\n                        currentChunk.minScale.x = value;\n                        break;\n                    case 7 /* PLYValue.MIN_SCALE_Y */:\n                        currentChunk.minScale.y = value;\n                        break;\n                    case 8 /* PLYValue.MIN_SCALE_Z */:\n                        currentChunk.minScale.z = value;\n                        break;\n                    case 9 /* PLYValue.MAX_SCALE_X */:\n                        currentChunk.maxScale.x = value;\n                        break;\n                    case 10 /* PLYValue.MAX_SCALE_Y */:\n                        currentChunk.maxScale.y = value;\n                        break;\n                    case 11 /* PLYValue.MAX_SCALE_Z */:\n                        currentChunk.maxScale.z = value;\n                        break;\n                    case 34 /* PLYValue.MIN_COLOR_R */:\n                        currentChunk.minColor.x = value;\n                        break;\n                    case 35 /* PLYValue.MIN_COLOR_G */:\n                        currentChunk.minColor.y = value;\n                        break;\n                    case 36 /* PLYValue.MIN_COLOR_B */:\n                        currentChunk.minColor.z = value;\n                        break;\n                    case 37 /* PLYValue.MAX_COLOR_R */:\n                        currentChunk.maxColor.x = value;\n                        break;\n                    case 38 /* PLYValue.MAX_COLOR_G */:\n                        currentChunk.maxColor.y = value;\n                        break;\n                    case 39 /* PLYValue.MAX_COLOR_B */:\n                        currentChunk.maxColor.z = value;\n                        break;\n                }\n            }\n            offset.value += header.rowChunkLength;\n        }\n        return compressedChunks;\n    }\n    static _GetSplat(header, index, compressedChunks, offset) {\n        const q = TmpVectors.Quaternion[0];\n        const temp3 = TmpVectors.Vector3[0];\n        const rowOutputLength = GaussianSplattingMesh._RowOutputLength;\n        const buffer = header.buffer;\n        const dataView = header.dataView;\n        const position = new Float32Array(buffer, index * rowOutputLength, 3);\n        const scale = new Float32Array(buffer, index * rowOutputLength + 12, 3);\n        const rgba = new Uint8ClampedArray(buffer, index * rowOutputLength + 24, 4);\n        const rot = new Uint8ClampedArray(buffer, index * rowOutputLength + 28, 4);\n        let sh = null;\n        if (header.shBuffer) {\n            sh = new Uint8ClampedArray(header.shBuffer, index * header.shCoefficientCount, header.shCoefficientCount);\n        }\n        const chunkIndex = index >> 8;\n        let r0 = 255;\n        let r1 = 0;\n        let r2 = 0;\n        let r3 = 0;\n        const plySH = [];\n        for (let propertyIndex = 0; propertyIndex < header.vertexProperties.length; propertyIndex++) {\n            const property = header.vertexProperties[propertyIndex];\n            let value;\n            switch (property.type) {\n                case 0 /* PLYType.FLOAT */:\n                    value = dataView.getFloat32(offset.value + property.offset, true);\n                    break;\n                case 1 /* PLYType.INT */:\n                    value = dataView.getInt32(offset.value + property.offset, true);\n                    break;\n                case 2 /* PLYType.UINT */:\n                    value = dataView.getUint32(offset.value + property.offset, true);\n                    break;\n                case 3 /* PLYType.DOUBLE */:\n                    value = dataView.getFloat64(offset.value + property.offset, true);\n                    break;\n                case 4 /* PLYType.UCHAR */:\n                    value = dataView.getUint8(offset.value + property.offset);\n                    break;\n                default:\n                    continue;\n            }\n            switch (property.value) {\n                case 12 /* PLYValue.PACKED_POSITION */:\n                    {\n                        const compressedChunk = compressedChunks[chunkIndex];\n                        unpack111011(value, temp3);\n                        position[0] = Scalar.Lerp(compressedChunk.min.x, compressedChunk.max.x, temp3.x);\n                        position[1] = -Scalar.Lerp(compressedChunk.min.y, compressedChunk.max.y, temp3.y);\n                        position[2] = Scalar.Lerp(compressedChunk.min.z, compressedChunk.max.z, temp3.z);\n                    }\n                    break;\n                case 13 /* PLYValue.PACKED_ROTATION */:\n                    {\n                        unpackRot(value, q);\n                        r0 = q.w;\n                        r1 = q.z;\n                        r2 = q.y;\n                        r3 = q.x;\n                    }\n                    break;\n                case 14 /* PLYValue.PACKED_SCALE */:\n                    {\n                        const compressedChunk = compressedChunks[chunkIndex];\n                        unpack111011(value, temp3);\n                        scale[0] = Math.exp(Scalar.Lerp(compressedChunk.minScale.x, compressedChunk.maxScale.x, temp3.x));\n                        scale[1] = Math.exp(Scalar.Lerp(compressedChunk.minScale.y, compressedChunk.maxScale.y, temp3.y));\n                        scale[2] = Math.exp(Scalar.Lerp(compressedChunk.minScale.z, compressedChunk.maxScale.z, temp3.z));\n                    }\n                    break;\n                case 15 /* PLYValue.PACKED_COLOR */:\n                    {\n                        const compressedChunk = compressedChunks[chunkIndex];\n                        unpack8888(value, rgba);\n                        rgba[0] = Scalar.Lerp(compressedChunk.minColor.x, compressedChunk.maxColor.x, rgba[0] / 255) * 255;\n                        rgba[1] = Scalar.Lerp(compressedChunk.minColor.y, compressedChunk.maxColor.y, rgba[1] / 255) * 255;\n                        rgba[2] = Scalar.Lerp(compressedChunk.minColor.z, compressedChunk.maxColor.z, rgba[2] / 255) * 255;\n                    }\n                    break;\n                case 16 /* PLYValue.X */:\n                    position[0] = value;\n                    break;\n                case 17 /* PLYValue.Y */:\n                    position[1] = -value;\n                    break;\n                case 18 /* PLYValue.Z */:\n                    position[2] = -value;\n                    break;\n                case 19 /* PLYValue.SCALE_0 */:\n                    scale[0] = Math.exp(value);\n                    break;\n                case 20 /* PLYValue.SCALE_1 */:\n                    scale[1] = Math.exp(value);\n                    break;\n                case 21 /* PLYValue.SCALE_2 */:\n                    scale[2] = Math.exp(value);\n                    break;\n                case 22 /* PLYValue.DIFFUSE_RED */:\n                    rgba[0] = value;\n                    break;\n                case 23 /* PLYValue.DIFFUSE_GREEN */:\n                    rgba[1] = value;\n                    break;\n                case 24 /* PLYValue.DIFFUSE_BLUE */:\n                    rgba[2] = value;\n                    break;\n                case 26 /* PLYValue.F_DC_0 */:\n                    rgba[0] = (0.5 + GaussianSplattingMesh._SH_C0 * value) * 255;\n                    break;\n                case 27 /* PLYValue.F_DC_1 */:\n                    rgba[1] = (0.5 + GaussianSplattingMesh._SH_C0 * value) * 255;\n                    break;\n                case 28 /* PLYValue.F_DC_2 */:\n                    rgba[2] = (0.5 + GaussianSplattingMesh._SH_C0 * value) * 255;\n                    break;\n                case 29 /* PLYValue.F_DC_3 */:\n                    rgba[3] = (0.5 + GaussianSplattingMesh._SH_C0 * value) * 255;\n                    break;\n                case 25 /* PLYValue.OPACITY */:\n                    rgba[3] = (1 / (1 + Math.exp(-value))) * 255;\n                    break;\n                case 30 /* PLYValue.ROT_0 */:\n                    r0 = value;\n                    break;\n                case 31 /* PLYValue.ROT_1 */:\n                    r1 = value;\n                    break;\n                case 32 /* PLYValue.ROT_2 */:\n                    r2 = value;\n                    break;\n                case 33 /* PLYValue.ROT_3 */:\n                    r3 = value;\n                    break;\n            }\n            if (sh && property.value >= 40 /* PLYValue.SH_0 */ && property.value <= 84 /* PLYValue.SH_44 */) {\n                const clampedValue = Scalar.Clamp(value * 127.5 + 127.5, 0, 255);\n                const shIndex = property.value - 40 /* PLYValue.SH_0 */;\n                plySH[shIndex] = clampedValue;\n            }\n        }\n        if (sh) {\n            const shDim = header.shDegree == 1 ? 3 : header.shDegree == 2 ? 8 : 15;\n            for (let j = 0; j < shDim; j++) {\n                sh[j * 3 + 0] = plySH[j];\n                sh[j * 3 + 1] = plySH[j + shDim];\n                sh[j * 3 + 2] = plySH[j + shDim * 2];\n            }\n        }\n        q.set(r1, r2, r3, r0);\n        q.normalize();\n        rot[0] = q.w * 128 + 128;\n        rot[1] = q.x * 128 + 128;\n        rot[2] = q.y * 128 + 128;\n        rot[3] = q.z * 128 + 128;\n        offset.value += header.rowVertexLength;\n    }\n    /**\n     * Converts a .ply data with SH coefficients splat\n     * if data array buffer is not ply, returns the original buffer\n     * @param data the .ply data to load\n     * @param useCoroutine use coroutine and yield\n     * @returns the loaded splat buffer and optional array of sh coefficients\n     */\n    static *ConvertPLYWithSHToSplat(data, useCoroutine = false) {\n        const header = GaussianSplattingMesh.ParseHeader(data);\n        if (!header) {\n            return { buffer: data };\n        }\n        const offset = { value: 0 };\n        const compressedChunks = GaussianSplattingMesh._GetCompressedChunks(header, offset);\n        for (let i = 0; i < header.vertexCount; i++) {\n            GaussianSplattingMesh._GetSplat(header, i, compressedChunks, offset);\n            if (i % GaussianSplattingMesh._PlyConversionBatchSize === 0 && useCoroutine) {\n                yield;\n            }\n        }\n        let sh = null;\n        // make SH texture buffers\n        if (header.shDegree && header.shBuffer) {\n            const textureCount = Math.ceil(header.shCoefficientCount / 16); // 4 components can be stored per texture, 4 sh per component\n            let shIndexRead = 0;\n            const ubuf = new Uint8Array(header.shBuffer);\n            // sh is an array of uint8array that will be used to create sh textures\n            sh = [];\n            const splatCount = header.vertexCount;\n            const engine = EngineStore.LastCreatedEngine;\n            if (engine) {\n                const width = engine.getCaps().maxTextureSize;\n                const height = Math.ceil(splatCount / width);\n                // create array for the number of textures needed.\n                for (let textureIndex = 0; textureIndex < textureCount; textureIndex++) {\n                    const texture = new Uint8Array(height * width * 4 * 4); // 4 components per texture, 4 sh per component\n                    sh.push(texture);\n                }\n                for (let i = 0; i < splatCount; i++) {\n                    for (let shIndexWrite = 0; shIndexWrite < header.shCoefficientCount; shIndexWrite++) {\n                        const shValue = ubuf[shIndexRead++];\n                        const textureIndex = Math.floor(shIndexWrite / 16);\n                        const shArray = sh[textureIndex];\n                        const byteIndexInTexture = shIndexWrite % 16; // [0..15]\n                        const offsetPerSplat = i * 16; // 16 sh values per texture per splat.\n                        shArray[byteIndexInTexture + offsetPerSplat] = shValue;\n                    }\n                }\n            }\n        }\n        return { buffer: header.buffer, sh: sh };\n    }\n    /**\n     * Converts a .ply data array buffer to splat\n     * if data array buffer is not ply, returns the original buffer\n     * @param data the .ply data to load\n     * @param useCoroutine use coroutine and yield\n     * @returns the loaded splat buffer without SH coefficient, whether ply contains or not SH.\n     */\n    static *ConvertPLYToSplat(data, useCoroutine = false) {\n        const header = GaussianSplattingMesh.ParseHeader(data);\n        if (!header) {\n            return data;\n        }\n        const offset = { value: 0 };\n        const compressedChunks = GaussianSplattingMesh._GetCompressedChunks(header, offset);\n        for (let i = 0; i < header.vertexCount; i++) {\n            GaussianSplattingMesh._GetSplat(header, i, compressedChunks, offset);\n            if (i % GaussianSplattingMesh._PlyConversionBatchSize === 0 && useCoroutine) {\n                yield;\n            }\n        }\n        return header.buffer;\n    }\n    /**\n     * Converts a .ply data array buffer to splat\n     * if data array buffer is not ply, returns the original buffer\n     * @param data the .ply data to load\n     * @returns the loaded splat buffer\n     */\n    static async ConvertPLYToSplatAsync(data) {\n        return runCoroutineAsync(GaussianSplattingMesh.ConvertPLYToSplat(data, true), createYieldingScheduler());\n    }\n    /**\n     * Converts a .ply with SH data array buffer to splat\n     * if data array buffer is not ply, returns the original buffer\n     * @param data the .ply data to load\n     * @returns the loaded splat buffer with SH\n     */\n    static async ConvertPLYWithSHToSplatAsync(data) {\n        return runCoroutineAsync(GaussianSplattingMesh.ConvertPLYWithSHToSplat(data, true), createYieldingScheduler());\n    }\n    /**\n     * Loads a .splat Gaussian Splatting array buffer asynchronously\n     * @param data arraybuffer containing splat file\n     * @returns a promise that resolves when the operation is complete\n     */\n    loadDataAsync(data) {\n        return this.updateDataAsync(data);\n    }\n    /**\n     * Loads a .splat Gaussian or .ply Splatting file asynchronously\n     * @param url path to the splat file to load\n     * @returns a promise that resolves when the operation is complete\n     * @deprecated Please use SceneLoader.ImportMeshAsync instead\n     */\n    loadFileAsync(url) {\n        return Tools.LoadFileAsync(url, true).then(async (plyBuffer) => {\n            GaussianSplattingMesh.ConvertPLYWithSHToSplatAsync(plyBuffer).then((splatsData) => {\n                this.updateDataAsync(splatsData.buffer, splatsData.sh);\n            });\n        });\n    }\n    /**\n     * Releases resources associated with this mesh.\n     * @param doNotRecurse Set to true to not recurse into each children (recurse into each children by default)\n     */\n    dispose(doNotRecurse) {\n        this._covariancesATexture?.dispose();\n        this._covariancesBTexture?.dispose();\n        this._centersTexture?.dispose();\n        this._colorsTexture?.dispose();\n        if (this._shTextures) {\n            this._shTextures.forEach((shTexture) => {\n                shTexture.dispose();\n            });\n        }\n        this._covariancesATexture = null;\n        this._covariancesBTexture = null;\n        this._centersTexture = null;\n        this._colorsTexture = null;\n        this._shTextures = null;\n        this._worker?.terminate();\n        this._worker = null;\n        super.dispose(doNotRecurse, true);\n    }\n    _copyTextures(source) {\n        this._covariancesATexture = source.covariancesATexture?.clone();\n        this._covariancesBTexture = source.covariancesBTexture?.clone();\n        this._centersTexture = source.centersTexture?.clone();\n        this._colorsTexture = source.colorsTexture?.clone();\n        if (source._shTextures) {\n            this._shTextures = [];\n            this._shTextures.forEach((shTexture) => {\n                this._shTextures?.push(shTexture.clone());\n            });\n        }\n    }\n    /**\n     * Returns a new Mesh object generated from the current mesh properties.\n     * @param name is a string, the name given to the new mesh\n     * @returns a new Gaussian Splatting Mesh\n     */\n    clone(name = \"\") {\n        const newGS = new GaussianSplattingMesh(name, undefined, this.getScene());\n        newGS._copySource(this);\n        newGS.makeGeometryUnique();\n        newGS._vertexCount = this._vertexCount;\n        newGS._copyTextures(this);\n        newGS._modelViewMatrix = Matrix.Identity();\n        newGS._splatPositions = this._splatPositions;\n        newGS._readyToDisplay = false;\n        newGS._instanciateWorker();\n        const binfo = this.getBoundingInfo();\n        newGS.getBoundingInfo().reConstruct(binfo.minimum, binfo.maximum, this.getWorldMatrix());\n        newGS.forcedInstanceCount = newGS._vertexCount;\n        newGS.setEnabled(true);\n        return newGS;\n    }\n    _makeSplat(index, fBuffer, uBuffer, covA, covB, colorArray, minimum, maximum) {\n        const matrixRotation = TmpVectors.Matrix[0];\n        const matrixScale = TmpVectors.Matrix[1];\n        const quaternion = TmpVectors.Quaternion[0];\n        const covBSItemSize = this._useRGBACovariants ? 4 : 2;\n        const x = fBuffer[8 * index + 0];\n        const y = -fBuffer[8 * index + 1];\n        const z = fBuffer[8 * index + 2];\n        this._splatPositions[4 * index + 0] = x;\n        this._splatPositions[4 * index + 1] = y;\n        this._splatPositions[4 * index + 2] = z;\n        minimum.minimizeInPlaceFromFloats(x, y, z);\n        maximum.maximizeInPlaceFromFloats(x, y, z);\n        quaternion.set((uBuffer[32 * index + 28 + 1] - 127.5) / 127.5, (uBuffer[32 * index + 28 + 2] - 127.5) / 127.5, (uBuffer[32 * index + 28 + 3] - 127.5) / 127.5, -(uBuffer[32 * index + 28 + 0] - 127.5) / 127.5);\n        quaternion.toRotationMatrix(matrixRotation);\n        Matrix.ScalingToRef(fBuffer[8 * index + 3 + 0] * 2, fBuffer[8 * index + 3 + 1] * 2, fBuffer[8 * index + 3 + 2] * 2, matrixScale);\n        const M = matrixRotation.multiplyToRef(matrixScale, TmpVectors.Matrix[0]).m;\n        const covariances = this._tmpCovariances;\n        covariances[0] = M[0] * M[0] + M[1] * M[1] + M[2] * M[2];\n        covariances[1] = M[0] * M[4] + M[1] * M[5] + M[2] * M[6];\n        covariances[2] = M[0] * M[8] + M[1] * M[9] + M[2] * M[10];\n        covariances[3] = M[4] * M[4] + M[5] * M[5] + M[6] * M[6];\n        covariances[4] = M[4] * M[8] + M[5] * M[9] + M[6] * M[10];\n        covariances[5] = M[8] * M[8] + M[9] * M[9] + M[10] * M[10];\n        // normalize covA, covB\n        let factor = -10000;\n        for (let covIndex = 0; covIndex < 6; covIndex++) {\n            factor = Math.max(factor, Math.abs(covariances[covIndex]));\n        }\n        this._splatPositions[4 * index + 3] = factor;\n        const transform = factor;\n        covA[index * 4 + 0] = ToHalfFloat(covariances[0] / transform);\n        covA[index * 4 + 1] = ToHalfFloat(covariances[1] / transform);\n        covA[index * 4 + 2] = ToHalfFloat(covariances[2] / transform);\n        covA[index * 4 + 3] = ToHalfFloat(covariances[3] / transform);\n        covB[index * covBSItemSize + 0] = ToHalfFloat(covariances[4] / transform);\n        covB[index * covBSItemSize + 1] = ToHalfFloat(covariances[5] / transform);\n        // colors\n        colorArray[index * 4 + 0] = uBuffer[32 * index + 24 + 0];\n        colorArray[index * 4 + 1] = uBuffer[32 * index + 24 + 1];\n        colorArray[index * 4 + 2] = uBuffer[32 * index + 24 + 2];\n        colorArray[index * 4 + 3] = uBuffer[32 * index + 24 + 3];\n    }\n    _updateTextures(covA, covB, colorArray, sh) {\n        const textureSize = this._getTextureSize(this._vertexCount);\n        // Update the textures\n        const createTextureFromData = (data, width, height, format) => {\n            return new RawTexture(data, width, height, format, this._scene, false, false, 2, 1);\n        };\n        const createTextureFromDataU8 = (data, width, height, format) => {\n            return new RawTexture(data, width, height, format, this._scene, false, false, 2, 0);\n        };\n        const createTextureFromDataU32 = (data, width, height, format) => {\n            return new RawTexture(data, width, height, format, this._scene, false, false, 1, 7);\n        };\n        const createTextureFromDataF16 = (data, width, height, format) => {\n            return new RawTexture(data, width, height, format, this._scene, false, false, 2, 2);\n        };\n        if (this._covariancesATexture) {\n            this._delayedTextureUpdate = { covA: covA, covB: covB, colors: colorArray, centers: this._splatPositions, sh: sh };\n            const positions = Float32Array.from(this._splatPositions);\n            const vertexCount = this._vertexCount;\n            this._worker.postMessage({ positions, vertexCount }, [positions.buffer]);\n            this._postToWorker(true);\n        }\n        else {\n            this._covariancesATexture = createTextureFromDataF16(covA, textureSize.x, textureSize.y, 5);\n            this._covariancesBTexture = createTextureFromDataF16(covB, textureSize.x, textureSize.y, this._useRGBACovariants ? 5 : 7);\n            this._centersTexture = createTextureFromData(this._splatPositions, textureSize.x, textureSize.y, 5);\n            this._colorsTexture = createTextureFromDataU8(colorArray, textureSize.x, textureSize.y, 5);\n            if (sh) {\n                this._shTextures = [];\n                sh.forEach((shData) => {\n                    const buffer = new Uint32Array(shData.buffer);\n                    const shTexture = createTextureFromDataU32(buffer, textureSize.x, textureSize.y, 11);\n                    shTexture.wrapU = 0;\n                    shTexture.wrapV = 0;\n                    this._shTextures.push(shTexture);\n                });\n            }\n            this._instanciateWorker();\n        }\n    }\n    *_updateData(data, isAsync, sh) {\n        // if a covariance texture is present, then it's not a creation but an update\n        if (!this._covariancesATexture) {\n            this._readyToDisplay = false;\n        }\n        // Parse the data\n        const uBuffer = new Uint8Array(data);\n        const fBuffer = new Float32Array(uBuffer.buffer);\n        if (this._keepInRam) {\n            this._splatsData = data;\n            if (sh) {\n                this._sh = sh;\n            }\n        }\n        const vertexCount = uBuffer.length / GaussianSplattingMesh._RowOutputLength;\n        if (vertexCount != this._vertexCount) {\n            this._updateSplatIndexBuffer(vertexCount);\n        }\n        this._vertexCount = vertexCount;\n        // degree == 1 for 1 texture (3 terms), 2 for 2 textures(8 terms) and 3 for 3 textures (15 terms)\n        this._shDegree = sh ? sh.length : 0;\n        const textureSize = this._getTextureSize(vertexCount);\n        const textureLength = textureSize.x * textureSize.y;\n        const lineCountUpdate = GaussianSplattingMesh.ProgressiveUpdateAmount ?? textureSize.y;\n        const textureLengthPerUpdate = textureSize.x * lineCountUpdate;\n        this._splatPositions = new Float32Array(4 * textureLength);\n        const covA = new Uint16Array(textureLength * 4);\n        const covB = new Uint16Array((this._useRGBACovariants ? 4 : 2) * textureLength);\n        const colorArray = new Uint8Array(textureLength * 4);\n        const minimum = new Vector3(Number.MAX_VALUE, Number.MAX_VALUE, Number.MAX_VALUE);\n        const maximum = new Vector3(-Number.MAX_VALUE, -Number.MAX_VALUE, -Number.MAX_VALUE);\n        if (GaussianSplattingMesh.ProgressiveUpdateAmount) {\n            // create textures with not filled-yet array, then update directly portions of it\n            this._updateTextures(covA, covB, colorArray, sh);\n            this.setEnabled(true);\n            const partCount = Math.ceil(textureSize.y / lineCountUpdate);\n            for (let partIndex = 0; partIndex < partCount; partIndex++) {\n                const updateLine = partIndex * lineCountUpdate;\n                const splatIndexBase = updateLine * textureSize.x;\n                for (let i = 0; i < textureLengthPerUpdate; i++) {\n                    this._makeSplat(splatIndexBase + i, fBuffer, uBuffer, covA, covB, colorArray, minimum, maximum);\n                }\n                this._updateSubTextures(this._splatPositions, covA, covB, colorArray, updateLine, Math.min(lineCountUpdate, textureSize.y - updateLine));\n                // Update the binfo\n                this.getBoundingInfo().reConstruct(minimum, maximum, this.getWorldMatrix());\n                if (isAsync) {\n                    yield;\n                }\n            }\n            // sort will be dirty here as just finished filled positions will not be sorted\n            const positions = Float32Array.from(this._splatPositions);\n            const vertexCount = this._vertexCount;\n            this._worker.postMessage({ positions, vertexCount }, [positions.buffer]);\n            this._sortIsDirty = true;\n        }\n        else {\n            for (let i = 0; i < vertexCount; i++) {\n                this._makeSplat(i, fBuffer, uBuffer, covA, covB, colorArray, minimum, maximum);\n                if (isAsync && i % GaussianSplattingMesh._SplatBatchSize === 0) {\n                    yield;\n                }\n            }\n            // textures\n            this._updateTextures(covA, covB, colorArray, sh);\n            // Update the binfo\n            this.getBoundingInfo().reConstruct(minimum, maximum, this.getWorldMatrix());\n            this.setEnabled(true);\n        }\n        this._postToWorker(true);\n    }\n    /**\n     * Update asynchronously the buffer\n     * @param data array buffer containing center, color, orientation and scale of splats\n     * @param sh optional array of uint8 array for SH data\n     * @returns a promise\n     */\n    async updateDataAsync(data, sh) {\n        return runCoroutineAsync(this._updateData(data, true, sh), createYieldingScheduler());\n    }\n    /**\n     * @experimental\n     * Update data from GS (position, orientation, color, scaling)\n     * @param data array that contain all the datas\n     * @param sh optional array of uint8 array for SH data\n     */\n    updateData(data, sh) {\n        runCoroutineSync(this._updateData(data, false, sh));\n    }\n    /**\n     * Refreshes the bounding info, taking into account all the thin instances defined\n     * @returns the current Gaussian Splatting\n     */\n    refreshBoundingInfo() {\n        this.thinInstanceRefreshBoundingInfo(false);\n        return this;\n    }\n    // in case size is different\n    _updateSplatIndexBuffer(vertexCount) {\n        if (!this._splatIndex || vertexCount > this._splatIndex.length) {\n            this._splatIndex = new Float32Array(vertexCount);\n            this.thinInstanceSetBuffer(\"splatIndex\", this._splatIndex, 1, false);\n        }\n        this.forcedInstanceCount = vertexCount;\n    }\n    _updateSubTextures(centers, covA, covB, colors, lineStart, lineCount, sh) {\n        const updateTextureFromData = (texture, data, width, lineStart, lineCount) => {\n            this.getEngine().updateTextureData(texture.getInternalTexture(), data, 0, lineStart, width, lineCount, 0, 0, false);\n        };\n        const textureSize = this._getTextureSize(this._vertexCount);\n        const covBSItemSize = this._useRGBACovariants ? 4 : 2;\n        const texelStart = lineStart * textureSize.x;\n        const texelCount = lineCount * textureSize.x;\n        const covAView = new Uint16Array(covA.buffer, texelStart * 4 * Uint16Array.BYTES_PER_ELEMENT, texelCount * 4);\n        const covBView = new Uint16Array(covB.buffer, texelStart * covBSItemSize * Uint16Array.BYTES_PER_ELEMENT, texelCount * covBSItemSize);\n        const colorsView = new Uint8Array(colors.buffer, texelStart * 4, texelCount * 4);\n        const centersView = new Float32Array(centers.buffer, texelStart * 4 * Float32Array.BYTES_PER_ELEMENT, texelCount * 4);\n        updateTextureFromData(this._covariancesATexture, covAView, textureSize.x, lineStart, lineCount);\n        updateTextureFromData(this._covariancesBTexture, covBView, textureSize.x, lineStart, lineCount);\n        updateTextureFromData(this._centersTexture, centersView, textureSize.x, lineStart, lineCount);\n        updateTextureFromData(this._colorsTexture, colorsView, textureSize.x, lineStart, lineCount);\n        if (sh) {\n            for (let i = 0; i < sh.length; i++) {\n                const componentCount = 4;\n                const shView = new Uint8Array(this._sh[i].buffer, texelStart * componentCount, texelCount * componentCount);\n                updateTextureFromData(this._shTextures[i], shView, textureSize.x, lineStart, lineCount);\n            }\n        }\n    }\n    _instanciateWorker() {\n        if (!this._vertexCount) {\n            return;\n        }\n        this._updateSplatIndexBuffer(this._vertexCount);\n        // Start the worker thread\n        this._worker?.terminate();\n        this._worker = new Worker(URL.createObjectURL(new Blob([\"(\", GaussianSplattingMesh._CreateWorker.toString(), \")(self)\"], {\n            type: \"application/javascript\",\n        })));\n        this._depthMix = new BigInt64Array(this._vertexCount);\n        const positions = Float32Array.from(this._splatPositions);\n        const vertexCount = this._vertexCount;\n        this._worker.postMessage({ positions, vertexCount }, [positions.buffer]);\n        this._worker.onmessage = (e) => {\n            this._depthMix = e.data.depthMix;\n            const indexMix = new Uint32Array(e.data.depthMix.buffer);\n            if (this._splatIndex) {\n                for (let j = 0; j < this._vertexCount; j++) {\n                    this._splatIndex[j] = indexMix[2 * j];\n                }\n            }\n            if (this._delayedTextureUpdate) {\n                const textureSize = this._getTextureSize(vertexCount);\n                this._updateSubTextures(this._delayedTextureUpdate.centers, this._delayedTextureUpdate.covA, this._delayedTextureUpdate.covB, this._delayedTextureUpdate.colors, 0, textureSize.y, this._delayedTextureUpdate.sh);\n                this._delayedTextureUpdate = null;\n            }\n            this.thinInstanceBufferUpdated(\"splatIndex\");\n            this._canPostToWorker = true;\n            this._readyToDisplay = true;\n            // sort is dirty when GS is visible for progressive update with a this message arriving but positions were partially filled\n            // another update needs to be kicked. The kick can't happen just when the position buffer is ready because _canPostToWorker might be false.\n            if (this._sortIsDirty) {\n                this._postToWorker(true);\n                this._sortIsDirty = false;\n            }\n        };\n    }\n    _getTextureSize(length) {\n        const engine = this._scene.getEngine();\n        const width = engine.getCaps().maxTextureSize;\n        let height = 1;\n        if (engine.version === 1 && !engine.isWebGPU) {\n            while (width * height < length) {\n                height *= 2;\n            }\n        }\n        else {\n            height = Math.ceil(length / width);\n        }\n        if (height > width) {\n            Logger.Error(\"GaussianSplatting texture size: (\" + width + \", \" + height + \"), maxTextureSize: \" + width);\n            height = width;\n        }\n        return new Vector2(width, height);\n    }\n}\nGaussianSplattingMesh._RowOutputLength = 3 * 4 + 3 * 4 + 4 + 4; // Vector3 position, Vector3 scale, 1 u8 quaternion, 1 color with alpha\nGaussianSplattingMesh._SH_C0 = 0.28209479177387814;\n// batch size between 2 yield calls. This value is a tradeoff between updates overhead and framerate hiccups\n// This step is faster the PLY conversion. So batch size can be bigger\nGaussianSplattingMesh._SplatBatchSize = 327680;\n// batch size between 2 yield calls during the PLY to splat conversion.\nGaussianSplattingMesh._PlyConversionBatchSize = 32768;\n/**\n * Set the number of batch (a batch is 16384 splats) after which a display update is performed\n * A value of 0 (default) means display update will not happens before splat is ready.\n */\nGaussianSplattingMesh.ProgressiveUpdateAmount = 0;\nGaussianSplattingMesh._CreateWorker = function (self) {\n    let vertexCount = 0;\n    let positions;\n    let depthMix;\n    let indices;\n    let floatMix;\n    self.onmessage = (e) => {\n        // updated on init\n        if (e.data.positions) {\n            positions = e.data.positions;\n            vertexCount = e.data.vertexCount;\n        }\n        // udpate on view changed\n        else {\n            const viewProj = e.data.view;\n            if (!positions || !viewProj) {\n                // Sanity check, it shouldn't happen!\n                throw new Error(\"positions or view is not defined!\");\n            }\n            depthMix = e.data.depthMix;\n            indices = new Uint32Array(depthMix.buffer);\n            floatMix = new Float32Array(depthMix.buffer);\n            // Sort\n            for (let j = 0; j < vertexCount; j++) {\n                indices[2 * j] = j;\n            }\n            let depthFactor = -1;\n            if (e.data.useRightHandedSystem) {\n                depthFactor = 1;\n            }\n            for (let j = 0; j < vertexCount; j++) {\n                floatMix[2 * j + 1] = 10000 + (viewProj[2] * positions[4 * j + 0] + viewProj[6] * positions[4 * j + 1] + viewProj[10] * positions[4 * j + 2]) * depthFactor;\n            }\n            depthMix.sort();\n            self.postMessage({ depthMix }, [depthMix.buffer]);\n        }\n    };\n};\n//# sourceMappingURL=gaussianSplattingMesh.js.map", "import { Color4, Vector2, Vector3, TmpV<PERSON>s, Quaternion } from \"../Maths/math.js\";\n/**\n * Represents one particle of a points cloud system.\n */\nexport class CloudPoint {\n    /**\n     * Creates a Point Cloud object.\n     * Don't create particles manually, use instead the PCS internal tools like _addParticle()\n     * @param particleIndex (integer) is the particle index in the PCS pool. It's also the particle identifier.\n     * @param group (PointsGroup) is the group the particle belongs to\n     * @param groupId (integer) is the group identifier in the PCS.\n     * @param idxInGroup (integer) is the index of the particle in the current point group (ex: the 10th point of addPoints(30))\n     * @param pcs defines the PCS it is associated to\n     */\n    constructor(particleIndex, group, groupId, idxInGroup, pcs) {\n        /**\n         * particle global index\n         */\n        this.idx = 0;\n        /**\n         * The color of the particle\n         */\n        this.color = new Color4(1.0, 1.0, 1.0, 1.0);\n        /**\n         * The world space position of the particle.\n         */\n        this.position = Vector3.Zero();\n        /**\n         * The world space rotation of the particle. (Not use if rotationQuaternion is set)\n         */\n        this.rotation = Vector3.Zero();\n        /**\n         * The uv of the particle.\n         */\n        this.uv = new Vector2(0.0, 0.0);\n        /**\n         * The current speed of the particle.\n         */\n        this.velocity = Vector3.Zero();\n        /**\n         * The pivot point in the particle local space.\n         */\n        this.pivot = Vector3.Zero();\n        /**\n         * Must the particle be translated from its pivot point in its local space ?\n         * In this case, the pivot point is set at the origin of the particle local space and the particle is translated.\n         * Default : false\n         */\n        this.translateFromPivot = false;\n        /**\n         * Index of this particle in the global \"positions\" array (Internal use)\n         * @internal\n         */\n        this._pos = 0;\n        /**\n         * @internal Index of this particle in the global \"indices\" array (Internal use)\n         */\n        this._ind = 0;\n        /**\n         * Group id of this particle\n         */\n        this.groupId = 0;\n        /**\n         * Index of the particle in its group id (Internal use)\n         */\n        this.idxInGroup = 0;\n        /**\n         * @internal Still set as invisible in order to skip useless computations (Internal use)\n         */\n        this._stillInvisible = false;\n        /**\n         * @internal Last computed particle rotation matrix\n         */\n        this._rotationMatrix = [1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0];\n        /**\n         * Parent particle Id, if any.\n         * Default null.\n         */\n        this.parentId = null;\n        /**\n         * @internal Internal global position in the PCS.\n         */\n        this._globalPosition = Vector3.Zero();\n        this.idx = particleIndex;\n        this._group = group;\n        this.groupId = groupId;\n        this.idxInGroup = idxInGroup;\n        this._pcs = pcs;\n    }\n    /**\n     * get point size\n     */\n    get size() {\n        return this.size;\n    }\n    /**\n     * Set point size\n     */\n    set size(scale) {\n        this.size = scale;\n    }\n    /**\n     * Legacy support, changed quaternion to rotationQuaternion\n     */\n    get quaternion() {\n        return this.rotationQuaternion;\n    }\n    /**\n     * Legacy support, changed quaternion to rotationQuaternion\n     */\n    set quaternion(q) {\n        this.rotationQuaternion = q;\n    }\n    /**\n     * Returns a boolean. True if the particle intersects a mesh, else false\n     * The intersection is computed on the particle position and Axis Aligned Bounding Box (AABB) or Sphere\n     * @param target is the object (point or mesh) what the intersection is computed against\n     * @param isSphere is boolean flag when false (default) bounding box of mesh is used, when true the bounding sphere is used\n     * @returns true if it intersects\n     */\n    intersectsMesh(target, isSphere) {\n        if (!target.hasBoundingInfo) {\n            return false;\n        }\n        if (!this._pcs.mesh) {\n            throw new Error(\"Point Cloud System doesnt contain the Mesh\");\n        }\n        if (isSphere) {\n            return target.getBoundingInfo().boundingSphere.intersectsPoint(this.position.add(this._pcs.mesh.position));\n        }\n        const bbox = target.getBoundingInfo().boundingBox;\n        const maxX = bbox.maximumWorld.x;\n        const minX = bbox.minimumWorld.x;\n        const maxY = bbox.maximumWorld.y;\n        const minY = bbox.minimumWorld.y;\n        const maxZ = bbox.maximumWorld.z;\n        const minZ = bbox.minimumWorld.z;\n        const x = this.position.x + this._pcs.mesh.position.x;\n        const y = this.position.y + this._pcs.mesh.position.y;\n        const z = this.position.z + this._pcs.mesh.position.z;\n        return minX <= x && x <= maxX && minY <= y && y <= maxY && minZ <= z && z <= maxZ;\n    }\n    /**\n     * get the rotation matrix of the particle\n     * @internal\n     */\n    getRotationMatrix(m) {\n        let quaternion;\n        if (this.rotationQuaternion) {\n            quaternion = this.rotationQuaternion;\n        }\n        else {\n            quaternion = TmpVectors.Quaternion[0];\n            const rotation = this.rotation;\n            Quaternion.RotationYawPitchRollToRef(rotation.y, rotation.x, rotation.z, quaternion);\n        }\n        quaternion.toRotationMatrix(m);\n    }\n}\n/**\n * Represents a group of points in a points cloud system\n *  * PCS internal tool, don't use it manually.\n */\nexport class PointsGroup {\n    /**\n     * Get or set the groupId\n     * @deprecated Please use groupId instead\n     */\n    get groupID() {\n        return this.groupId;\n    }\n    set groupID(groupID) {\n        this.groupId = groupID;\n    }\n    /**\n     * Creates a points group object. This is an internal reference to produce particles for the PCS.\n     * PCS internal tool, don't use it manually.\n     * @internal\n     */\n    constructor(id, posFunction) {\n        this.groupId = id;\n        this._positionFunction = posFunction;\n    }\n}\n//# sourceMappingURL=cloudPoint.js.map", "import { Color4, Color3 } from \"../Maths/math.js\";\nimport { Vector2, Vector3, Vector4, TmpVectors, Matrix } from \"../Maths/math.vector.js\";\nimport { Logger } from \"../Misc/logger.js\";\nimport { VertexBuffer } from \"../Buffers/buffer.js\";\nimport { VertexData } from \"../Meshes/mesh.vertexData.js\";\nimport { Mesh } from \"../Meshes/mesh.js\";\nimport { EngineStore } from \"../Engines/engineStore.js\";\nimport { CloudPoint, PointsGroup } from \"./cloudPoint.js\";\nimport { Ray } from \"../Culling/ray.js\";\nimport { StandardMaterial } from \"../Materials/standardMaterial.js\";\nimport { BaseTexture } from \"./../Materials/Textures/baseTexture.js\";\nimport { RandomRange } from \"../Maths/math.scalar.functions.js\";\n/** Defines the 4 color options */\nexport var PointColor;\n(function (PointColor) {\n    /** color value */\n    PointColor[PointColor[\"Color\"] = 2] = \"Color\";\n    /** uv value */\n    PointColor[PointColor[\"UV\"] = 1] = \"UV\";\n    /** random value */\n    PointColor[PointColor[\"Random\"] = 0] = \"Random\";\n    /** stated value */\n    PointColor[PointColor[\"Stated\"] = 3] = \"Stated\";\n})(PointColor || (PointColor = {}));\n/**\n * The PointCloudSystem (PCS) is a single updatable mesh. The points corresponding to the vertices of this big mesh.\n * As it is just a mesh, the PointCloudSystem has all the same properties as any other BJS mesh : not more, not less. It can be scaled, rotated, translated, enlighted, textured, moved, etc.\n\n * The PointCloudSystem is also a particle system, with each point being a particle. It provides some methods to manage the particles.\n * However it is behavior agnostic. This means it has no emitter, no particle physics, no particle recycler. You have to implement your own behavior.\n *\n * Full documentation here : TO BE ENTERED\n */\nexport class PointsCloudSystem {\n    /**\n     * Gets the particle positions computed by the Point Cloud System\n     */\n    get positions() {\n        return this._positions32;\n    }\n    /**\n     * Gets the particle colors computed by the Point Cloud System\n     */\n    get colors() {\n        return this._colors32;\n    }\n    /**\n     * Gets the particle uvs computed by the Point Cloud System\n     */\n    get uvs() {\n        return this._uvs32;\n    }\n    /**\n     * Creates a PCS (Points Cloud System) object\n     * @param name (String) is the PCS name, this will be the underlying mesh name\n     * @param pointSize (number) is the size for each point. Has no effect on a WebGPU engine.\n     * @param scene (Scene) is the scene in which the PCS is added\n     * @param options defines the options of the PCS e.g.\n     * * updatable (optional boolean, default true) : if the PCS must be updatable or immutable\n     */\n    constructor(name, pointSize, scene, options) {\n        /**\n         *  The PCS array of cloud point objects. Just access each particle as with any classic array.\n         *  Example : var p = SPS.particles[i];\n         */\n        this.particles = new Array();\n        /**\n         * The PCS total number of particles. Read only. Use PCS.counter instead if you need to set your own value.\n         */\n        this.nbParticles = 0;\n        /**\n         * This a counter for your own usage. It's not set by any SPS functions.\n         */\n        this.counter = 0;\n        /**\n         * This empty object is intended to store some PCS specific or temporary values in order to lower the Garbage Collector activity.\n         * Please read :\n         */\n        this.vars = {};\n        this._promises = [];\n        this._positions = new Array();\n        this._indices = new Array();\n        this._normals = new Array();\n        this._colors = new Array();\n        this._uvs = new Array();\n        this._updatable = true;\n        this._isVisibilityBoxLocked = false;\n        this._alwaysVisible = false;\n        this._groups = new Array(); //start indices for each group of particles\n        this._groupCounter = 0;\n        this._computeParticleColor = true;\n        this._computeParticleTexture = true;\n        this._computeParticleRotation = true;\n        this._computeBoundingBox = false;\n        this._isReady = false;\n        this.name = name;\n        this._size = pointSize;\n        this._scene = scene || EngineStore.LastCreatedScene;\n        if (options && options.updatable !== undefined) {\n            this._updatable = options.updatable;\n        }\n        else {\n            this._updatable = true;\n        }\n    }\n    /**\n     * Builds the PCS underlying mesh. Returns a standard Mesh.\n     * If no points were added to the PCS, the returned mesh is just a single point.\n     * @param material The material to use to render the mesh. If not provided, will create a default one\n     * @returns a promise for the created mesh\n     */\n    buildMeshAsync(material) {\n        return Promise.all(this._promises).then(() => {\n            this._isReady = true;\n            return this._buildMesh(material);\n        });\n    }\n    /**\n     * @internal\n     */\n    _buildMesh(material) {\n        if (this.nbParticles === 0) {\n            this.addPoints(1);\n        }\n        this._positions32 = new Float32Array(this._positions);\n        this._uvs32 = new Float32Array(this._uvs);\n        this._colors32 = new Float32Array(this._colors);\n        const vertexData = new VertexData();\n        vertexData.set(this._positions32, VertexBuffer.PositionKind);\n        if (this._uvs32.length > 0) {\n            vertexData.set(this._uvs32, VertexBuffer.UVKind);\n        }\n        let ec = 0; //emissive color value 0 for UVs, 1 for color\n        if (this._colors32.length > 0) {\n            ec = 1;\n            vertexData.set(this._colors32, VertexBuffer.ColorKind);\n        }\n        const mesh = new Mesh(this.name, this._scene);\n        vertexData.applyToMesh(mesh, this._updatable);\n        this.mesh = mesh;\n        // free memory\n        this._positions = null;\n        this._uvs = null;\n        this._colors = null;\n        if (!this._updatable) {\n            this.particles.length = 0;\n        }\n        let mat = material;\n        if (!mat) {\n            mat = new StandardMaterial(\"point cloud material\", this._scene);\n            mat.emissiveColor = new Color3(ec, ec, ec);\n            mat.disableLighting = true;\n            mat.pointsCloud = true;\n            mat.pointSize = this._size;\n        }\n        mesh.material = mat;\n        return new Promise((resolve) => resolve(mesh));\n    }\n    // adds a new particle object in the particles array\n    _addParticle(idx, group, groupId, idxInGroup) {\n        const cp = new CloudPoint(idx, group, groupId, idxInGroup, this);\n        this.particles.push(cp);\n        return cp;\n    }\n    _randomUnitVector(particle) {\n        particle.position = new Vector3(Math.random(), Math.random(), Math.random());\n        particle.color = new Color4(1, 1, 1, 1);\n    }\n    _getColorIndicesForCoord(pointsGroup, x, y, width) {\n        const imageData = pointsGroup._groupImageData;\n        const color = y * (width * 4) + x * 4;\n        const colorIndices = [color, color + 1, color + 2, color + 3];\n        const redIndex = colorIndices[0];\n        const greenIndex = colorIndices[1];\n        const blueIndex = colorIndices[2];\n        const alphaIndex = colorIndices[3];\n        const redForCoord = imageData[redIndex];\n        const greenForCoord = imageData[greenIndex];\n        const blueForCoord = imageData[blueIndex];\n        const alphaForCoord = imageData[alphaIndex];\n        return new Color4(redForCoord / 255, greenForCoord / 255, blueForCoord / 255, alphaForCoord);\n    }\n    _setPointsColorOrUV(mesh, pointsGroup, isVolume, colorFromTexture, hasTexture, color, range, uvSetIndex) {\n        uvSetIndex = uvSetIndex ?? 0;\n        if (isVolume) {\n            mesh.updateFacetData();\n        }\n        const boundInfo = mesh.getBoundingInfo();\n        const diameter = 2 * boundInfo.boundingSphere.radius;\n        let meshPos = mesh.getVerticesData(VertexBuffer.PositionKind);\n        const meshInd = mesh.getIndices();\n        const meshUV = mesh.getVerticesData(VertexBuffer.UVKind + (uvSetIndex ? uvSetIndex + 1 : \"\"));\n        const meshCol = mesh.getVerticesData(VertexBuffer.ColorKind);\n        const place = Vector3.Zero();\n        mesh.computeWorldMatrix();\n        const meshMatrix = mesh.getWorldMatrix();\n        if (!meshMatrix.isIdentity()) {\n            meshPos = meshPos.slice(0);\n            for (let p = 0; p < meshPos.length / 3; p++) {\n                Vector3.TransformCoordinatesFromFloatsToRef(meshPos[3 * p], meshPos[3 * p + 1], meshPos[3 * p + 2], meshMatrix, place);\n                meshPos[3 * p] = place.x;\n                meshPos[3 * p + 1] = place.y;\n                meshPos[3 * p + 2] = place.z;\n            }\n        }\n        let idxPoints = 0;\n        let id0 = 0;\n        let id1 = 0;\n        let id2 = 0;\n        let v0X = 0;\n        let v0Y = 0;\n        let v0Z = 0;\n        let v1X = 0;\n        let v1Y = 0;\n        let v1Z = 0;\n        let v2X = 0;\n        let v2Y = 0;\n        let v2Z = 0;\n        const vertex0 = Vector3.Zero();\n        const vertex1 = Vector3.Zero();\n        const vertex2 = Vector3.Zero();\n        const vec0 = Vector3.Zero();\n        const vec1 = Vector3.Zero();\n        let uv0X = 0;\n        let uv0Y = 0;\n        let uv1X = 0;\n        let uv1Y = 0;\n        let uv2X = 0;\n        let uv2Y = 0;\n        const uv0 = Vector2.Zero();\n        const uv1 = Vector2.Zero();\n        const uv2 = Vector2.Zero();\n        const uvec0 = Vector2.Zero();\n        const uvec1 = Vector2.Zero();\n        let col0X = 0;\n        let col0Y = 0;\n        let col0Z = 0;\n        let col0A = 0;\n        let col1X = 0;\n        let col1Y = 0;\n        let col1Z = 0;\n        let col1A = 0;\n        let col2X = 0;\n        let col2Y = 0;\n        let col2Z = 0;\n        let col2A = 0;\n        const col0 = Vector4.Zero();\n        const col1 = Vector4.Zero();\n        const col2 = Vector4.Zero();\n        const colvec0 = Vector4.Zero();\n        const colvec1 = Vector4.Zero();\n        let lamda = 0;\n        let mu = 0;\n        range = range ? range : 0;\n        let facetPoint;\n        let uvPoint;\n        let colPoint = new Vector4(0, 0, 0, 0);\n        let norm = Vector3.Zero();\n        let tang = Vector3.Zero();\n        let biNorm = Vector3.Zero();\n        let angle = 0;\n        let facetPlaneVec = Vector3.Zero();\n        let gap = 0;\n        let distance = 0;\n        const ray = new Ray(Vector3.Zero(), new Vector3(1, 0, 0));\n        let pickInfo;\n        let direction = Vector3.Zero();\n        for (let index = 0; index < meshInd.length / 3; index++) {\n            id0 = meshInd[3 * index];\n            id1 = meshInd[3 * index + 1];\n            id2 = meshInd[3 * index + 2];\n            v0X = meshPos[3 * id0];\n            v0Y = meshPos[3 * id0 + 1];\n            v0Z = meshPos[3 * id0 + 2];\n            v1X = meshPos[3 * id1];\n            v1Y = meshPos[3 * id1 + 1];\n            v1Z = meshPos[3 * id1 + 2];\n            v2X = meshPos[3 * id2];\n            v2Y = meshPos[3 * id2 + 1];\n            v2Z = meshPos[3 * id2 + 2];\n            vertex0.set(v0X, v0Y, v0Z);\n            vertex1.set(v1X, v1Y, v1Z);\n            vertex2.set(v2X, v2Y, v2Z);\n            vertex1.subtractToRef(vertex0, vec0);\n            vertex2.subtractToRef(vertex1, vec1);\n            if (meshUV) {\n                uv0X = meshUV[2 * id0];\n                uv0Y = meshUV[2 * id0 + 1];\n                uv1X = meshUV[2 * id1];\n                uv1Y = meshUV[2 * id1 + 1];\n                uv2X = meshUV[2 * id2];\n                uv2Y = meshUV[2 * id2 + 1];\n                uv0.set(uv0X, uv0Y);\n                uv1.set(uv1X, uv1Y);\n                uv2.set(uv2X, uv2Y);\n                uv1.subtractToRef(uv0, uvec0);\n                uv2.subtractToRef(uv1, uvec1);\n            }\n            if (meshCol && colorFromTexture) {\n                col0X = meshCol[4 * id0];\n                col0Y = meshCol[4 * id0 + 1];\n                col0Z = meshCol[4 * id0 + 2];\n                col0A = meshCol[4 * id0 + 3];\n                col1X = meshCol[4 * id1];\n                col1Y = meshCol[4 * id1 + 1];\n                col1Z = meshCol[4 * id1 + 2];\n                col1A = meshCol[4 * id1 + 3];\n                col2X = meshCol[4 * id2];\n                col2Y = meshCol[4 * id2 + 1];\n                col2Z = meshCol[4 * id2 + 2];\n                col2A = meshCol[4 * id2 + 3];\n                col0.set(col0X, col0Y, col0Z, col0A);\n                col1.set(col1X, col1Y, col1Z, col1A);\n                col2.set(col2X, col2Y, col2Z, col2A);\n                col1.subtractToRef(col0, colvec0);\n                col2.subtractToRef(col1, colvec1);\n            }\n            let width;\n            let height;\n            let deltaS;\n            let deltaV;\n            let h;\n            let s;\n            let v;\n            let hsvCol;\n            const statedColor = new Color3(0, 0, 0);\n            const colPoint3 = new Color3(0, 0, 0);\n            let pointColors;\n            let particle;\n            for (let i = 0; i < pointsGroup._groupDensity[index]; i++) {\n                idxPoints = this.particles.length;\n                this._addParticle(idxPoints, pointsGroup, this._groupCounter, index + i);\n                particle = this.particles[idxPoints];\n                //form a point inside the facet v0, v1, v2;\n                lamda = Math.sqrt(RandomRange(0, 1));\n                mu = RandomRange(0, 1);\n                facetPoint = vertex0.add(vec0.scale(lamda)).add(vec1.scale(lamda * mu));\n                if (isVolume) {\n                    norm = mesh.getFacetNormal(index).normalize().scale(-1);\n                    tang = vec0.clone().normalize();\n                    biNorm = Vector3.Cross(norm, tang);\n                    angle = RandomRange(0, 2 * Math.PI);\n                    facetPlaneVec = tang.scale(Math.cos(angle)).add(biNorm.scale(Math.sin(angle)));\n                    angle = RandomRange(0.1, Math.PI / 2);\n                    direction = facetPlaneVec.scale(Math.cos(angle)).add(norm.scale(Math.sin(angle)));\n                    ray.origin = facetPoint.add(direction.scale(0.00001));\n                    ray.direction = direction;\n                    ray.length = diameter;\n                    pickInfo = ray.intersectsMesh(mesh);\n                    if (pickInfo.hit) {\n                        distance = pickInfo.pickedPoint.subtract(facetPoint).length();\n                        gap = RandomRange(0, 1) * distance;\n                        facetPoint.addInPlace(direction.scale(gap));\n                    }\n                }\n                particle.position = facetPoint.clone();\n                this._positions.push(particle.position.x, particle.position.y, particle.position.z);\n                if (colorFromTexture !== undefined) {\n                    if (meshUV) {\n                        uvPoint = uv0.add(uvec0.scale(lamda)).add(uvec1.scale(lamda * mu));\n                        if (colorFromTexture) {\n                            //Set particle color to texture color\n                            if (hasTexture && pointsGroup._groupImageData !== null) {\n                                width = pointsGroup._groupImgWidth;\n                                height = pointsGroup._groupImgHeight;\n                                pointColors = this._getColorIndicesForCoord(pointsGroup, Math.round(uvPoint.x * width), Math.round(uvPoint.y * height), width);\n                                particle.color = pointColors;\n                                this._colors.push(pointColors.r, pointColors.g, pointColors.b, pointColors.a);\n                            }\n                            else {\n                                if (meshCol) {\n                                    //failure in texture and colors available\n                                    colPoint = col0.add(colvec0.scale(lamda)).add(colvec1.scale(lamda * mu));\n                                    particle.color = new Color4(colPoint.x, colPoint.y, colPoint.z, colPoint.w);\n                                    this._colors.push(colPoint.x, colPoint.y, colPoint.z, colPoint.w);\n                                }\n                                else {\n                                    colPoint = col0.set(Math.random(), Math.random(), Math.random(), 1);\n                                    particle.color = new Color4(colPoint.x, colPoint.y, colPoint.z, colPoint.w);\n                                    this._colors.push(colPoint.x, colPoint.y, colPoint.z, colPoint.w);\n                                }\n                            }\n                        }\n                        else {\n                            //Set particle uv based on a mesh uv\n                            particle.uv = uvPoint.clone();\n                            this._uvs.push(particle.uv.x, particle.uv.y);\n                        }\n                    }\n                }\n                else {\n                    if (color) {\n                        statedColor.set(color.r, color.g, color.b);\n                        deltaS = RandomRange(-range, range);\n                        deltaV = RandomRange(-range, range);\n                        hsvCol = statedColor.toHSV();\n                        h = hsvCol.r;\n                        s = hsvCol.g + deltaS;\n                        v = hsvCol.b + deltaV;\n                        if (s < 0) {\n                            s = 0;\n                        }\n                        if (s > 1) {\n                            s = 1;\n                        }\n                        if (v < 0) {\n                            v = 0;\n                        }\n                        if (v > 1) {\n                            v = 1;\n                        }\n                        Color3.HSVtoRGBToRef(h, s, v, colPoint3);\n                        colPoint.set(colPoint3.r, colPoint3.g, colPoint3.b, 1);\n                    }\n                    else {\n                        colPoint = col0.set(Math.random(), Math.random(), Math.random(), 1);\n                    }\n                    particle.color = new Color4(colPoint.x, colPoint.y, colPoint.z, colPoint.w);\n                    this._colors.push(colPoint.x, colPoint.y, colPoint.z, colPoint.w);\n                }\n            }\n        }\n    }\n    // stores mesh texture in dynamic texture for color pixel retrieval\n    // when pointColor type is color for surface points\n    _colorFromTexture(mesh, pointsGroup, isVolume) {\n        if (mesh.material === null) {\n            Logger.Warn(mesh.name + \"has no material.\");\n            pointsGroup._groupImageData = null;\n            this._setPointsColorOrUV(mesh, pointsGroup, isVolume, true, false);\n            return;\n        }\n        const mat = mesh.material;\n        const textureList = mat.getActiveTextures();\n        if (textureList.length === 0) {\n            Logger.Warn(mesh.name + \"has no usable texture.\");\n            pointsGroup._groupImageData = null;\n            this._setPointsColorOrUV(mesh, pointsGroup, isVolume, true, false);\n            return;\n        }\n        const clone = mesh.clone();\n        clone.setEnabled(false);\n        this._promises.push(new Promise((resolve) => {\n            BaseTexture.WhenAllReady(textureList, () => {\n                let n = pointsGroup._textureNb;\n                if (n < 0) {\n                    n = 0;\n                }\n                if (n > textureList.length - 1) {\n                    n = textureList.length - 1;\n                }\n                const finalize = () => {\n                    pointsGroup._groupImgWidth = textureList[n].getSize().width;\n                    pointsGroup._groupImgHeight = textureList[n].getSize().height;\n                    this._setPointsColorOrUV(clone, pointsGroup, isVolume, true, true, undefined, undefined, textureList[n].coordinatesIndex);\n                    clone.dispose();\n                    resolve();\n                };\n                pointsGroup._groupImageData = null;\n                const dataPromise = textureList[n].readPixels();\n                if (!dataPromise) {\n                    finalize();\n                }\n                else {\n                    dataPromise.then((data) => {\n                        pointsGroup._groupImageData = data;\n                        finalize();\n                    });\n                }\n            });\n        }));\n    }\n    // calculates the point density per facet of a mesh for surface points\n    _calculateDensity(nbPoints, positions, indices) {\n        let id0;\n        let id1;\n        let id2;\n        let v0X;\n        let v0Y;\n        let v0Z;\n        let v1X;\n        let v1Y;\n        let v1Z;\n        let v2X;\n        let v2Y;\n        let v2Z;\n        const vertex0 = Vector3.Zero();\n        const vertex1 = Vector3.Zero();\n        const vertex2 = Vector3.Zero();\n        const vec0 = Vector3.Zero();\n        const vec1 = Vector3.Zero();\n        const normal = Vector3.Zero();\n        let area;\n        const cumulativeAreas = [];\n        let surfaceArea = 0;\n        const nbFacets = indices.length / 3;\n        //surface area\n        for (let index = 0; index < nbFacets; index++) {\n            id0 = indices[3 * index];\n            id1 = indices[3 * index + 1];\n            id2 = indices[3 * index + 2];\n            v0X = positions[3 * id0];\n            v0Y = positions[3 * id0 + 1];\n            v0Z = positions[3 * id0 + 2];\n            v1X = positions[3 * id1];\n            v1Y = positions[3 * id1 + 1];\n            v1Z = positions[3 * id1 + 2];\n            v2X = positions[3 * id2];\n            v2Y = positions[3 * id2 + 1];\n            v2Z = positions[3 * id2 + 2];\n            vertex0.set(v0X, v0Y, v0Z);\n            vertex1.set(v1X, v1Y, v1Z);\n            vertex2.set(v2X, v2Y, v2Z);\n            vertex1.subtractToRef(vertex0, vec0);\n            vertex2.subtractToRef(vertex1, vec1);\n            Vector3.CrossToRef(vec0, vec1, normal);\n            area = 0.5 * normal.length();\n            surfaceArea += area;\n            cumulativeAreas[index] = surfaceArea;\n        }\n        const density = new Array(nbFacets);\n        let remainingPoints = nbPoints;\n        for (let index = nbFacets - 1; index > 0; index--) {\n            const cumulativeArea = cumulativeAreas[index];\n            if (cumulativeArea === 0) {\n                // avoiding division by 0 upon degenerate triangles\n                density[index] = 0;\n            }\n            else {\n                const area = cumulativeArea - cumulativeAreas[index - 1];\n                const facetPointsWithFraction = (area / cumulativeArea) * remainingPoints;\n                const floored = Math.floor(facetPointsWithFraction);\n                const fraction = facetPointsWithFraction - floored;\n                const extraPoint = Number(Math.random() < fraction);\n                const facetPoints = floored + extraPoint;\n                density[index] = facetPoints;\n                remainingPoints -= facetPoints;\n            }\n        }\n        density[0] = remainingPoints;\n        return density;\n    }\n    /**\n     * Adds points to the PCS in random positions within a unit sphere\n     * @param nb (positive integer) the number of particles to be created from this model\n     * @param pointFunction is an optional javascript function to be called for each particle on PCS creation\n     * @returns the number of groups in the system\n     */\n    addPoints(nb, pointFunction = this._randomUnitVector) {\n        const pointsGroup = new PointsGroup(this._groupCounter, pointFunction);\n        let cp;\n        // particles\n        let idx = this.nbParticles;\n        for (let i = 0; i < nb; i++) {\n            cp = this._addParticle(idx, pointsGroup, this._groupCounter, i);\n            if (pointsGroup && pointsGroup._positionFunction) {\n                pointsGroup._positionFunction(cp, idx, i);\n            }\n            this._positions.push(cp.position.x, cp.position.y, cp.position.z);\n            if (cp.color) {\n                this._colors.push(cp.color.r, cp.color.g, cp.color.b, cp.color.a);\n            }\n            if (cp.uv) {\n                this._uvs.push(cp.uv.x, cp.uv.y);\n            }\n            idx++;\n        }\n        this.nbParticles += nb;\n        this._groupCounter++;\n        return this._groupCounter;\n    }\n    /**\n     * Adds points to the PCS from the surface of the model shape\n     * @param mesh is any Mesh object that will be used as a surface model for the points\n     * @param nb (positive integer) the number of particles to be created from this model\n     * @param colorWith determines whether a point is colored using color (default), uv, random, stated or none (invisible)\n     * @param color (color4) to be used when colorWith is stated or color (number) when used to specify texture position\n     * @param range (number from 0 to 1) to determine the variation in shape and tone for a stated color\n     * @returns the number of groups in the system\n     */\n    addSurfacePoints(mesh, nb, colorWith, color, range) {\n        let colored = colorWith ? colorWith : 0 /* PointColor.Random */;\n        if (isNaN(colored) || colored < 0 || colored > 3) {\n            colored = 0 /* PointColor.Random */;\n        }\n        const meshPos = mesh.getVerticesData(VertexBuffer.PositionKind);\n        const meshInd = mesh.getIndices();\n        this._groups.push(this._groupCounter);\n        const pointsGroup = new PointsGroup(this._groupCounter, null);\n        pointsGroup._groupDensity = this._calculateDensity(nb, meshPos, meshInd);\n        if (colored === 2 /* PointColor.Color */) {\n            pointsGroup._textureNb = color ? color : 0;\n        }\n        else {\n            color = color ? color : new Color4(1, 1, 1, 1);\n        }\n        switch (colored) {\n            case 2 /* PointColor.Color */:\n                this._colorFromTexture(mesh, pointsGroup, false);\n                break;\n            case 1 /* PointColor.UV */:\n                this._setPointsColorOrUV(mesh, pointsGroup, false, false, false);\n                break;\n            case 0 /* PointColor.Random */:\n                this._setPointsColorOrUV(mesh, pointsGroup, false);\n                break;\n            case 3 /* PointColor.Stated */:\n                this._setPointsColorOrUV(mesh, pointsGroup, false, undefined, undefined, color, range);\n                break;\n        }\n        this.nbParticles += nb;\n        this._groupCounter++;\n        return this._groupCounter - 1;\n    }\n    /**\n     * Adds points to the PCS inside the model shape\n     * @param mesh is any Mesh object that will be used as a surface model for the points\n     * @param nb (positive integer) the number of particles to be created from this model\n     * @param colorWith determines whether a point is colored using color (default), uv, random, stated or none (invisible)\n     * @param color (color4) to be used when colorWith is stated or color (number) when used to specify texture position\n     * @param range (number from 0 to 1) to determine the variation in shape and tone for a stated color\n     * @returns the number of groups in the system\n     */\n    addVolumePoints(mesh, nb, colorWith, color, range) {\n        let colored = colorWith ? colorWith : 0 /* PointColor.Random */;\n        if (isNaN(colored) || colored < 0 || colored > 3) {\n            colored = 0 /* PointColor.Random */;\n        }\n        const meshPos = mesh.getVerticesData(VertexBuffer.PositionKind);\n        const meshInd = mesh.getIndices();\n        this._groups.push(this._groupCounter);\n        const pointsGroup = new PointsGroup(this._groupCounter, null);\n        pointsGroup._groupDensity = this._calculateDensity(nb, meshPos, meshInd);\n        if (colored === 2 /* PointColor.Color */) {\n            pointsGroup._textureNb = color ? color : 0;\n        }\n        else {\n            color = color ? color : new Color4(1, 1, 1, 1);\n        }\n        switch (colored) {\n            case 2 /* PointColor.Color */:\n                this._colorFromTexture(mesh, pointsGroup, true);\n                break;\n            case 1 /* PointColor.UV */:\n                this._setPointsColorOrUV(mesh, pointsGroup, true, false, false);\n                break;\n            case 0 /* PointColor.Random */:\n                this._setPointsColorOrUV(mesh, pointsGroup, true);\n                break;\n            case 3 /* PointColor.Stated */:\n                this._setPointsColorOrUV(mesh, pointsGroup, true, undefined, undefined, color, range);\n                break;\n        }\n        this.nbParticles += nb;\n        this._groupCounter++;\n        return this._groupCounter - 1;\n    }\n    /**\n     *  Sets all the particles : this method actually really updates the mesh according to the particle positions, rotations, colors, textures, etc.\n     *  This method calls `updateParticle()` for each particle of the SPS.\n     *  For an animated SPS, it is usually called within the render loop.\n     * @param start The particle index in the particle array where to start to compute the particle property values _(default 0)_\n     * @param end The particle index in the particle array where to stop to compute the particle property values _(default nbParticle - 1)_\n     * @param update If the mesh must be finally updated on this call after all the particle computations _(default true)_\n     * @returns the PCS.\n     */\n    setParticles(start = 0, end = this.nbParticles - 1, update = true) {\n        if (!this._updatable || !this._isReady) {\n            return this;\n        }\n        // custom beforeUpdate\n        this.beforeUpdateParticles(start, end, update);\n        const rotMatrix = TmpVectors.Matrix[0];\n        const mesh = this.mesh;\n        const colors32 = this._colors32;\n        const positions32 = this._positions32;\n        const uvs32 = this._uvs32;\n        const tempVectors = TmpVectors.Vector3;\n        const camAxisX = tempVectors[5].copyFromFloats(1.0, 0.0, 0.0);\n        const camAxisY = tempVectors[6].copyFromFloats(0.0, 1.0, 0.0);\n        const camAxisZ = tempVectors[7].copyFromFloats(0.0, 0.0, 1.0);\n        const minimum = tempVectors[8].setAll(Number.MAX_VALUE);\n        const maximum = tempVectors[9].setAll(-Number.MAX_VALUE);\n        Matrix.IdentityToRef(rotMatrix);\n        let idx = 0; // current index of the particle\n        if (this.mesh?.isFacetDataEnabled) {\n            this._computeBoundingBox = true;\n        }\n        end = end >= this.nbParticles ? this.nbParticles - 1 : end;\n        if (this._computeBoundingBox) {\n            if (start != 0 || end != this.nbParticles - 1) {\n                // only some particles are updated, then use the current existing BBox basis. Note : it can only increase.\n                const boundingInfo = this.mesh?.getBoundingInfo();\n                if (boundingInfo) {\n                    minimum.copyFrom(boundingInfo.minimum);\n                    maximum.copyFrom(boundingInfo.maximum);\n                }\n            }\n        }\n        idx = 0; // particle index\n        let pindex = 0; //index in positions array\n        let cindex = 0; //index in color array\n        let uindex = 0; //index in uv array\n        // particle loop\n        for (let p = start; p <= end; p++) {\n            const particle = this.particles[p];\n            idx = particle.idx;\n            pindex = 3 * idx;\n            cindex = 4 * idx;\n            uindex = 2 * idx;\n            // call to custom user function to update the particle properties\n            this.updateParticle(particle);\n            const particleRotationMatrix = particle._rotationMatrix;\n            const particlePosition = particle.position;\n            const particleGlobalPosition = particle._globalPosition;\n            if (this._computeParticleRotation) {\n                particle.getRotationMatrix(rotMatrix);\n            }\n            const particleHasParent = particle.parentId !== null;\n            if (particleHasParent) {\n                const parent = this.particles[particle.parentId];\n                const parentRotationMatrix = parent._rotationMatrix;\n                const parentGlobalPosition = parent._globalPosition;\n                const rotatedY = particlePosition.x * parentRotationMatrix[1] + particlePosition.y * parentRotationMatrix[4] + particlePosition.z * parentRotationMatrix[7];\n                const rotatedX = particlePosition.x * parentRotationMatrix[0] + particlePosition.y * parentRotationMatrix[3] + particlePosition.z * parentRotationMatrix[6];\n                const rotatedZ = particlePosition.x * parentRotationMatrix[2] + particlePosition.y * parentRotationMatrix[5] + particlePosition.z * parentRotationMatrix[8];\n                particleGlobalPosition.x = parentGlobalPosition.x + rotatedX;\n                particleGlobalPosition.y = parentGlobalPosition.y + rotatedY;\n                particleGlobalPosition.z = parentGlobalPosition.z + rotatedZ;\n                if (this._computeParticleRotation) {\n                    const rotMatrixValues = rotMatrix.m;\n                    particleRotationMatrix[0] =\n                        rotMatrixValues[0] * parentRotationMatrix[0] + rotMatrixValues[1] * parentRotationMatrix[3] + rotMatrixValues[2] * parentRotationMatrix[6];\n                    particleRotationMatrix[1] =\n                        rotMatrixValues[0] * parentRotationMatrix[1] + rotMatrixValues[1] * parentRotationMatrix[4] + rotMatrixValues[2] * parentRotationMatrix[7];\n                    particleRotationMatrix[2] =\n                        rotMatrixValues[0] * parentRotationMatrix[2] + rotMatrixValues[1] * parentRotationMatrix[5] + rotMatrixValues[2] * parentRotationMatrix[8];\n                    particleRotationMatrix[3] =\n                        rotMatrixValues[4] * parentRotationMatrix[0] + rotMatrixValues[5] * parentRotationMatrix[3] + rotMatrixValues[6] * parentRotationMatrix[6];\n                    particleRotationMatrix[4] =\n                        rotMatrixValues[4] * parentRotationMatrix[1] + rotMatrixValues[5] * parentRotationMatrix[4] + rotMatrixValues[6] * parentRotationMatrix[7];\n                    particleRotationMatrix[5] =\n                        rotMatrixValues[4] * parentRotationMatrix[2] + rotMatrixValues[5] * parentRotationMatrix[5] + rotMatrixValues[6] * parentRotationMatrix[8];\n                    particleRotationMatrix[6] =\n                        rotMatrixValues[8] * parentRotationMatrix[0] + rotMatrixValues[9] * parentRotationMatrix[3] + rotMatrixValues[10] * parentRotationMatrix[6];\n                    particleRotationMatrix[7] =\n                        rotMatrixValues[8] * parentRotationMatrix[1] + rotMatrixValues[9] * parentRotationMatrix[4] + rotMatrixValues[10] * parentRotationMatrix[7];\n                    particleRotationMatrix[8] =\n                        rotMatrixValues[8] * parentRotationMatrix[2] + rotMatrixValues[9] * parentRotationMatrix[5] + rotMatrixValues[10] * parentRotationMatrix[8];\n                }\n            }\n            else {\n                particleGlobalPosition.x = 0;\n                particleGlobalPosition.y = 0;\n                particleGlobalPosition.z = 0;\n                if (this._computeParticleRotation) {\n                    const rotMatrixValues = rotMatrix.m;\n                    particleRotationMatrix[0] = rotMatrixValues[0];\n                    particleRotationMatrix[1] = rotMatrixValues[1];\n                    particleRotationMatrix[2] = rotMatrixValues[2];\n                    particleRotationMatrix[3] = rotMatrixValues[4];\n                    particleRotationMatrix[4] = rotMatrixValues[5];\n                    particleRotationMatrix[5] = rotMatrixValues[6];\n                    particleRotationMatrix[6] = rotMatrixValues[8];\n                    particleRotationMatrix[7] = rotMatrixValues[9];\n                    particleRotationMatrix[8] = rotMatrixValues[10];\n                }\n            }\n            const pivotBackTranslation = tempVectors[11];\n            if (particle.translateFromPivot) {\n                pivotBackTranslation.setAll(0.0);\n            }\n            else {\n                pivotBackTranslation.copyFrom(particle.pivot);\n            }\n            // positions\n            const tmpVertex = tempVectors[0];\n            tmpVertex.copyFrom(particle.position);\n            const vertexX = tmpVertex.x - particle.pivot.x;\n            const vertexY = tmpVertex.y - particle.pivot.y;\n            const vertexZ = tmpVertex.z - particle.pivot.z;\n            let rotatedX = vertexX * particleRotationMatrix[0] + vertexY * particleRotationMatrix[3] + vertexZ * particleRotationMatrix[6];\n            let rotatedY = vertexX * particleRotationMatrix[1] + vertexY * particleRotationMatrix[4] + vertexZ * particleRotationMatrix[7];\n            let rotatedZ = vertexX * particleRotationMatrix[2] + vertexY * particleRotationMatrix[5] + vertexZ * particleRotationMatrix[8];\n            rotatedX += pivotBackTranslation.x;\n            rotatedY += pivotBackTranslation.y;\n            rotatedZ += pivotBackTranslation.z;\n            const px = (positions32[pindex] = particleGlobalPosition.x + camAxisX.x * rotatedX + camAxisY.x * rotatedY + camAxisZ.x * rotatedZ);\n            const py = (positions32[pindex + 1] = particleGlobalPosition.y + camAxisX.y * rotatedX + camAxisY.y * rotatedY + camAxisZ.y * rotatedZ);\n            const pz = (positions32[pindex + 2] = particleGlobalPosition.z + camAxisX.z * rotatedX + camAxisY.z * rotatedY + camAxisZ.z * rotatedZ);\n            if (this._computeBoundingBox) {\n                minimum.minimizeInPlaceFromFloats(px, py, pz);\n                maximum.maximizeInPlaceFromFloats(px, py, pz);\n            }\n            if (this._computeParticleColor && particle.color) {\n                const color = particle.color;\n                const colors32 = this._colors32;\n                colors32[cindex] = color.r;\n                colors32[cindex + 1] = color.g;\n                colors32[cindex + 2] = color.b;\n                colors32[cindex + 3] = color.a;\n            }\n            if (this._computeParticleTexture && particle.uv) {\n                const uv = particle.uv;\n                const uvs32 = this._uvs32;\n                uvs32[uindex] = uv.x;\n                uvs32[uindex + 1] = uv.y;\n            }\n        }\n        // if the VBO must be updated\n        if (mesh) {\n            if (update) {\n                if (this._computeParticleColor) {\n                    mesh.updateVerticesData(VertexBuffer.ColorKind, colors32, false, false);\n                }\n                if (this._computeParticleTexture) {\n                    mesh.updateVerticesData(VertexBuffer.UVKind, uvs32, false, false);\n                }\n                mesh.updateVerticesData(VertexBuffer.PositionKind, positions32, false, false);\n            }\n            if (this._computeBoundingBox) {\n                if (mesh.hasBoundingInfo) {\n                    mesh.getBoundingInfo().reConstruct(minimum, maximum, mesh._worldMatrix);\n                }\n                else {\n                    mesh.buildBoundingInfo(minimum, maximum, mesh._worldMatrix);\n                }\n            }\n        }\n        this.afterUpdateParticles(start, end, update);\n        return this;\n    }\n    /**\n     * Disposes the PCS.\n     */\n    dispose() {\n        this.mesh?.dispose();\n        this.vars = null;\n        // drop references to internal big arrays for the GC\n        this._positions = null;\n        this._indices = null;\n        this._normals = null;\n        this._uvs = null;\n        this._colors = null;\n        this._indices32 = null;\n        this._positions32 = null;\n        this._uvs32 = null;\n        this._colors32 = null;\n    }\n    /**\n     * Visibility helper : Recomputes the visible size according to the mesh bounding box\n     * doc :\n     * @returns the PCS.\n     */\n    refreshVisibleSize() {\n        if (!this._isVisibilityBoxLocked) {\n            this.mesh?.refreshBoundingInfo();\n        }\n        return this;\n    }\n    /**\n     * Visibility helper : Sets the size of a visibility box, this sets the underlying mesh bounding box.\n     * @param size the size (float) of the visibility box\n     * note : this doesn't lock the PCS mesh bounding box.\n     * doc :\n     */\n    setVisibilityBox(size) {\n        if (!this.mesh) {\n            return;\n        }\n        const vis = size / 2;\n        this.mesh.buildBoundingInfo(new Vector3(-vis, -vis, -vis), new Vector3(vis, vis, vis));\n    }\n    /**\n     * Gets whether the PCS is always visible or not\n     * doc :\n     */\n    get isAlwaysVisible() {\n        return this._alwaysVisible;\n    }\n    /**\n     * Sets the PCS as always visible or not\n     * doc :\n     */\n    set isAlwaysVisible(val) {\n        if (!this.mesh) {\n            return;\n        }\n        this._alwaysVisible = val;\n        this.mesh.alwaysSelectAsActiveMesh = val;\n    }\n    /**\n     * Tells to `setParticles()` to compute the particle rotations or not\n     * Default value : false. The PCS is faster when it's set to false\n     * Note : particle rotations are only applied to parent particles\n     * Note : the particle rotations aren't stored values, so setting `computeParticleRotation` to false will prevents the particle to rotate\n     */\n    set computeParticleRotation(val) {\n        this._computeParticleRotation = val;\n    }\n    /**\n     * Tells to `setParticles()` to compute the particle colors or not.\n     * Default value : true. The PCS is faster when it's set to false.\n     * Note : the particle colors are stored values, so setting `computeParticleColor` to false will keep yet the last colors set.\n     */\n    set computeParticleColor(val) {\n        this._computeParticleColor = val;\n    }\n    set computeParticleTexture(val) {\n        this._computeParticleTexture = val;\n    }\n    /**\n     * Gets if `setParticles()` computes the particle colors or not.\n     * Default value : false. The PCS is faster when it's set to false.\n     * Note : the particle colors are stored values, so setting `computeParticleColor` to false will keep yet the last colors set.\n     */\n    get computeParticleColor() {\n        return this._computeParticleColor;\n    }\n    /**\n     * Gets if `setParticles()` computes the particle textures or not.\n     * Default value : false. The PCS is faster when it's set to false.\n     * Note : the particle textures are stored values, so setting `computeParticleTexture` to false will keep yet the last colors set.\n     */\n    get computeParticleTexture() {\n        return this._computeParticleTexture;\n    }\n    /**\n     * Tells to `setParticles()` to compute or not the mesh bounding box when computing the particle positions.\n     */\n    set computeBoundingBox(val) {\n        this._computeBoundingBox = val;\n    }\n    /**\n     * Gets if `setParticles()` computes or not the mesh bounding box when computing the particle positions.\n     */\n    get computeBoundingBox() {\n        return this._computeBoundingBox;\n    }\n    // =======================================================================\n    // Particle behavior logic\n    // these following methods may be overwritten by users to fit their needs\n    /**\n     * This function does nothing. It may be overwritten to set all the particle first values.\n     * The PCS doesn't call this function, you may have to call it by your own.\n     * doc :\n     */\n    initParticles() { }\n    /**\n     * This function does nothing. It may be overwritten to recycle a particle\n     * The PCS doesn't call this function, you can to call it\n     * doc :\n     * @param particle The particle to recycle\n     * @returns the recycled particle\n     */\n    recycleParticle(particle) {\n        return particle;\n    }\n    /**\n     * Updates a particle : this function should  be overwritten by the user.\n     * It is called on each particle by `setParticles()`. This is the place to code each particle behavior.\n     * doc :\n     * @example : just set a particle position or velocity and recycle conditions\n     * @param particle The particle to update\n     * @returns the updated particle\n     */\n    updateParticle(particle) {\n        return particle;\n    }\n    /**\n     * This will be called before any other treatment by `setParticles()` and will be passed three parameters.\n     * This does nothing and may be overwritten by the user.\n     * @param start the particle index in the particle array where to start to iterate, same than the value passed to setParticle()\n     * @param stop the particle index in the particle array where to stop to iterate, same than the value passed to setParticle()\n     * @param update the boolean update value actually passed to setParticles()\n     */\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    beforeUpdateParticles(start, stop, update) { }\n    /**\n     * This will be called  by `setParticles()` after all the other treatments and just before the actual mesh update.\n     * This will be passed three parameters.\n     * This does nothing and may be overwritten by the user.\n     * @param start the particle index in the particle array where to start to iterate, same than the value passed to setParticle()\n     * @param stop the particle index in the particle array where to stop to iterate, same than the value passed to setParticle()\n     * @param update the boolean update value actually passed to setParticles()\n     */\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    afterUpdateParticles(start, stop, update) { }\n}\n//# sourceMappingURL=pointsCloudSystem.js.map", "import { RegisterSceneLoaderPlugin } from \"@babylonjs/core/Loading/sceneLoader.js\";\nimport { SPLATFileLoaderMetadata } from \"./splatFileLoader.metadata.js\";\nimport { GaussianSplattingMesh } from \"@babylonjs/core/Meshes/GaussianSplatting/gaussianSplattingMesh.js\";\nimport { AssetContainer } from \"@babylonjs/core/assetContainer.js\";\nimport { Mesh } from \"@babylonjs/core/Meshes/mesh.js\";\nimport { Logger } from \"@babylonjs/core/Misc/logger.js\";\nimport { Vector3 } from \"@babylonjs/core/Maths/math.vector.js\";\nimport { PointsCloudSystem } from \"@babylonjs/core/Particles/pointsCloudSystem.js\";\nimport { Color4 } from \"@babylonjs/core/Maths/math.color.js\";\nimport { VertexData } from \"@babylonjs/core/Meshes/mesh.vertexData.js\";\nimport { Scalar } from \"@babylonjs/core/Maths/math.scalar.js\";\n/**\n * Indicator of the parsed ply buffer. A standard ready to use splat or an array of positions for a point cloud\n */\nvar Mode;\n(function (Mode) {\n    Mode[Mode[\"Splat\"] = 0] = \"Splat\";\n    Mode[Mode[\"PointCloud\"] = 1] = \"PointCloud\";\n    Mode[Mode[\"Mesh\"] = 2] = \"Mesh\";\n    Mode[Mode[\"Reject\"] = 3] = \"Reject\";\n})(Mode || (Mode = {}));\n/**\n * @experimental\n * SPLAT file type loader.\n * This is a babylon scene loader plugin.\n */\nexport class SPLATFileLoader {\n    /**\n     * Creates loader for gaussian splatting files\n     * @param loadingOptions options for loading and parsing splat and PLY files.\n     */\n    constructor(loadingOptions = SPLATFileLoader._DefaultLoadingOptions) {\n        /**\n         * Defines the name of the plugin.\n         */\n        this.name = SPLATFileLoaderMetadata.name;\n        this._assetContainer = null;\n        /**\n         * Defines the extensions the splat loader is able to load.\n         * force data to come in as an ArrayBuffer\n         */\n        this.extensions = SPLATFileLoaderMetadata.extensions;\n        this._loadingOptions = loadingOptions;\n    }\n    /** @internal */\n    createPlugin(options) {\n        return new SPLATFileLoader(options[SPLATFileLoaderMetadata.name]);\n    }\n    /**\n     * Imports  from the loaded gaussian splatting data and adds them to the scene\n     * @param meshesNames a string or array of strings of the mesh names that should be loaded from the file\n     * @param scene the scene the meshes should be added to\n     * @param data the gaussian splatting data to load\n     * @param rootUrl root url to load from\n     * @param onProgress callback called while file is loading\n     * @param fileName Defines the name of the file to load\n     * @returns a promise containing the loaded meshes, particles, skeletons and animations\n     */\n    async importMeshAsync(meshesNames, scene, data, rootUrl, onProgress, fileName) {\n        return this._parse(meshesNames, scene, data, rootUrl).then((meshes) => {\n            return {\n                meshes: meshes,\n                particleSystems: [],\n                skeletons: [],\n                animationGroups: [],\n                transformNodes: [],\n                geometries: [],\n                lights: [],\n                spriteManagers: [],\n            };\n        });\n    }\n    static _BuildPointCloud(pointcloud, data) {\n        if (!data.byteLength) {\n            return false;\n        }\n        const uBuffer = new Uint8Array(data);\n        const fBuffer = new Float32Array(data);\n        // parsed array contains room for position(3floats), normal(3floats), color (4b), quantized quaternion (4b)\n        const rowLength = 3 * 4 + 3 * 4 + 4 + 4;\n        const vertexCount = uBuffer.length / rowLength;\n        const pointcloudfunc = function (particle, i) {\n            const x = fBuffer[8 * i + 0];\n            const y = fBuffer[8 * i + 1];\n            const z = fBuffer[8 * i + 2];\n            particle.position = new Vector3(x, y, z);\n            const r = uBuffer[rowLength * i + 24 + 0] / 255;\n            const g = uBuffer[rowLength * i + 24 + 1] / 255;\n            const b = uBuffer[rowLength * i + 24 + 2] / 255;\n            particle.color = new Color4(r, g, b, 1);\n        };\n        pointcloud.addPoints(vertexCount, pointcloudfunc);\n        return true;\n    }\n    static _BuildMesh(scene, parsedPLY) {\n        const mesh = new Mesh(\"PLYMesh\", scene);\n        const uBuffer = new Uint8Array(parsedPLY.data);\n        const fBuffer = new Float32Array(parsedPLY.data);\n        const rowLength = 3 * 4 + 3 * 4 + 4 + 4;\n        const vertexCount = uBuffer.length / rowLength;\n        const positions = [];\n        const vertexData = new VertexData();\n        for (let i = 0; i < vertexCount; i++) {\n            const x = fBuffer[8 * i + 0];\n            const y = fBuffer[8 * i + 1];\n            const z = fBuffer[8 * i + 2];\n            positions.push(x, y, z);\n        }\n        if (parsedPLY.hasVertexColors) {\n            const colors = new Float32Array(vertexCount * 4);\n            for (let i = 0; i < vertexCount; i++) {\n                const r = uBuffer[rowLength * i + 24 + 0] / 255;\n                const g = uBuffer[rowLength * i + 24 + 1] / 255;\n                const b = uBuffer[rowLength * i + 24 + 2] / 255;\n                colors[i * 4 + 0] = r;\n                colors[i * 4 + 1] = g;\n                colors[i * 4 + 2] = b;\n                colors[i * 4 + 3] = 1;\n            }\n            vertexData.colors = colors;\n        }\n        vertexData.positions = positions;\n        vertexData.indices = parsedPLY.faces;\n        vertexData.applyToMesh(mesh);\n        return mesh;\n    }\n    _parseSPZ(data, scene) {\n        const ubuf = new Uint8Array(data);\n        const ubufu32 = new Uint32Array(data.slice(0, 12)); // Only need ubufu32[0] to [2]\n        // debug infos\n        const splatCount = ubufu32[2];\n        const shDegree = ubuf[12];\n        const fractionalBits = ubuf[13];\n        //const flags = ubuf[14];\n        const reserved = ubuf[15];\n        // check magic and version\n        if (reserved || ubufu32[0] != 0x5053474e || ubufu32[1] != 2) {\n            // reserved must be 0\n            return new Promise((resolve) => {\n                resolve({ mode: 3 /* Mode.Reject */, data: buffer, hasVertexColors: false });\n            });\n        }\n        const rowOutputLength = 3 * 4 + 3 * 4 + 4 + 4; // 32\n        const buffer = new ArrayBuffer(rowOutputLength * splatCount);\n        const positionScale = 1.0 / (1 << fractionalBits);\n        const int32View = new Int32Array(1);\n        const uint8View = new Uint8Array(int32View.buffer);\n        const read24bComponent = function (u8, offset) {\n            uint8View[0] = u8[offset + 0];\n            uint8View[1] = u8[offset + 1];\n            uint8View[2] = u8[offset + 2];\n            uint8View[3] = u8[offset + 2] & 0x80 ? 0xff : 0x00;\n            return int32View[0] * positionScale;\n        };\n        let byteOffset = 16;\n        const position = new Float32Array(buffer);\n        const scale = new Float32Array(buffer);\n        const rgba = new Uint8ClampedArray(buffer);\n        const rot = new Uint8ClampedArray(buffer);\n        let coordinateSign = 1;\n        let quaternionOffset = 0;\n        if (!this._loadingOptions.flipY) {\n            coordinateSign = -1;\n            quaternionOffset = 255;\n        }\n        // positions\n        for (let i = 0; i < splatCount; i++) {\n            position[i * 8 + 0] = read24bComponent(ubuf, byteOffset + 0);\n            position[i * 8 + 1] = coordinateSign * read24bComponent(ubuf, byteOffset + 3);\n            position[i * 8 + 2] = coordinateSign * read24bComponent(ubuf, byteOffset + 6);\n            byteOffset += 9;\n        }\n        // colors\n        const SH_C0 = 0.282;\n        for (let i = 0; i < splatCount; i++) {\n            for (let component = 0; component < 3; component++) {\n                const byteValue = ubuf[byteOffset + splatCount + i * 3 + component];\n                // 0.15 is hard coded value from spz\n                // Scale factor for DC color components. To convert to RGB, we should multiply by 0.282, but it can\n                // be useful to represent base colors that are out of range if the higher spherical harmonics bands\n                // bring them back into range so we multiply by a smaller value.\n                const value = (byteValue - 127.5) / (0.15 * 255);\n                rgba[i * 32 + 24 + component] = Scalar.Clamp((0.5 + SH_C0 * value) * 255, 0, 255);\n            }\n            rgba[i * 32 + 24 + 3] = ubuf[byteOffset + i];\n        }\n        byteOffset += splatCount * 4;\n        // scales\n        for (let i = 0; i < splatCount; i++) {\n            scale[i * 8 + 3 + 0] = Math.exp(ubuf[byteOffset + 0] / 16.0 - 10.0);\n            scale[i * 8 + 3 + 1] = Math.exp(ubuf[byteOffset + 1] / 16.0 - 10.0);\n            scale[i * 8 + 3 + 2] = Math.exp(ubuf[byteOffset + 2] / 16.0 - 10.0);\n            byteOffset += 3;\n        }\n        // convert quaternion\n        for (let i = 0; i < splatCount; i++) {\n            const x = ubuf[byteOffset + 0];\n            const y = ubuf[byteOffset + 1] * coordinateSign + quaternionOffset;\n            const z = ubuf[byteOffset + 2] * coordinateSign + quaternionOffset;\n            const nx = x / 127.5 - 1;\n            const ny = y / 127.5 - 1;\n            const nz = z / 127.5 - 1;\n            rot[i * 32 + 28 + 1] = x;\n            rot[i * 32 + 28 + 2] = y;\n            rot[i * 32 + 28 + 3] = z;\n            const v = 1 - (nx * nx + ny * ny + nz * nz);\n            rot[i * 32 + 28 + 0] = 127.5 + Math.sqrt(v < 0 ? 0 : v) * 127.5;\n            byteOffset += 3;\n        }\n        //SH\n        if (shDegree) {\n            // shVectorCount is : 3 for dim = 1, 8 for dim = 2 and 15 for dim = 3\n            // number of vec3 vector needed per splat\n            const shVectorCount = (shDegree + 1) * (shDegree + 1) - 1; // minus 1 because sh0 is color\n            // number of component values : 3 per vector3 (45)\n            const shComponentCount = shVectorCount * 3;\n            const textureCount = Math.ceil(shComponentCount / 16); // 4 components can be stored per texture, 4 sh per component\n            let shIndexRead = byteOffset;\n            // sh is an array of uint8array that will be used to create sh textures\n            const sh = [];\n            const engine = scene.getEngine();\n            const width = engine.getCaps().maxTextureSize;\n            const height = Math.ceil(splatCount / width);\n            // create array for the number of textures needed.\n            for (let textureIndex = 0; textureIndex < textureCount; textureIndex++) {\n                const texture = new Uint8Array(height * width * 4 * 4); // 4 components per texture, 4 sh per component\n                sh.push(texture);\n            }\n            for (let i = 0; i < splatCount; i++) {\n                for (let shIndexWrite = 0; shIndexWrite < shComponentCount; shIndexWrite++) {\n                    const shValue = ubuf[shIndexRead++];\n                    const textureIndex = Math.floor(shIndexWrite / 16);\n                    const shArray = sh[textureIndex];\n                    const byteIndexInTexture = shIndexWrite % 16; // [0..15]\n                    const offsetPerSplat = i * 16; // 16 sh values per texture per splat.\n                    shArray[byteIndexInTexture + offsetPerSplat] = shValue;\n                }\n            }\n            return new Promise((resolve) => {\n                resolve({ mode: 0 /* Mode.Splat */, data: buffer, hasVertexColors: false, sh: sh });\n            });\n        }\n        return new Promise((resolve) => {\n            resolve({ mode: 0 /* Mode.Splat */, data: buffer, hasVertexColors: false });\n        });\n    }\n    _parse(meshesNames, scene, data, rootUrl) {\n        const babylonMeshesArray = []; //The mesh for babylon\n        const readableStream = new ReadableStream({\n            start(controller) {\n                controller.enqueue(new Uint8Array(data)); // Enqueue the ArrayBuffer as a Uint8Array\n                controller.close();\n            },\n        });\n        // Use GZip DecompressionStream\n        const decompressionStream = new DecompressionStream(\"gzip\");\n        const decompressedStream = readableStream.pipeThrough(decompressionStream);\n        return new Promise((resolve) => {\n            new Response(decompressedStream)\n                .arrayBuffer()\n                .then((buffer) => {\n                this._parseSPZ(buffer, scene).then((parsedSPZ) => {\n                    scene._blockEntityCollection = !!this._assetContainer;\n                    const gaussianSplatting = new GaussianSplattingMesh(\"GaussianSplatting\", null, scene, this._loadingOptions.keepInRam);\n                    gaussianSplatting._parentContainer = this._assetContainer;\n                    babylonMeshesArray.push(gaussianSplatting);\n                    gaussianSplatting.updateData(parsedSPZ.data, parsedSPZ.sh);\n                    scene._blockEntityCollection = false;\n                    resolve(babylonMeshesArray);\n                });\n            })\n                .catch(() => {\n                // Catch any decompression errors\n                SPLATFileLoader._ConvertPLYToSplat(data).then(async (parsedPLY) => {\n                    scene._blockEntityCollection = !!this._assetContainer;\n                    switch (parsedPLY.mode) {\n                        case 0 /* Mode.Splat */:\n                            {\n                                const gaussianSplatting = new GaussianSplattingMesh(\"GaussianSplatting\", null, scene, this._loadingOptions.keepInRam);\n                                gaussianSplatting._parentContainer = this._assetContainer;\n                                babylonMeshesArray.push(gaussianSplatting);\n                                gaussianSplatting.updateData(parsedPLY.data, parsedPLY.sh);\n                            }\n                            break;\n                        case 1 /* Mode.PointCloud */:\n                            {\n                                const pointcloud = new PointsCloudSystem(\"PointCloud\", 1, scene);\n                                if (SPLATFileLoader._BuildPointCloud(pointcloud, parsedPLY.data)) {\n                                    await pointcloud.buildMeshAsync().then((mesh) => {\n                                        babylonMeshesArray.push(mesh);\n                                    });\n                                }\n                                else {\n                                    pointcloud.dispose();\n                                }\n                            }\n                            break;\n                        case 2 /* Mode.Mesh */:\n                            {\n                                if (parsedPLY.faces) {\n                                    babylonMeshesArray.push(SPLATFileLoader._BuildMesh(scene, parsedPLY));\n                                }\n                                else {\n                                    throw new Error(\"PLY mesh doesn't contain face informations.\");\n                                }\n                            }\n                            break;\n                        default:\n                            throw new Error(\"Unsupported Splat mode\");\n                    }\n                    scene._blockEntityCollection = false;\n                    resolve(babylonMeshesArray);\n                });\n            });\n        });\n    }\n    /**\n     * Load into an asset container.\n     * @param scene The scene to load into\n     * @param data The data to import\n     * @param rootUrl The root url for scene and resources\n     * @returns The loaded asset container\n     */\n    loadAssetContainerAsync(scene, data, rootUrl) {\n        const container = new AssetContainer(scene);\n        this._assetContainer = container;\n        return this.importMeshAsync(null, scene, data, rootUrl)\n            .then((result) => {\n            result.meshes.forEach((mesh) => container.meshes.push(mesh));\n            // mesh material will be null before 1st rendered frame.\n            this._assetContainer = null;\n            return container;\n        })\n            .catch((ex) => {\n            this._assetContainer = null;\n            throw ex;\n        });\n    }\n    /**\n     * Imports all objects from the loaded OBJ data and adds them to the scene\n     * @param scene the scene the objects should be added to\n     * @param data the OBJ data to load\n     * @param rootUrl root url to load from\n     * @returns a promise which completes when objects have been loaded to the scene\n     */\n    loadAsync(scene, data, rootUrl) {\n        //Get the 3D model\n        return this.importMeshAsync(null, scene, data, rootUrl).then(() => {\n            // return void\n        });\n    }\n    /**\n     * Code from https://github.com/dylanebert/gsplat.js/blob/main/src/loaders/PLYLoader.ts Under MIT license\n     * Converts a .ply data array buffer to splat\n     * if data array buffer is not ply, returns the original buffer\n     * @param data the .ply data to load\n     * @returns the loaded splat buffer\n     */\n    static _ConvertPLYToSplat(data) {\n        const ubuf = new Uint8Array(data);\n        const header = new TextDecoder().decode(ubuf.slice(0, 1024 * 10));\n        const headerEnd = \"end_header\\n\";\n        const headerEndIndex = header.indexOf(headerEnd);\n        if (headerEndIndex < 0 || !header) {\n            // standard splat\n            return new Promise((resolve) => {\n                resolve({ mode: 0 /* Mode.Splat */, data: data });\n            });\n        }\n        const vertexCount = parseInt(/element vertex (\\d+)\\n/.exec(header)[1]);\n        const faceElement = /element face (\\d+)\\n/.exec(header);\n        let faceCount = 0;\n        if (faceElement) {\n            faceCount = parseInt(faceElement[1]);\n        }\n        const chunkElement = /element chunk (\\d+)\\n/.exec(header);\n        let chunkCount = 0;\n        if (chunkElement) {\n            chunkCount = parseInt(chunkElement[1]);\n        }\n        let rowVertexOffset = 0;\n        let rowChunkOffset = 0;\n        const offsets = {\n            double: 8,\n            int: 4,\n            uint: 4,\n            float: 4,\n            short: 2,\n            ushort: 2,\n            uchar: 1,\n            list: 0,\n        };\n        let ElementMode;\n        (function (ElementMode) {\n            ElementMode[ElementMode[\"Vertex\"] = 0] = \"Vertex\";\n            ElementMode[ElementMode[\"Chunk\"] = 1] = \"Chunk\";\n        })(ElementMode || (ElementMode = {}));\n        let chunkMode = 1 /* ElementMode.Chunk */;\n        const vertexProperties = [];\n        const chunkProperties = [];\n        const filtered = header.slice(0, headerEndIndex).split(\"\\n\");\n        for (const prop of filtered) {\n            if (prop.startsWith(\"property \")) {\n                const [, type, name] = prop.split(\" \");\n                if (chunkMode == 1 /* ElementMode.Chunk */) {\n                    chunkProperties.push({ name, type, offset: rowChunkOffset });\n                    rowChunkOffset += offsets[type];\n                }\n                else if (chunkMode == 0 /* ElementMode.Vertex */) {\n                    vertexProperties.push({ name, type, offset: rowVertexOffset });\n                    rowVertexOffset += offsets[type];\n                }\n                if (!offsets[type]) {\n                    Logger.Warn(`Unsupported property type: ${type}.`);\n                }\n            }\n            else if (prop.startsWith(\"element \")) {\n                const [, type] = prop.split(\" \");\n                if (type == \"chunk\") {\n                    chunkMode = 1 /* ElementMode.Chunk */;\n                }\n                else if (type == \"vertex\") {\n                    chunkMode = 0 /* ElementMode.Vertex */;\n                }\n            }\n        }\n        const rowVertexLength = rowVertexOffset;\n        const rowChunkLength = rowChunkOffset;\n        return GaussianSplattingMesh.ConvertPLYWithSHToSplatAsync(data).then((splatsData) => {\n            const dataView = new DataView(data, headerEndIndex + headerEnd.length);\n            let offset = rowChunkLength * chunkCount + rowVertexLength * vertexCount;\n            // faces\n            const faces = [];\n            if (faceCount) {\n                for (let i = 0; i < faceCount; i++) {\n                    const faceVertexCount = dataView.getUint8(offset);\n                    if (faceVertexCount != 3) {\n                        continue; // only support triangles\n                    }\n                    offset += 1;\n                    for (let j = 0; j < faceVertexCount; j++) {\n                        const vertexIndex = dataView.getUint32(offset + (2 - j) * 4, true); // change face winding\n                        faces.push(vertexIndex);\n                    }\n                    offset += 12;\n                }\n            }\n            // early exit for chunked/quantized ply\n            if (chunkCount) {\n                return new Promise((resolve) => {\n                    resolve({ mode: 0 /* Mode.Splat */, data: splatsData.buffer, sh: splatsData.sh, faces: faces, hasVertexColors: false });\n                });\n            }\n            // count available properties. if all necessary are present then it's a splat. Otherwise, it's a point cloud\n            // if faces are found, then it's a standard mesh\n            let propertyCount = 0;\n            let propertyColorCount = 0;\n            const splatProperties = [\"x\", \"y\", \"z\", \"scale_0\", \"scale_1\", \"scale_2\", \"opacity\", \"rot_0\", \"rot_1\", \"rot_2\", \"rot_3\"];\n            const splatColorProperties = [\"red\", \"green\", \"blue\", \"f_dc_0\", \"f_dc_1\", \"f_dc_2\"];\n            for (let propertyIndex = 0; propertyIndex < vertexProperties.length; propertyIndex++) {\n                const property = vertexProperties[propertyIndex];\n                if (splatProperties.includes(property.name)) {\n                    propertyCount++;\n                }\n                if (splatColorProperties.includes(property.name)) {\n                    propertyColorCount++;\n                }\n            }\n            const hasMandatoryProperties = propertyCount == splatProperties.length && propertyColorCount == 3;\n            const currentMode = faceCount ? 2 /* Mode.Mesh */ : hasMandatoryProperties ? 0 /* Mode.Splat */ : 1 /* Mode.PointCloud */;\n            // parsed ready ready to be used as a splat\n            return new Promise((resolve) => {\n                resolve({ mode: currentMode, data: splatsData.buffer, sh: splatsData.sh, faces: faces, hasVertexColors: !!propertyColorCount });\n            });\n        });\n    }\n}\nSPLATFileLoader._DefaultLoadingOptions = {\n    keepInRam: false,\n    flipY: false,\n};\n// Add this loader into the register plugin\nRegisterSceneLoaderPlugin(new SPLATFileLoader());\n//# sourceMappingURL=splatFileLoader.js.map"], "file": "assets/splatFileLoader-DythdS2W.js"}