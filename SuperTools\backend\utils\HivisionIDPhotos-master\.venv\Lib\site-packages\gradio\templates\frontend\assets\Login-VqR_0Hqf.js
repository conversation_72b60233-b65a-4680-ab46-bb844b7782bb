import G from"./Index-DE1Sah7F.js";import{T as j}from"./Textbox-DI9Q41tU.js";import{B as z}from"./Block-CJdXVpa7.js";import"./MarkdownCode.svelte_svelte_type_style_lang-DzFJaVYu.js";/* empty css                                                        */import{$ as J}from"./index-Ccc2t4AG.js";import"./StreamingBar.svelte_svelte_type_style_lang-CDNxkBIr.js";/* empty css                                              */import{B as K}from"./Button-Dy8yxofg.js";import Q from"./Index-C_pBLFQL.js";import"./BlockTitle-D2VH9_Na.js";import"./Info-BpelqhYn.js";import"./MarkdownCode-BRQ4PUpt.js";import"./Check-CEkiXcyC.js";import"./Copy-CxQ9EyK2.js";import"./Send-DyoOovnk.js";import"./Square-oAGqOwsh.js";import"./index-CRyThWY1.js";/* empty css                                              */import"./prism-python-D8O99YiR.js";import"./svelte/svelte.js";import"./Image-CnqB5dbD.js";import"./file-url-DoxvUUVV.js";import"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";/* empty css                                                   */import"./index-BFBcOI-E.js";import"./IconButton-C_HS7fTi.js";import"./Clear-By3xiIwg.js";const{SvelteComponent:R,add_flush_callback:x,append:M,attr:L,bind:A,binding_callbacks:E,component_subscribe:U,create_component:v,destroy_component:w,detach:c,element:N,flush:D,init:V,insert:$,mount_component:h,safe_not_equal:W,set_data:H,space:B,text:I,toggle_class:C,transition_in:k,transition_out:T}=window.__gradio__svelte__internal;function F(i){let e;return{c(){e=N("p"),L(e,"class","auth svelte-1ogxbi0")},m(t,o){$(t,e,o),e.innerHTML=i[1]},p(t,o){o&2&&(e.innerHTML=t[1])},d(t){t&&c(e)}}}function O(i){let e,t=i[7]("login.enable_cookies")+"",o;return{c(){e=N("p"),o=I(t),L(e,"class","auth svelte-1ogxbi0")},m(l,s){$(l,e,s),M(e,o)},p(l,s){s&128&&t!==(t=l[7]("login.enable_cookies")+"")&&H(o,t)},d(l){l&&c(e)}}}function P(i){let e,t=i[7]("login.incorrect_credentials")+"",o;return{c(){e=N("p"),o=I(t),L(e,"class","creds svelte-1ogxbi0")},m(l,s){$(l,e,s),M(e,o)},p(l,s){s&128&&t!==(t=l[7]("login.incorrect_credentials")+"")&&H(o,t)},d(l){l&&c(e)}}}function X(i){let e,t,o;function l(n){i[9](n)}let s={root:i[0],label:i[7]("login.username"),lines:1,show_label:!0,max_lines:1};return i[4]!==void 0&&(s.value=i[4]),e=new j({props:s}),E.push(()=>A(e,"value",l)),e.$on("submit",i[8]),{c(){v(e.$$.fragment)},m(n,u){h(e,n,u),o=!0},p(n,u){const a={};u&1&&(a.root=n[0]),u&128&&(a.label=n[7]("login.username")),!t&&u&16&&(t=!0,a.value=n[4],x(()=>t=!1)),e.$set(a)},i(n){o||(k(e.$$.fragment,n),o=!0)},o(n){T(e.$$.fragment,n),o=!1},d(n){w(e,n)}}}function Y(i){let e,t,o;function l(n){i[10](n)}let s={root:i[0],label:i[7]("login.password"),lines:1,show_label:!0,max_lines:1,type:"password"};return i[5]!==void 0&&(s.value=i[5]),e=new j({props:s}),E.push(()=>A(e,"value",l)),e.$on("submit",i[8]),{c(){v(e.$$.fragment)},m(n,u){h(e,n,u),o=!0},p(n,u){const a={};u&1&&(a.root=n[0]),u&128&&(a.label=n[7]("login.password")),!t&&u&32&&(t=!0,a.value=n[5],x(()=>t=!1)),e.$set(a)},i(n){o||(k(e.$$.fragment,n),o=!0)},o(n){T(e.$$.fragment,n),o=!1},d(n){w(e,n)}}}function Z(i){let e,t,o,l;return e=new z({props:{$$slots:{default:[X]},$$scope:{ctx:i}}}),o=new z({props:{$$slots:{default:[Y]},$$scope:{ctx:i}}}),{c(){v(e.$$.fragment),t=B(),v(o.$$.fragment)},m(s,n){h(e,s,n),$(s,t,n),h(o,s,n),l=!0},p(s,n){const u={};n&2193&&(u.$$scope={dirty:n,ctx:s}),e.$set(u);const a={};n&2209&&(a.$$scope={dirty:n,ctx:s}),o.$set(a)},i(s){l||(k(e.$$.fragment,s),k(o.$$.fragment,s),l=!0)},o(s){T(e.$$.fragment,s),T(o.$$.fragment,s),l=!1},d(s){s&&c(t),w(e,s),w(o,s)}}}function y(i){let e=i[7]("login.login")+"",t;return{c(){t=I(e)},m(o,l){$(o,t,l)},p(o,l){l&128&&e!==(e=o[7]("login.login")+"")&&H(t,e)},d(o){o&&c(t)}}}function ee(i){let e,t=i[7]("login.login")+"",o,l,s,n,u,a,b,g,d,p=i[1]&&F(i),_=i[3]&&O(i),f=i[6]&&P(i);return a=new G({props:{$$slots:{default:[Z]},$$scope:{ctx:i}}}),g=new K({props:{size:"lg",variant:"primary",$$slots:{default:[y]},$$scope:{ctx:i}}}),g.$on("click",i[8]),{c(){e=N("h2"),o=I(t),l=B(),p&&p.c(),s=B(),_&&_.c(),n=B(),f&&f.c(),u=B(),v(a.$$.fragment),b=B(),v(g.$$.fragment),L(e,"class","svelte-1ogxbi0")},m(r,m){$(r,e,m),M(e,o),$(r,l,m),p&&p.m(r,m),$(r,s,m),_&&_.m(r,m),$(r,n,m),f&&f.m(r,m),$(r,u,m),h(a,r,m),$(r,b,m),h(g,r,m),d=!0},p(r,m){(!d||m&128)&&t!==(t=r[7]("login.login")+"")&&H(o,t),r[1]?p?p.p(r,m):(p=F(r),p.c(),p.m(s.parentNode,s)):p&&(p.d(1),p=null),r[3]?_?_.p(r,m):(_=O(r),_.c(),_.m(n.parentNode,n)):_&&(_.d(1),_=null),r[6]?f?f.p(r,m):(f=P(r),f.c(),f.m(u.parentNode,u)):f&&(f.d(1),f=null);const S={};m&2225&&(S.$$scope={dirty:m,ctx:r}),a.$set(S);const q={};m&2176&&(q.$$scope={dirty:m,ctx:r}),g.$set(q)},i(r){d||(k(a.$$.fragment,r),k(g.$$.fragment,r),d=!0)},o(r){T(a.$$.fragment,r),T(g.$$.fragment,r),d=!1},d(r){r&&(c(e),c(l),c(s),c(n),c(u),c(b)),p&&p.d(r),_&&_.d(r),f&&f.d(r),w(a,r),w(g,r)}}}function te(i){let e,t,o;return t=new Q({props:{variant:"panel",min_width:480,$$slots:{default:[ee]},$$scope:{ctx:i}}}),{c(){e=N("div"),v(t.$$.fragment),L(e,"class","wrap svelte-1ogxbi0"),C(e,"min-h-screen",i[2])},m(l,s){$(l,e,s),h(t,e,null),o=!0},p(l,[s]){const n={};s&2299&&(n.$$scope={dirty:s,ctx:l}),t.$set(n),(!o||s&4)&&C(e,"min-h-screen",l[2])},i(l){o||(k(t.$$.fragment,l),o=!0)},o(l){T(t.$$.fragment,l),o=!1},d(l){l&&c(e),w(t)}}}function oe(i,e,t){let o;U(i,J,f=>t(7,o=f));let{root:l}=e,{auth_message:s}=e,{app_mode:n}=e,{space_id:u}=e,a="",b="",g=!1;const d=async()=>{const f=new FormData;f.append("username",a),f.append("password",b);let r=await fetch(l+"/login",{method:"POST",body:f});r.status===400?(t(6,g=!0),t(4,a=""),t(5,b="")):r.status==200&&location.reload()};function p(f){a=f,t(4,a)}function _(f){b=f,t(5,b)}return i.$$set=f=>{"root"in f&&t(0,l=f.root),"auth_message"in f&&t(1,s=f.auth_message),"app_mode"in f&&t(2,n=f.app_mode),"space_id"in f&&t(3,u=f.space_id)},[l,s,n,u,a,b,g,o,d,p,_]}class Se extends R{constructor(e){super(),V(this,e,oe,te,W,{root:0,auth_message:1,app_mode:2,space_id:3})}get root(){return this.$$.ctx[0]}set root(e){this.$$set({root:e}),D()}get auth_message(){return this.$$.ctx[1]}set auth_message(e){this.$$set({auth_message:e}),D()}get app_mode(){return this.$$.ctx[2]}set app_mode(e){this.$$set({app_mode:e}),D()}get space_id(){return this.$$.ctx[3]}set space_id(e){this.$$set({space_id:e}),D()}}export{Se as default};
//# sourceMappingURL=Login-VqR_0Hqf.js.map
