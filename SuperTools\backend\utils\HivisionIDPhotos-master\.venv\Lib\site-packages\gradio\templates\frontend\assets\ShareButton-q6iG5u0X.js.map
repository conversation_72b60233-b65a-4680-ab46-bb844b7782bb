{"version": 3, "file": "ShareButton-q6iG5u0X.js", "sources": ["../../../../js/atoms/src/ShareButton.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport IconButton from \"./IconButton.svelte\";\n\timport { Community } from \"@gradio/icons\";\n\timport { createEventDispatcher } from \"svelte\";\n\timport type { ShareData } from \"@gradio/utils\";\n\timport { ShareError } from \"@gradio/utils\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\n\tconst dispatch = createEventDispatcher<{\n\t\tshare: ShareData;\n\t\terror: string;\n\t}>();\n\n\texport let formatter: (arg0: any) => Promise<string>;\n\texport let value: any;\n\texport let i18n: I18nFormatter;\n\tlet pending = false;\n</script>\n\n<IconButton\n\tIcon={Community}\n\tlabel={i18n(\"common.share\")}\n\t{pending}\n\ton:click={async () => {\n\t\ttry {\n\t\t\tpending = true;\n\t\t\tconst formatted = await formatter(value);\n\t\t\tdispatch(\"share\", {\n\t\t\t\tdescription: formatted\n\t\t\t});\n\t\t} catch (e) {\n\t\t\tconsole.error(e);\n\t\t\tlet message = e instanceof ShareError ? e.message : \"Share failed.\";\n\t\t\tdispatch(\"error\", message);\n\t\t} finally {\n\t\t\tpending = false;\n\t\t}\n\t}}\n/>\n"], "names": ["Community", "ctx", "dirty", "iconbutton_changes", "dispatch", "createEventDispatcher", "formatter", "$$props", "value", "i18n", "pending", "$$invalidate", "formatted", "e", "message", "ShareError"], "mappings": "gbAGuC,EAAA,OAAA,6EAiBhCA,EACC,MAAAC,KAAK,cAAc,uGAAnBC,EAAA,IAAAC,EAAA,MAAAF,KAAK,cAAc,2IAbpBG,EAAWC,IAKN,GAAA,CAAA,UAAAC,CAAA,EAAAC,EACA,CAAA,MAAAC,CAAA,EAAAD,EACA,CAAA,KAAAE,CAAA,EAAAF,EACPG,EAAU,yBASZC,EAAA,EAAAD,EAAU,EAAI,QACRE,EAAS,MAASN,EAAUE,CAAK,EACvCJ,EAAS,QACR,CAAA,YAAaQ,CAAA,CAAA,QAENC,EAAC,CACT,QAAQ,MAAMA,CAAC,MACXC,EAAUD,aAAaE,EAAaF,EAAE,QAAU,gBACpDT,EAAS,QAASU,CAAO,UAEzBH,EAAA,EAAAD,EAAU,EAAK"}