{"version": 3, "file": "Index-xQEydEg1.js", "sources": ["../../../../node_modules/.pnpm/@zerodevx+svelte-json-view@1.0.7_svelte@4.2.15/node_modules/@zerodevx/svelte-json-view/JsonView.svelte", "../../../../js/fallback/Index.svelte"], "sourcesContent": ["<script>\n/** @type {*} - object or array to display */\nexport let json\n/** @type {number} - initial expansion depth */\nexport let depth = Infinity\nexport let _cur = 0\nexport let _last = true\n\n/** @type {*[]} */\nlet items\nlet isArray = false\nlet brackets = ['', '']\nlet collapsed = false\n\n/**\n * @param {*} i\n * @returns {string}\n */\nfunction getType(i) {\n  if (i === null) return 'null'\n  return typeof i\n}\n\n/**\n * @param {*} i\n * @returns {string}\n */\nfunction format(i) {\n  const t = getType(i)\n  if (t === 'string') return `\"${i}\"`\n  if (t === 'function') return 'f () {...}'\n  if (t === 'symbol') return i.toString()\n  return i\n}\n\nfunction clicked() {\n  collapsed = !collapsed\n}\n\n/**\n * @param {Event} e\n */\nfunction pressed(e) {\n  if (e instanceof KeyboardEvent && ['Enter', ' '].includes(e.key)) clicked()\n}\n\n$: {\n  items = getType(json) === 'object' ? Object.keys(json) : []\n  isArray = Array.isArray(json)\n  brackets = isArray ? ['[', ']'] : ['{', '}']\n}\n\n$: collapsed = depth < _cur\n</script>\n\n{#if !items.length}\n  <span class=\"_jsonBkt empty\" class:isArray>{brackets[0]}{brackets[1]}</span>{#if !_last}<span\n      class=\"_jsonSep\">,</span\n    >{/if}\n{:else if collapsed}\n  <span\n    class=\"_jsonBkt\"\n    class:isArray\n    role=\"button\"\n    tabindex=\"0\"\n    on:click={clicked}\n    on:keydown={pressed}>{brackets[0]}...{brackets[1]}</span\n  >{#if !_last && collapsed}<span class=\"_jsonSep\">,</span>{/if}\n{:else}\n  <span\n    class=\"_jsonBkt\"\n    class:isArray\n    role=\"button\"\n    tabindex=\"0\"\n    on:click={clicked}\n    on:keydown={pressed}>{brackets[0]}</span\n  >\n  <ul class=\"_jsonList\">\n    {#each items as i, idx}\n      <li>\n        {#if !isArray}\n          <span class=\"_jsonKey\">\"{i}\"</span><span class=\"_jsonSep\">:</span>\n        {/if}\n        {#if getType(json[i]) === 'object'}\n          <svelte:self json={json[i]} {depth} _cur={_cur + 1} _last={idx === items.length - 1} />\n        {:else}\n          <span class=\"_jsonVal {getType(json[i])}\">{format(json[i])}</span\n          >{#if idx < items.length - 1}<span class=\"_jsonSep\">,</span>{/if}\n        {/if}\n      </li>\n    {/each}\n  </ul>\n  <span\n    class=\"_jsonBkt\"\n    class:isArray\n    role=\"button\"\n    tabindex=\"0\"\n    on:click={clicked}\n    on:keydown={pressed}>{brackets[1]}</span\n  >{#if !_last}<span class=\"_jsonSep\">,</span>{/if}\n{/if}\n\n<style>\n:where(._jsonList) {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n  padding-left: var(--jsonPaddingLeft, 1rem);\n  border-left: var(--jsonBorderLeft, 1px dotted);\n}\n:where(._jsonBkt) {\n  color: var(--jsonBracketColor, currentcolor);\n}\n:where(._jsonBkt):not(.empty):hover {\n  cursor: pointer;\n  background: var(--jsonBracketHoverBackground, #e5e7eb);\n}\n:where(._jsonSep) {\n  color: var(--jsonSeparatorColor, currentcolor);\n}\n:where(._jsonKey) {\n  color: var(--jsonKeyColor, currentcolor);\n}\n:where(._jsonVal) {\n  color: var(--jsonValColor, #9ca3af);\n}\n:where(._jsonVal).string {\n  color: var(--jsonValStringColor, #059669);\n}\n:where(._jsonVal).number {\n  color: var(--jsonValNumberColor, #d97706);\n}\n:where(._jsonVal).boolean {\n  color: var(--jsonValBooleanColor, #2563eb);\n}\n</style>\n", "<script lang=\"ts\">\n\timport { JsonView } from \"@zerodevx/svelte-json-view\";\n\n\timport type { Gradio } from \"@gradio/utils\";\n\timport { Block, Info } from \"@gradio/atoms\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport type { SelectData } from \"@gradio/utils\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value = false;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let loading_status: LoadingStatus;\n\texport let gradio: Gradio<{\n\t\tchange: never;\n\t\tselect: SelectData;\n\t\tinput: never;\n\t\tclear_status: LoadingStatus;\n\t}>;\n</script>\n\n<Block {visible} {elem_id} {elem_classes} {container} {scale} {min_width}>\n\t{#if loading_status}\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t\t/>\n\t{/if}\n\n\t<JsonView json={value} />\n</Block>\n"], "names": ["t0_value", "ctx", "t3_value", "i", "create_if_block_4", "insert", "target", "span0", "anchor", "ul", "span1", "current", "dirty", "set_data", "t0", "each_blocks", "t3", "t2_value", "if_block", "create_if_block_3", "span", "t2", "t1_value", "create_if_block_1", "t1", "format", "create_if_block_6", "getType", "t", "t_value", "jsonview_changes", "create_if_block_7", "show_if", "li", "json", "$$props", "depth", "_cur", "_last", "items", "isArray", "brackets", "collapsed", "clicked", "$$invalidate", "pressed", "e", "create_if_block", "elem_id", "elem_classes", "visible", "value", "container", "scale", "min_width", "loading_status", "gradio", "clear_status_handler"], "mappings": "23BA2E0BA,EAAAC,KAAS,CAAC,EAAA,aAuBVC,EAAAD,KAAS,CAAC,EAAA,iBApBzBA,EAAK,CAAA,CAAA,uBAAV,OAAIE,GAAA,kEAqBDF,EAAK,CAAA,GAAAG,EAAA,kXA9BZC,EAOAC,EAAAC,EAAAC,CAAA,kBACAH,EAcIC,EAAAG,EAAAD,CAAA,4DACJH,EAOCC,EAAAI,EAAAF,CAAA,sDAzBWP,EAAO,CAAA,CAAA,gBACLA,EAAO,CAAA,CAAA,cAsBTA,EAAO,CAAA,CAAA,gBACLA,EAAO,CAAA,CAAA,qBAvBG,CAAAU,GAAAC,EAAA,KAAAZ,KAAAA,EAAAC,KAAS,CAAC,EAAA,KAAAY,EAAAC,EAAAd,CAAA,4CAGzBC,EAAK,CAAA,CAAA,oBAAV,OAAIE,GAAA,EAAA,yGAAJ,OAAIA,EAAAY,EAAA,OAAAZ,GAAA,aAoBgB,CAAAQ,GAAAC,EAAA,KAAAV,KAAAA,EAAAD,KAAS,CAAC,EAAA,KAAAY,EAAAG,EAAAd,CAAA,kCAC3BD,EAAK,CAAA,uFArBR,OAAIE,GAAA,gLAZgBH,EAAAC,KAAS,CAAC,EAAA,OAAMgB,EAAAhB,KAAS,CAAC,EAAA,WAC3CiB,EAAA,CAAAjB,MAASA,EAAS,CAAA,GAAAkB,EAAA,oCADW,KAAG,mIANvCd,EAOCC,EAAAc,EAAAZ,CAAA,+DAFWP,EAAO,CAAA,CAAA,gBACLA,EAAO,CAAA,CAAA,iBAAGW,EAAA,IAAAZ,KAAAA,EAAAC,KAAS,CAAC,EAAA,KAAAY,EAAAC,EAAAd,CAAA,EAAMY,EAAA,IAAAK,KAAAA,EAAAhB,KAAS,CAAC,EAAA,KAAAY,EAAAQ,EAAAJ,CAAA,4BAC3C,CAAAhB,MAASA,EAAS,CAAA,mIAXmBD,EAAAC,KAAS,CAAC,EAAA,KAAGqB,EAAArB,KAAS,CAAC,EAAA,UAAeA,EAAK,CAAA,GAAAsB,EAAA,8HAAvFlB,EAA4EC,EAAAc,EAAAZ,CAAA,6CAAhCI,EAAA,IAAAZ,KAAAA,EAAAC,KAAS,CAAC,EAAA,KAAAY,EAAAC,EAAAd,CAAA,EAAGY,EAAA,IAAAU,KAAAA,EAAArB,KAAS,CAAC,EAAA,KAAAY,EAAAW,EAAAF,CAAA,4BAAerB,EAAK,CAAA,2HAyBtDA,EAAC,EAAA,EAAA,oCAAH,GAAC,aAAG,GAAC,oHAA5BI,EAAmCC,EAAAC,EAAAC,CAAA,uBAAAH,EAA8BC,EAAAI,EAAAF,CAAA,uBAAxCP,EAAC,EAAA,EAAA,KAAAY,EAAAW,EAAAF,CAAA,gDAKiBG,EAAOxB,EAAI,CAAA,EAACA,EAAC,EAAA,CAAA,CAAA,EAAA,SAClDiB,EAAAjB,EAAM,EAAA,EAAAA,EAAM,CAAA,EAAA,OAAS,GAACyB,EAAA,yEADLC,EAAQ1B,EAAI,CAAA,EAACA,EAAC,EAAA,CAAA,CAAA,EAAA,gBAAA,UAArCI,EACCC,EAAAc,EAAAZ,CAAA,mDAD0CiB,EAAOxB,EAAI,CAAA,EAACA,EAAC,EAAA,CAAA,CAAA,EAAA,KAAAY,EAAAe,EAAAC,CAAA,2BAAjCF,EAAQ1B,EAAI,CAAA,EAACA,EAAC,EAAA,CAAA,CAAA,EAAA,kCAC/BA,EAAM,EAAA,EAAAA,EAAM,CAAA,EAAA,OAAS,kJAHR,KAAAA,KAAKA,EAAC,EAAA,CAAA,aAAiB,KAAAA,KAAO,EAAU,MAAAA,EAAQ,EAAA,IAAAA,EAAM,CAAA,EAAA,OAAS,sEAA/DW,EAAA,KAAAkB,EAAA,KAAA7B,KAAKA,EAAC,EAAA,CAAA,uBAAiBW,EAAA,IAAAkB,EAAA,KAAA7B,KAAO,GAAUW,EAAA,KAAAkB,EAAA,MAAA7B,EAAQ,EAAA,IAAAA,EAAM,CAAA,EAAA,OAAS,yMAGrDI,EAA+BC,EAAAc,EAAAZ,CAAA,qDAPxDP,EAAO,CAAA,GAAA8B,EAAA9B,CAAA,6DAGR+B,GAAA,OAAAA,EAAAL,EAAQ1B,EAAK,CAAA,EAAAA,UAAQ,+HAJ5BI,EAUIC,EAAA2B,EAAAzB,CAAA,2DATIP,EAAO,CAAA,4WAmBNI,EAA+BC,EAAAc,EAAAZ,CAAA,6HAhClBH,EAA+BC,EAAAc,EAAAZ,CAAA,6HAX+BH,EAErFC,EAAAc,EAAAZ,CAAA,qFAHC,OAAAP,KAAM,OAIFA,EAAS,CAAA,EAAA,IAJD,sSArCT,SAAA0B,EAAQxB,EAAC,QACZA,IAAM,KAAa,OACT,OAAAA,EAOP,SAAAsB,EAAOtB,EAAC,OACTyB,EAAID,EAAQxB,CAAC,SACfyB,IAAM,SAAQ,IAAazB,CAAC,IAC5ByB,IAAM,WAAmB,aACzBA,IAAM,SAAiBzB,EAAE,SAAQ,EAC9BA,qBA9BE,GAAA,CAAA,KAAA+B,CAAA,EAAAC,EAEA,CAAA,MAAAC,EAAQ,GAAA,EAAAD,EACR,CAAA,KAAAE,EAAO,CAAA,EAAAF,EACP,CAAA,MAAAG,EAAQ,EAAA,EAAAH,EAGfI,EACAC,EAAU,GACVC,EAAQ,CAAI,GAAI,EAAE,EAClBC,EAAY,YAuBPC,GAAO,CACdC,EAAA,EAAAF,EAAa,CAAAA,CAAA,EAMN,SAAAG,EAAQC,EAAC,CACZA,aAAa,eAAkB,CAAA,QAAS,GAAG,EAAE,SAASA,EAAE,GAAG,GAAGH,yKAIlEJ,EAAQZ,EAAQO,CAAI,IAAM,SAAW,OAAO,KAAKA,CAAI,EAAA,CAAA,CAAA,EACrDU,EAAA,EAAAJ,EAAU,MAAM,QAAQN,CAAI,CAAA,MAC5BO,EAAWD,EAAW,CAAA,IAAK,GAAG,EAAA,CAAK,IAAK,GAAG,CAAA,iBAG1CI,EAAA,EAAAF,EAAYN,EAAQC,CAAA,guBCxBR,CAAA,WAAApC,KAAO,UAAU,EACvB,CAAA,KAAAA,KAAO,IAAI,EACbA,EAAc,CAAA,mLAFNW,EAAA,KAAA,CAAA,WAAAX,KAAO,UAAU,EACvBW,EAAA,KAAA,CAAA,KAAAX,KAAO,IAAI,YACbA,EAAc,CAAA,CAAA,4HAJfA,EAAc,CAAA,GAAA8C,GAAA9C,CAAA,+BASHA,EAAK,CAAA,CAAA,CAAA,CAAA,4FAThBA,EAAc,CAAA,qIASHA,EAAK,CAAA,4oBA1BV,QAAA+C,EAAU,EAAA,EAAAb,EACV,CAAA,aAAAc,EAAA,EAAA,EAAAd,GACA,QAAAe,EAAU,EAAA,EAAAf,GACV,MAAAgB,EAAQ,EAAA,EAAAhB,GACR,UAAAiB,EAAY,EAAA,EAAAjB,GACZ,MAAAkB,EAAuB,IAAA,EAAAlB,GACvB,UAAAmB,EAAgC,MAAA,EAAAnB,EAChC,CAAA,eAAAoB,CAAA,EAAApB,EACA,CAAA,OAAAqB,CAAA,EAAArB,EAcc,MAAAsB,EAAA,IAAAD,EAAO,SAAS,eAAgBD,CAAc", "x_google_ignoreList": [0]}