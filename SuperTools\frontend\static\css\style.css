/* 全局变量 */
:root {
    --primary: #7c3aed;
    --primary-light: #9d65ef;
    --primary-dark: #6025ca;
    --secondary: #f43f5e;
    --accent: #4f46e5;
    --background: #ffffff;
    --foreground: #1e293b;
    --muted: #f9fafb;
    --muted-foreground: #64748b;
    --border: #e2e8f0;
    --radius: 0.5rem;
    --radius-sm: 0.25rem;
    --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-sans);
    line-height: 1.6;
    color: var(--foreground);
    background-color: var(--muted);
}

a {
    color: var(--primary);
    text-decoration: none;
    transition: color 0.2s ease;
}

a:hover {
    color: var(--primary-dark);
}

ul {
    list-style: none;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: var(--radius);
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    border: 1px solid transparent;
    gap: 0.5rem; /* 添加图标和文字之间的间距 */
}

/* 按钮内图标样式 */
.btn i {
    font-size: 1rem;
    line-height: 1;
}

/* 确保按钮内的图标和文字有适当间距 */
.btn i + * {
    margin-left: 0.25rem;
}

.btn-primary {
    background-color: var(--primary);
    color: white;
    padding: 0.75rem 1.5rem; /* 增加内边距 */
    font-size: 0.9rem; /* 稍微增大字体 */
    font-weight: 600; /* 增加字体粗细 */
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px); /* 添加悬停效果 */
    box-shadow: 0 4px 12px rgba(124, 58, 237, 0.3); /* 添加阴影 */
}

.btn-secondary {
    background-color: var(--muted);
    color: var(--foreground);
}

.btn-secondary:hover {
    background-color: #e4e4e7;
}

.btn-small {
    padding: 0.25rem 0.5rem;
    font-size: 0.85rem;
    border-radius: var(--radius-sm);
}

/* 禁用按钮样式 */
.btn.disabled,
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

/* 确保特定按钮不受禁用样式影响 */
#more-platforms-btn,
#feedback-btn {
    pointer-events: auto !important;
    opacity: 1 !important;
    cursor: pointer !important;
}

/* 表单提交按钮特殊样式 */
button[type="submit"].btn {
    min-width: 140px; /* 最小宽度 */
    padding: 0.875rem 2rem; /* 更大的内边距 */
    font-size: 1rem; /* 更大的字体 */
    font-weight: 600;
    letter-spacing: 0.025em; /* 字母间距 */
}

/* CSDN提交按钮特殊样式 */
#csdn-submit-btn {
    background: linear-gradient(135deg, var(--primary) 0%, #8b5cf6 100%); /* 渐变背景 */
    border: none;
    box-shadow: 0 2px 8px rgba(124, 58, 237, 0.2);
    transition: all 0.3s ease;
}

#csdn-submit-btn:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, #7c3aed 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(124, 58, 237, 0.4);
}

/* 头部导航 */
.header {
    background-color: var(--background);
    padding: 1rem 0;
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.logo {
    display: flex;
    align-items: center;
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary);
    margin-right: 1rem;
}

.logo i {
    margin-right: 0.5rem;
}

.subtitle {
    color: var(--muted-foreground);
    font-size: 0.875rem;
    margin-right: auto;
}

.main-nav {
    margin-left: auto;
}

.main-nav ul {
    display: flex;
    gap: 1.5rem;
}

.main-nav a {
    color: var(--foreground);
    font-weight: 500;
    font-size: 0.875rem;
    position: relative;
    padding-bottom: 0.25rem;
}

.main-nav a:hover,
.main-nav a.active {
    color: var(--primary);
}

.main-nav a.active:after {
    content: '' !important;
    position: absolute !important;
    bottom: -2px !important;
    left: 0 !important;
    width: 100% !important;
    height: 2px !important;
    background-color: #7c3aed !important;
    border-radius: 1px !important;
    display: block !important;
    z-index: 999 !important;
}

/* 额外的强制样式确保紫色下划线显示 */
.main-nav a.active::before {
    content: '' !important;
    position: absolute !important;
    bottom: -3px !important;
    left: 0 !important;
    width: 100% !important;
    height: 3px !important;
    background-color: #7c3aed !important;
    border-radius: 2px !important;
    display: block !important;
    z-index: 1000 !important;
}

/* 英雄区域 */
.hero {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    color: white;
    padding: 4rem 0;
    text-align: center;
    margin-bottom: 2rem;
}

.hero h1 {
    font-size: 2.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero p {
    max-width: 700px;
    margin: 0 auto 2rem;
    opacity: 0.9;
}

.search-box {
    display: flex;
    max-width: 600px;
    margin: 0 auto;
    border-radius: var(--radius);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.search-box input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: none;
    outline: none;
    font-size: 1rem;
}

.search-box button {
    padding: 0.75rem 1.5rem;
    background-color: var(--accent);
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem; /* 图标和文字间距 */
    font-weight: 500;
    transition: all 0.2s ease;
}

/* 搜索按钮内图标样式 */
.search-box button i {
    font-size: 1rem;
    line-height: 1;
}

.search-box button:hover {
    background-color: #4338ca;
}

/* 搜索结果样式 */
.search-result {
    max-width: 600px;
    margin: 1.5rem auto 0;
    text-align: left;
}

.result-card {
    background-color: var(--background);
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    border: 1px solid var(--border);
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background-color: var(--primary);
    border-bottom: 1px solid var(--border);
}

.result-header h3 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: white;
}

.result-close {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    font-size: 1rem;
    padding: 0.25rem;
}

.result-close:hover {
    color: white;
}

.result-content {
    padding: 1rem;
}

.result-item {
    margin-bottom: 0.75rem;
}

.result-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--foreground);
    margin-bottom: 0.25rem;
    text-transform: uppercase;
}

.result-value {
    font-size: 0.875rem;
    word-break: break-all;
    padding: 0.75rem;
    background-color: var(--muted);
    border-radius: var(--radius);
    border: 1px solid var(--border);
    color: var(--foreground); /* 确保文本颜色为深色 */
    font-weight: 500; /* 增加字体粗细 */
}

.result-actions {
    display: flex;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background-color: var(--muted);
    border-top: 1px solid var(--border);
}

/* 区块标题 */
.section-title {
    text-align: center;
    font-size: 1.875rem;
    font-weight: 700;
    margin-bottom: 2.5rem;
    position: relative;
    display: inline-block;
    padding-bottom: 0.5rem;
}

.section-title:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--primary);
    border-radius: 3px;
}

section {
    padding: 3rem 0;
}

/* 平台卡片 */
.platforms {
    background-color: var(--background);
    text-align: center;
}

/* 平台标签页样式 */
.platform-tabs {
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--border);
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.platform-tab-btn {
    padding: 0.75rem 1.25rem;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    font-weight: 500;
    color: var(--muted-foreground);
    cursor: pointer;
    transition: all 0.2s ease;
}

.platform-tab-btn:hover {
    color: var(--foreground);
}

.platform-tab-btn.active {
    color: var(--primary);
    border-bottom-color: var(--primary);
}

.platform-search-container {
    margin-left: auto;
    position: relative;
}

.platform-search-container input {
    padding: 0.5rem 2rem 0.5rem 1rem;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    background-color: var(--muted);
    font-size: 0.875rem;
}

.platform-search-container i {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--muted-foreground);
}

.search-clear-btn {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--muted-foreground);
    cursor: pointer;
    padding: 0.25rem;
    display: none;
}

.search-clear-btn:hover {
    color: var(--foreground);
}

/* 无结果消息 */
.no-results-message {
    padding: 2rem;
    text-align: center;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    background-color: var(--muted);
    border-radius: var(--radius);
}

.empty-state i {
    font-size: 2rem;
    color: var(--muted-foreground);
    margin-bottom: 1rem;
}

.empty-state p {
    color: var(--muted-foreground);
    font-size: 0.875rem;
}

/* 平台分类内容 */
.platform-category-content {
    display: none;
}

.platform-category-content.active {
    display: block;
}

/* 平台网格布局 */
.platform-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.platform-card {
    padding: 1.5rem;
    background-color: var(--background);
    border-radius: var(--radius);
    box-shadow: var(--shadow-sm);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: 1px solid var(--border);
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative; /* 添加相对定位以支持Pro角标 */
}

.platform-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow);
}

/* 即将上线平台卡片 */
.platform-card.coming-soon {
    opacity: 0.8;
    position: relative;
    overflow: hidden;
}

.coming-soon-badge {
    position: absolute;
    top: 1rem;
    right: -2rem;
    background-color: var(--primary);
    color: white;
    padding: 0.25rem 2rem;
    font-size: 0.75rem;
    transform: rotate(45deg);
    box-shadow: var(--shadow);
}

/* Pro角标样式 */
.pro-badge {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    background: linear-gradient(135deg, #ff6b6b, #ffa500);
    color: white;
    padding: 0.25rem 0.5rem;
    font-size: 0.7rem;
    font-weight: 600;
    border-radius: 0.25rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    z-index: 10;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}



/* 查看更多按钮 */
.view-more-container {
    text-align: center;
    margin-top: 2rem;
}

.view-more-text {
    display: inline;
}

.view-less-text {
    display: none;
}

.expanded .view-more-text {
    display: none;
}

.expanded .view-less-text {
    display: inline;
}

.expanded .fa-chevron-down {
    transform: rotate(180deg);
}

.platform-icon {
    width: 64px;
    height: 64px;
    border-radius: 100%;
    background-color: rgba(124, 58, 237, 0.1);
    color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.platform-card h3 {
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.platform-card p {
    color: var(--muted-foreground);
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
}

/* 工具卡片 */
.tools {
    background-color: var(--muted);
}

/* 登录需求提示 */
.login-required-message {
    text-align: center;
    padding: 2rem 0;
}

.message-card {
    background-color: var(--background);
    border-radius: var(--radius);
    box-shadow: var(--shadow-sm);
    padding: 2rem;
    max-width: 500px;
    margin: 0 auto;
    border: 1px solid var(--border);
}

.message-card i {
    font-size: 3rem;
    color: var(--primary);
    margin-bottom: 1rem;
}

.message-card h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--foreground);
}

.message-card p {
    color: var(--muted-foreground);
    margin-bottom: 1.5rem;
}

.message-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* 新增: 工具内容包裹器样式 */
.tool-content-wrapper {
    display: none !important; /* 默认隐藏，使用!important确保优先级 */
}

.tool-content-wrapper.active {
    display: block !important; /* 显示活动的工具，使用!important确保优先级 */
    animation: fadeIn 0.5s ease-in-out; /* 添加淡入动画 */
}

.tool-card {
    background-color: var(--background);
    border-radius: var(--radius);
    box-shadow: var(--shadow-sm);
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 1px solid var(--border);
}

.tool-title {
    display: flex;
    align-items: center;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--foreground);
}

.tool-icon {
    width: 36px;
    height: 36px;
    border-radius: 0.375rem;
    background-color: var(--primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
}

.tool-desc {
    color: var(--muted-foreground);
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
}

/* 标签页样式 */
.tabs {
    display: flex;
    border-bottom: 1px solid var(--border);
    margin-bottom: 1.5rem;
}

.tab-btn {
    padding: 0.75rem 1.25rem;
    background: none;
    border: none;
    font-weight: 500;
    color: var(--muted-foreground);
    cursor: pointer;
    position: relative;
    transition: color 0.2s ease;
    font-size: 0.875rem;
}

.tab-btn:hover {
    color: var(--primary);
}

.tab-btn.active {
    color: var(--primary);
}

.tab-btn.active:after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--primary);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 表单样式 */
.form-card {
    background-color: var(--muted);
    border-radius: var(--radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.25rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    font-size: 0.875rem;
    color: var(--foreground);
}

.form-input {
    width: 100%;
    padding: 0.625rem 0.875rem;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    font-size: 0.875rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
}

/* 只读输入框样式 */
.form-input[readonly] {
    background-color: var(--muted);
    color: var(--muted-foreground);
    cursor: not-allowed;
}

/* 表单提示样式 */
.form-hint {
    font-size: 0.75rem;
    color: var(--muted-foreground);
    margin-top: 0.25rem;
    font-style: italic;
}

/* 验证消息样式 */
.validation-message {
    font-size: 0.75rem;
    margin-top: 0.25rem;
    display: none;
}

.validation-message.error {
    color: #e53e3e;
}

.validation-message.success {
    color: #38a169;
}

.validation-message.warning {
    color: #f59e0b;
}

/* 权限管理样式 */
.permission-info {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.current-role-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: var(--radius);
    text-align: center;
}

.current-role-card h3 {
    margin: 0 0 1rem 0;
    font-size: 1.2rem;
}

.role-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.role-badge {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-weight: 600;
    font-size: 1.1rem;
}

.role-description {
    opacity: 0.9;
    font-size: 0.9rem;
}

.role-expire {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.expire-label {
    opacity: 0.8;
}

.expire-date {
    font-weight: 600;
}

.upgrade-options h3 {
    margin-bottom: 1rem;
    color: var(--foreground);
}

.upgrade-card, .downgrade-card {
    border: 1px solid var(--border);
    border-radius: var(--radius);
    padding: 1.5rem;
    background: var(--background);
}

.upgrade-card {
    border-color: var(--primary);
    background: linear-gradient(135deg, rgba(124, 58, 237, 0.05) 0%, rgba(124, 58, 237, 0.1) 100%);
}

.downgrade-card {
    border-color: #e53e3e;
    background: linear-gradient(135deg, rgba(229, 62, 62, 0.05) 0%, rgba(229, 62, 62, 0.1) 100%);
}

.upgrade-header, .downgrade-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.upgrade-header h4, .downgrade-header h4 {
    margin: 0;
    color: var(--foreground);
}

.upgrade-price, .upgrade-method {
    background: var(--primary);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-weight: 600;
    font-size: 0.9rem;
}

.upgrade-method {
    background: #38a169;
}

.downgrade-warning {
    background: #e53e3e;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-weight: 600;
    font-size: 0.9rem;
}

.upgrade-features ul, .downgrade-features ul {
    list-style: none;
    padding: 0;
    margin: 0 0 1rem 0;
}

.upgrade-features li, .downgrade-features li {
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border);
}

.upgrade-features li:last-child, .downgrade-features li:last-child {
    border-bottom: none;
}

.upgrade-actions, .downgrade-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

/* 激活码错误提示样式 */
.activation-error {
    margin-top: 0.5rem;
    padding: 0.375rem 0.5rem;
    background-color: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 3px;
    border-left: 2px solid #e53e3e;
}

.activation-error .error-message {
    color: #c53030;
    font-size: 0.7rem;
    line-height: 1.2;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.activation-error .error-message::before {
    content: "⚠️";
    font-size: 0.75rem;
    flex-shrink: 0;
}

/* 获取激活码按钮样式 */
.get-code-btn {
    position: relative;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.get-code-btn:hover {
    background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    color: white;
}

/* 二维码弹窗样式 */
.qr-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    padding: 20px;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
    margin-bottom: 10px;
    border: 1px solid #e9ecef;
}

.qr-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 8px solid transparent;
    border-top-color: white;
}

.get-code-btn:hover .qr-tooltip {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-5px);
}

.qr-content {
    text-align: center;
}

.qr-content h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.qr-code {
    margin: 15px 0;
    display: flex;
    justify-content: center;
}

.qr-code img {
    width: 120px;
    height: 120px;
    border: 2px solid #f8f9fa;
    border-radius: 8px;
}

.qr-text {
    margin: 15px 0 8px 0;
    color: #28a745;
    font-weight: 600;
    font-size: 14px;
}

.qr-note {
    margin: 0;
    color: #6c757d;
    font-size: 12px;
}

.upgrade-btn, .downgrade-btn {
    flex: 1;
    min-width: 150px;
}

.permission-details h3 {
    margin-bottom: 1rem;
    color: var(--foreground);
}

.permission-list {
    display: grid;
    gap: 1rem;
}

.permission-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--muted);
    border-radius: var(--radius);
    border: 1px solid var(--border);
}

.permission-name {
    font-weight: 600;
    color: var(--foreground);
}

.permission-status {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 600;
}

.permission-status.enabled {
    background: #38a169;
    color: white;
}

.permission-status.disabled {
    background: #e53e3e;
    color: white;
}

.permission-status.limited {
    background: #f59e0b;
    color: white;
}

/* 激活码输入样式 */
.activation-input {
    margin: 1rem 0;
}

.activation-input .form-group {
    margin-bottom: 0;
}

.activation-input input {
    font-family: 'Courier New', monospace;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.input-hint {
    font-size: 0.8rem;
    color: var(--muted-foreground);
    margin-top: 0.25rem;
}

/* 管理员界面样式 */
.admin-section {
    border-top: 2px solid var(--border);
    padding-top: 1.5rem;
    margin-top: 1.5rem;
}

/* 超级管理员专用样式 */
.permission-info .upgrade-options {
    transition: all 0.3s ease;
}

.permission-info .upgrade-options[style*="display: none"] {
    display: none !important;
}

.admin-section h3 {
    color: #e53e3e;
    margin-bottom: 1rem;
}

.admin-card {
    background: linear-gradient(135deg, rgba(229, 62, 62, 0.05) 0%, rgba(229, 62, 62, 0.1) 100%);
    border: 1px solid #e53e3e;
    border-radius: var(--radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.admin-header h4 {
    margin: 0 0 1rem 0;
    color: #e53e3e;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.admin-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.generated-codes {
    background: var(--muted);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.generated-codes h4 {
    margin: 0 0 1rem 0;
    color: var(--foreground);
}

.codes-list {
    display: grid;
    gap: 0.5rem;
}

.code-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--background);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    transition: all 0.2s ease;
}

.code-item.used {
    opacity: 0.6;
    background: var(--muted);
}

.code-item.unused {
    border-left: 3px solid #38a169;
}

.code-main {
    flex: 1;
}

.code-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.delete-code-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.delete-code-btn:hover {
    background: #c82333;
}

.code-status {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.8rem;
    font-weight: 600;
}

.code-status.used {
    background: #e53e3e;
    color: white;
}

.code-status.unused {
    background: #38a169;
    color: white;
}

.code-date {
    color: var(--muted-foreground);
    font-size: 0.8rem;
}

.no-codes {
    text-align: center;
    padding: 2rem;
    color: var(--muted-foreground);
    font-style: italic;
}

.code-value {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: var(--primary);
    letter-spacing: 1px;
}

.code-info {
    font-size: 0.9rem;
    color: var(--muted-foreground);
}

.copy-code-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    background: var(--secondary);
    color: var(--secondary-foreground);
    border: none;
    border-radius: 0.25rem;
    cursor: pointer;
}

.copy-code-btn:hover {
    background: var(--secondary-hover);
}

/* 激活码统计样式 */
.activation-stats {
    background: var(--muted);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    padding: 1.5rem;
}

.activation-stats h4 {
    margin: 0 0 1rem 0;
    color: var(--foreground);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: var(--background);
    border: 1px solid var(--border);
    border-radius: var(--radius);
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary);
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--muted-foreground);
    margin-top: 0.25rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }

    .admin-actions {
        flex-direction: column;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* 状态容器 */
.status-container {
    margin: 16px 0;
    padding: 8px 12px;
    border-radius: var(--radius);
    background-color: var(--muted);
}

.status-message {
    margin: 0;
    font-size: 0.875rem;
}

.status-message.error {
    color: #e53e3e;
}

.status-message.success {
    color: #38a169;
}

.status-message.info {
    color: #3182ce;
}

.status-message.warning {
    color: #f59e0b;
}

.progress-container {
    height: 6px;
    background-color: var(--border);
    border-radius: 3px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background-color: var(--primary);
    width: 0;
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* 结果容器 */
.result-container {
    background-color: var(--background);
    border-radius: var(--radius);
    padding: 1.5rem;
    margin-top: 1.5rem;
    display: none;
    border: 1px solid var(--border);
    box-shadow: var(--shadow-sm);
}

.result-item {
    margin-bottom: 1rem;
}

.result-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--foreground);
    margin-bottom: 0.25rem;
    text-transform: uppercase;
}

.result-value {
    font-weight: 500;
    color: var(--foreground);
    font-size: 0.95rem;
}

/* 视频预览样式 */
.video-preview-container {
    margin-bottom: 1.5rem;
    border-radius: var(--radius);
    overflow: hidden;
    background-color: #000;
    position: relative;
    padding-top: 56.25%; /* 16:9 宽高比 */
}

.video-player {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain;
    background-color: #000;
    border-radius: var(--radius);
}

/* 特性部分 */
.about {
    background-color: var(--background);
    text-align: center;
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
}

.about p {
    margin-bottom: 2.5rem;
}

.features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2.5rem;
}

.feature-card {
    background-color: var(--muted);
    padding: 1.5rem;
    border-radius: var(--radius);
    transition: transform 0.2s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: rgba(124, 58, 237, 0.1);
    color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.25rem;
}

.feature-card h3 {
    margin-bottom: 0.75rem;
    font-weight: 600;
}

.feature-card p {
    color: var(--muted-foreground);
    font-size: 0.875rem;
    margin-bottom: 0;
}

.feedback-link {
    margin-top: 1.5rem;
    display: flex;
    justify-content: center;
}

/* 微信反馈按钮样式 */
#feedback-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 0.75rem 1.5rem;
    background-color: #9c27b0; /* 紫色背景，与您提供的图片颜色相似 */
    border-color: #9c27b0;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 160px; /* 设置最小宽度 */
    margin: 0 auto; /* 居中显示 */
}

#feedback-btn:hover {
    background-color: #7b1fa2; /* 深一点的紫色 */
    border-color: #7b1fa2;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

#feedback-btn i {
    font-size: 1.2rem;
}

#feedback-btn span {
    font-size: 1rem;
    font-weight: 500;
}

/* 二维码容器样式 */
.qrcode-container {
    display: flex;
    justify-content: center;
    margin: 1.5rem 0;
}

.qrcode-image {
    max-width: 200px;
    height: auto;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    box-shadow: var(--shadow-sm);
}

/* 微信模态框特定样式 */
.wechat-modal-container {
    max-width: 400px;
}

.wechat-modal-header {
    background-color: #9c27b0; /* 与按钮颜色一致 */
    color: white;
}

.wechat-modal-header h2 {
    color: white;
}

.wechat-account-name {
    font-weight: 600;
    color: #9c27b0;
}

.text-center {
    text-align: center;
}

.mb-3 {
    margin-bottom: 1rem;
}

.mt-3 {
    margin-top: 1rem;
}

/* 页脚 */
.footer {
    background-color: var(--background);
    padding: 3rem 0 1.5rem;
    border-top: 1px solid var(--border);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.footer-logo {
    font-weight: 600;
    font-size: 1.25rem;
    color: var(--foreground);
}

.footer-links {
    display: flex;
    gap: 1.5rem;
}

.footer-links a {
    color: var(--muted-foreground);
    font-size: 0.875rem;
}

.copyright {
    text-align: center;
    font-size: 0.875rem;
    color: var(--muted-foreground);
    padding-top: 1.5rem;
    border-top: 1px solid var(--border);
}

/* 响应式样式 */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        align-items: flex-start;
    }

    .main-nav {
        margin-left: 0;
        margin-top: 1rem;
        width: 100%;
    }

    .hero h1 {
        font-size: 1.75rem;
    }

    .platform-grid,
    .features {
        grid-template-columns: 1fr;
    }

    .form-card {
        padding: 1.25rem;
    }

    .footer-content {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .footer-links {
        flex-wrap: wrap;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .search-box {
        flex-direction: column;
    }

    .search-box input {
        border-radius: var(--radius) var(--radius) 0 0;
    }

    .search-box button {
        border-radius: 0 0 var(--radius) var(--radius);
    }
}

/* 添加认证相关的CSS样式 */

/* 认证控件容器 */
.auth-controls {
    display: flex;
    align-items: center;
    margin-left: auto;
}

.auth-buttons {
    display: flex;
    gap: 10px;
}

/* 用户菜单 */
.user-menu {
    display: flex;
    align-items: center;
    cursor: pointer;
    position: relative;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
}

.dropdown {
    position: relative;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 5px;
    color: var(--foreground);
    font-weight: 500;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 8px;
    background-color: var(--background);
    border-radius: var(--radius);
    border: 1px solid var(--border);
    box-shadow: var(--shadow-lg);
    padding: 8px 0;
    min-width: 160px;
    z-index: 1000;
    display: none;
    transition: opacity 0.2s ease, visibility 0.2s ease;
}

/* 显示下拉菜单 */
.dropdown-menu.show {
    display: block;
}

/* 悬停时不隐藏下拉菜单 */
.dropdown-menu:hover,
.dropdown:hover .dropdown-menu.show {
    display: block;
}

.dropdown-menu a {
    display: block;
    padding: 8px 16px;
    color: var(--foreground);
    text-decoration: none;
    transition: background-color 0.2s ease;
    font-size: 0.875rem;
}

.dropdown-menu a:hover {
    background-color: var(--muted);
}

.dropdown-menu .divider {
    height: 1px;
    background-color: var(--border);
    margin: 8px 0;
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1050;
    display: none;
}

.modal.show,
.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-container {
    position: relative;
    background-color: var(--background);
    border-radius: var(--radius);
    box-shadow: var(--shadow-lg);
    width: 100%;
    max-width: 450px;
    z-index: 1060;
    overflow: hidden;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    border-bottom: 1px solid var(--border);
}

.modal-header h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--muted-foreground);
    cursor: pointer;
    padding: 4px;
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    margin-top: 16px;
    text-align: center;
}

.modal-footer a {
    color: var(--primary);
    text-decoration: none;
}

/* 表单复选框 */
.form-checkbox {
    display: flex;
    align-items: center;
    margin: 12px 0;
}

.form-checkbox input {
    margin-right: 8px;
}

/* 密码强度指示器已移除 - 使用简化的密码校验 */

/* 用户资料样式 */
.profile-info {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.profile-avatar {
    width: 96px;
    height: 96px;
    border-radius: 50%;
    background-color: var(--primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;
}

.profile-avatar i {
    font-size: 48px;
}

.profile-details {
    width: 100%;
}

.profile-row {
    display: flex;
    margin-bottom: 12px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border);
}

.profile-label {
    flex: 0 0 100px;
    font-weight: 500;
    color: var(--muted-foreground);
}

.profile-value {
    flex: 1;
    color: var(--foreground);
}

/* 平台规划模态框样式 */
.modal-large {
    max-width: 800px;
}

.roadmap-container {
    padding: 1rem;
    overflow-y: auto;
}

.roadmap-intro {
    margin-bottom: 2rem;
    text-align: center;
}

.platform-status-section {
    margin-bottom: 2rem;
}

.platform-status-section h3 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border);
}

.platform-status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 1rem;
}

.platform-status-card {
    background-color: var(--muted);
    border-radius: var(--radius);
    padding: 1rem;
    display: flex;
    flex-direction: column;
    border: 1px solid var(--border);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.platform-status-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow);
}

.platform-status-card.active {
    border-left: 3px solid var(--primary);
}

.platform-status-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: rgba(124, 58, 237, 0.1);
    color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.75rem;
    font-size: 1.25rem;
}

.platform-status-info h4 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.platform-status-info p {
    font-size: 0.875rem;
    color: var(--muted-foreground);
    margin-bottom: 0.75rem;
}

.status-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.status-badge.live {
    background-color: #10b981;
    color: white;
}

.status-badge.coming-soon {
    background-color: #f59e0b;
    color: white;
}

.status-badge.in-development {
    background-color: #3b82f6;
    color: white;
}

.status-badge.planned {
    background-color: #6b7280;
    color: white;
}

.vote-container {
    margin-top: 0.5rem;
}

.vote-btn {
    background-color: var(--background);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    transition: all 0.2s ease;
}

.vote-btn:hover {
    background-color: var(--primary-light);
    color: white;
    border-color: var(--primary-light);
}

.vote-btn i {
    font-size: 0.875rem;
}

.platform-suggestion {
    background-color: var(--muted);
    border-radius: var(--radius);
    padding: 1.5rem;
    margin-top: 1rem;
}

.platform-suggestion h3 {
    font-size: 1.25rem;
    margin-bottom: 0.75rem;
}

.platform-suggestion p {
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

/* 微信公众号建议区域样式 */
.wechat-suggestion-container {
    display: flex;
    background-color: var(--background);
    border-radius: var(--radius);
    padding: 1.5rem;
    margin-top: 1rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border);
}

.qrcode-container {
    flex: 0 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: 1.5rem;
}

.wechat-qrcode {
    width: 120px;
    height: 120px;
    border: 1px solid var(--border);
    border-radius: 8px;
    padding: 8px;
    background-color: white;
    object-fit: contain;
}

.qrcode-tip {
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: var(--muted-foreground);
    text-align: center;
}

.suggestion-guide {
    flex: 1;
}

.suggestion-guide h4 {
    font-size: 1rem;
    margin-bottom: 0.75rem;
    color: var(--foreground);
}

.suggestion-guide ol {
    margin-left: 1.5rem;
    margin-bottom: 1rem;
}

.suggestion-guide li {
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.suggestion-benefits {
    font-size: 0.875rem;
    color: var(--primary);
    background-color: rgba(124, 58, 237, 0.1);
    padding: 0.75rem;
    border-radius: var(--radius);
    border-left: 3px solid var(--primary);
    margin-top: 1rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .wechat-suggestion-container {
        flex-direction: column;
    }

    .qrcode-container {
        margin-right: 0;
        margin-bottom: 1.5rem;
    }
}

/* 二维码点击放大模态框 */
.qrcode-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.qrcode-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(3px);
}

.qrcode-modal-content {
    position: relative;
    z-index: 2001;
    background-color: white;
    padding: 1.5rem;
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    text-align: center;
    max-width: 90%;
}

.qrcode-modal-content img {
    width: 250px;
    height: 250px;
    object-fit: contain;
    margin-bottom: 1rem;
    border-radius: 12px;
    padding: 10px;
    background-color: white;
    border: 1px solid var(--border);
}

.qrcode-modal-content p {
    color: var(--muted-foreground);
    font-size: 0.875rem;
    margin: 0;
}

/* 让二维码图片看起来可点击 */
.wechat-qrcode {
    cursor: pointer;
    transition: transform 0.2s ease;
}

.wechat-qrcode:hover {
    transform: scale(1.05);
}

/* 通知动画 */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.expired-notice .notice-content,
.role-change-notice .notice-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.expired-notice .notice-close,
.role-change-notice .notice-close {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 5px;
    border-radius: 3px;
    transition: background-color 0.2s;
}

.expired-notice .notice-close:hover,
.role-change-notice .notice-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* 响应式通知样式 */
@media (max-width: 768px) {
    .expired-notice,
    .role-change-notice {
        left: 10px !important;
        right: 10px !important;
        max-width: none !important;
    }
}

/* 工具平台样式 */
/* 证件照工具样式 */
.upload-area {
    border: 2px dashed var(--border);
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: var(--background);
}

.upload-area:hover {
    border-color: var(--primary);
    background-color: var(--muted);
}

.upload-area.dragover {
    border-color: var(--primary);
    background-color: var(--primary-foreground);
}

.upload-placeholder i {
    font-size: 3rem;
    color: var(--muted-foreground);
    margin-bottom: 1rem;
}

.upload-placeholder p {
    font-size: 1.1rem;
    color: var(--foreground);
    margin-bottom: 0.5rem;
}

.upload-hint {
    font-size: 0.9rem;
    color: var(--muted-foreground);
}

.uploaded-preview {
    text-align: center;
    padding: 20px;
    border: 1px solid var(--border);
    border-radius: 8px;
    background-color: var(--background);
}

.uploaded-preview img {
    max-width: 200px;
    max-height: 200px;
    border-radius: 8px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.background-options {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    margin-top: 10px;
}

.background-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 10px;
    border-radius: 8px;
    transition: background-color 0.2s ease;
}

.background-option:hover {
    background-color: var(--muted);
}

.background-option input[type="radio"] {
    display: none;
}

.background-option input[type="radio"]:checked + .background-color {
    border: 3px solid var(--primary);
    transform: scale(1.1);
}

.background-color {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid var(--border);
    transition: all 0.2s ease;
}

.background-option span {
    font-size: 0.9rem;
    color: var(--foreground);
}

.custom-color-picker {
    margin-top: 10px;
    padding: 10px;
    border: 1px solid var(--border);
    border-radius: 8px;
    background-color: var(--background);
}

.custom-color-picker input[type="color"] {
    width: 60px;
    height: 40px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
}

.idphoto-preview {
    text-align: center;
    padding: 20px;
    background-color: var(--background);
    border-radius: 8px;
    border: 1px solid var(--border);
}

.idphoto-preview img {
    max-width: 300px;
    max-height: 400px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin-bottom: 15px;
}

.photo-info {
    color: var(--muted-foreground);
    font-size: 0.9rem;
}

.download-options {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.download-options .btn {
    flex: 1;
    min-width: 120px;
}

/* 工具平台响应式设计 */
@media (max-width: 768px) {
    .background-options {
        justify-content: center;
    }

    .download-options {
        flex-direction: column;
    }

    .upload-area {
        padding: 30px 15px;
    }

    .upload-placeholder i {
        font-size: 2.5rem;
    }
}

/* 搜索历史记录模态框样式 */
.downloads-modal-content {
    max-width: 1200px;
    width: 95%;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.downloads-modal-content .modal-body {
    flex: 1;
    overflow: hidden;
    padding: 0;
}

.downloads-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* 统计信息 */
.download-stats {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 10px;
    display: flex;
    justify-content: space-around;
    gap: 20px;
}

.stat-item {
    text-align: center;
    flex: 1;
}

.stat-value {
    font-size: 2.5em;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9em;
    opacity: 0.9;
}

/* 工具栏 */
.downloads-toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
    flex-wrap: wrap;
    gap: 15px;
}

.batch-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.selected-count-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-right: 10px;
}

.batch-action-btn {
    background: none;
    border: 1px solid #dee2e6;
    padding: 6px 10px;
    border-radius: 4px;
    cursor: pointer;
    color: #6c757d;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.batch-action-btn:hover:not(.disabled) {
    background: #e9ecef;
    color: #495057;
}

.batch-action-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.view-mode-switcher {
    display: flex;
    background: white;
    border-radius: 6px;
    padding: 2px;
    border: 1px solid #dee2e6;
}

.view-mode-btn {
    background: none;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    color: #6c757d;
    transition: all 0.2s ease;
}

.view-mode-btn.active,
.view-mode-btn:hover {
    background: #007bff;
    color: white;
}

.sort-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

.sort-container label {
    font-size: 0.9rem;
    color: #495057;
}

.sort-container select {
    padding: 6px 10px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background: white;
    color: #495057;
    font-size: 0.875rem;
}

/* 筛选表单 */
.filter-container {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.filter-form {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #495057;
}

.filter-group select {
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background: white;
    color: #495057;
    font-size: 0.875rem;
}

.filter-group select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.filter-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 20px;
}

.filter-checkbox input[type="checkbox"] {
    margin: 0;
}

.filter-checkbox label {
    margin: 0;
    cursor: pointer;
}

.filter-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.filter-actions .btn {
    padding: 8px 16px;
    font-size: 0.875rem;
}

/* 搜索列表容器 */
.downloads-list-container {
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    overflow: hidden;
    min-height: 400px;
}

#downloads-list {
    min-height: 300px;
    width: 100%;
    position: relative;
    z-index: 1;
}

/* 加载状态 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: #6c757d;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    color: #6c757d;
}

.empty-container i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #dee2e6;
}

.empty-container p {
    font-size: 1.1rem;
    margin: 0;
}

/* 搜索记录表格样式 */
.downloads-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.downloads-table th,
.downloads-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.downloads-table th {
    background: #f8f9fa;
    font-weight: 600;
    font-size: 0.875rem;
    color: #495057;
}

.downloads-table tbody tr:hover {
    background: #f8f9fa;
}

.downloads-table tbody tr.status-completed {
    border-left: 3px solid #28a745;
}

.downloads-table tbody tr.status-failed {
    border-left: 3px solid #dc3545;
}

.downloads-table tbody tr.is-favorite {
    background: rgba(255, 193, 7, 0.1);
}

.checkbox-column {
    width: 50px;
    text-align: center;
}

.download-title {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 500;
}

.download-actions {
    display: flex;
    gap: 5px;
    align-items: center;
}

.action-btn {
    background: none;
    border: 1px solid #dee2e6;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    color: #6c757d;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.action-btn:hover {
    background: #e9ecef;
    color: #495057;
}

.action-btn.favorite-btn.active {
    color: #ffc107;
    border-color: #ffc107;
}

.action-btn.notes-btn.has-notes {
    color: #007bff;
    border-color: #007bff;
}

.action-btn.delete-btn:hover {
    color: #dc3545;
    border-color: #dc3545;
}

.view-btn {
    color: #007bff;
    text-decoration: none;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.view-btn:hover {
    background: rgba(0, 123, 255, 0.1);
}

/* 状态标签 */
.status-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-badge.completed {
    background: #d4edda;
    color: #155724;
}

.status-badge.failed {
    background: #f8d7da;
    color: #721c24;
}

/* 分页 */
.pagination {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
}

.pagination-info {
    font-size: 0.875rem;
    color: #6c757d;
}

.pagination-links {
    display: flex;
    gap: 5px;
    align-items: center;
}

.page-link {
    padding: 6px 12px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background: white;
    color: #007bff;
    text-decoration: none;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.page-link:hover:not(.disabled) {
    background: #e9ecef;
    border-color: #adb5bd;
}

.page-link.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.page-link.disabled {
    color: #6c757d;
    cursor: not-allowed;
    pointer-events: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .downloads-toolbar {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .batch-actions,
    .view-mode-switcher,
    .sort-container {
        justify-content: center;
    }

    .filter-form {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .downloads-table {
        font-size: 0.875rem;
    }

    .downloads-table th,
    .downloads-table td {
        padding: 8px;
    }

    .download-title {
        max-width: 200px;
    }

    .download-actions {
        flex-direction: column;
        gap: 2px;
    }

    .pagination {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .pagination-links {
        justify-content: center;
    }
}

