import{GLTFLoader as S,ArrayItem as L}from"./glTFLoader-D-NF4fEj.js";import{ab as k,an as C,ao as I}from"./index-Cb4A4-Xi.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./bone-CgNwSK5F.js";import"./rawTexture-PjZ4PTsN.js";import"./assetContainer-CIn78ZXO.js";import"./objectModelMapping-D3Nr8hfO.js";const n="KHR_materials_variants";class s{constructor(t){this.name=n,this._loader=t,this.enabled=this._loader.isExtensionUsed(n)}dispose(){this._loader=null}static GetAvailableVariants(t){const e=this._GetExtensionMetadata(t);return e?Object.keys(e.variants):[]}getAvailableVariants(t){return s.GetAvailableVariants(t)}static SelectVariant(t,e){const r=this._GetExtensionMetadata(t);if(!r)throw new Error(`Cannot select variant on a glTF mesh that does not have the ${n} extension`);const h=c=>{const f=r.variants[c];if(f)for(const m of f)m.mesh.material=m.material};if(e instanceof Array)for(const c of e)h(c);else h(e);r.lastSelected=e}selectVariant(t,e){s.SelectVariant(t,e)}static Reset(t){const e=this._GetExtensionMetadata(t);if(!e)throw new Error(`Cannot reset on a glTF mesh that does not have the ${n} extension`);for(const r of e.original)r.mesh.material=r.material;e.lastSelected=null}reset(t){s.Reset(t)}static GetLastSelectedVariant(t){const e=this._GetExtensionMetadata(t);if(!e)throw new Error(`Cannot get the last selected variant on a glTF mesh that does not have the ${n} extension`);return e.lastSelected}getLastSelectedVariant(t){return s.GetLastSelectedVariant(t)}static _GetExtensionMetadata(t){return t?._internalMetadata?.gltf?.[n]||null}onLoading(){const t=this._loader.gltf.extensions;if(t&&t[this.name]){const e=t[this.name];this._variants=e.variants}}onReady(){const t=this._loader.rootBabylonMesh;if(t){const e=this._loader.parent.extensionOptions[n];e?.defaultVariant&&s.SelectVariant(t,e.defaultVariant),e?.onLoaded?.({get variants(){return s.GetAvailableVariants(t)},get selectedVariant(){const r=s.GetLastSelectedVariant(t);return r?Array.isArray(r)?r[0]:r:s.GetAvailableVariants(t)[0]},set selectedVariant(r){s.SelectVariant(t,r)}})}}_loadMeshPrimitiveAsync(t,e,r,h,c,f){return S.LoadExtensionAsync(t,c,this.name,(m,V)=>{const x=new Array;return x.push(this._loader._loadMeshPrimitiveAsync(t,e,r,h,c,o=>{if(f(o),o instanceof k){const $=S._GetDrawMode(t,c.mode),d=this._loader.rootBabylonMesh,E=d?d._internalMetadata=d._internalMetadata||{}:{},A=E.gltf=E.gltf||{},g=A[n]=A[n]||{lastSelected:null,original:[],variants:{}};g.original.push({mesh:o,material:o.material});for(let p=0;p<V.mappings.length;++p){const u=V.mappings[p],F=L.Get(`${m}/mappings/${p}/material`,this._loader.gltf.materials,u.material);x.push(this._loader._loadMaterialAsync(`#/materials/${u.material}`,F,o,$,T=>{for(let v=0;v<u.variants.length;++v){const w=u.variants[v],M=L.Get(`/extensions/${n}/variants/${w}`,this._variants,w);g.variants[M.name]=g.variants[M.name]||[],g.variants[M.name].push({mesh:o,material:T}),o.onClonedObservable.add(O=>{const G=O;let l=null,i=G;do{if(i=i.parent,!i)return;l=s._GetExtensionMetadata(i)}while(l===null);if(d&&l===s._GetExtensionMetadata(d)){i._internalMetadata={};for(const a in d._internalMetadata)i._internalMetadata[a]=d._internalMetadata[a];i._internalMetadata.gltf=[];for(const a in d._internalMetadata.gltf)i._internalMetadata.gltf[a]=d._internalMetadata.gltf[a];i._internalMetadata.gltf[n]={lastSelected:null,original:[],variants:{}};for(const a of l.original)i._internalMetadata.gltf[n].original.push({mesh:a.mesh,material:a.material});for(const a in l.variants)if(Object.prototype.hasOwnProperty.call(l.variants,a)){i._internalMetadata.gltf[n].variants[a]=[];for(const y of l.variants[a])i._internalMetadata.gltf[n].variants[a].push({mesh:y.mesh,material:y.material})}l=i._internalMetadata.gltf[n]}for(const a of l.original)a.mesh===o&&(a.mesh=G);for(const a of l.variants[M.name])a.mesh===o&&(a.mesh=G)})}}))}}})),Promise.all(x).then(([o])=>o)})}}C(n);I(n,!0,_=>new s(_));export{s as KHR_materials_variants};
//# sourceMappingURL=KHR_materials_variants-C_uXar2L.js.map
