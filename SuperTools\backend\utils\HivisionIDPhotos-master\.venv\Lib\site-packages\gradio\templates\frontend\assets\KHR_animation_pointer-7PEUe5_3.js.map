{"version": 3, "file": "KHR_animation_pointer-7PEUe5_3.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/KHR_animation_pointer.data.js", "../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/KHR_animation_pointer.js"], "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\nimport { Animation } from \"@babylonjs/core/Animations/animation.js\";\nimport { AnimationPropertyInfo } from \"../glTFLoaderAnimation.js\";\nimport { Color3 } from \"@babylonjs/core/Maths/math.color.js\";\nimport { SetInterpolationForKey } from \"./objectModelMapping.js\";\nfunction getColor3(_target, source, offset, scale) {\n    return Color3.FromArray(source, offset).scale(scale);\n}\nfunction getAlpha(_target, source, offset, scale) {\n    return source[offset + 3] * scale;\n}\nfunction getFloat(_target, source, offset, scale) {\n    return source[offset] * scale;\n}\nfunction getMinusFloat(_target, source, offset, scale) {\n    return -source[offset] * scale;\n}\nfunction getNextFloat(_target, source, offset, scale) {\n    return source[offset + 1] * scale;\n}\nfunction getFloatBy2(_target, source, offset, scale) {\n    return source[offset] * scale * 2;\n}\nfunction getTextureTransformTree(textureName) {\n    return {\n        scale: [\n            new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, `${textureName}.uScale`, getFloat, () => 2),\n            new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, `${textureName}.vScale`, getNextFloat, () => 2),\n        ],\n        offset: [\n            new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, `${textureName}.uOffset`, getFloat, () => 2),\n            new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, `${textureName}.vOffset`, getNextFloat, () => 2),\n        ],\n        rotation: [new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, `${textureName}.wAng`, getMinusFloat, () => 1)],\n    };\n}\nclass CameraAnimationPropertyInfo extends AnimationPropertyInfo {\n    /** @internal */\n    buildAnimations(target, name, fps, keys) {\n        return [{ babylonAnimatable: target._babylonCamera, babylonAnimation: this._buildAnimation(name, fps, keys) }];\n    }\n}\nclass MaterialAnimationPropertyInfo extends AnimationPropertyInfo {\n    /** @internal */\n    buildAnimations(target, name, fps, keys) {\n        const babylonAnimations = [];\n        for (const fillMode in target._data) {\n            babylonAnimations.push({\n                babylonAnimatable: target._data[fillMode].babylonMaterial,\n                babylonAnimation: this._buildAnimation(name, fps, keys),\n            });\n        }\n        return babylonAnimations;\n    }\n}\nclass LightAnimationPropertyInfo extends AnimationPropertyInfo {\n    /** @internal */\n    buildAnimations(target, name, fps, keys) {\n        return [{ babylonAnimatable: target._babylonLight, babylonAnimation: this._buildAnimation(name, fps, keys) }];\n    }\n}\nSetInterpolationForKey(\"/cameras/{}/orthographic/xmag\", [\n    new CameraAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"orthoLeft\", getMinusFloat, () => 1),\n    new CameraAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"orthoRight\", getNextFloat, () => 1),\n]);\nSetInterpolationForKey(\"/cameras/{}/orthographic/ymag\", [\n    new CameraAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"orthoBottom\", getMinusFloat, () => 1),\n    new CameraAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"orthoTop\", getNextFloat, () => 1),\n]);\nSetInterpolationForKey(\"/cameras/{}/orthographic/zfar\", [new CameraAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"maxZ\", getFloat, () => 1)]);\nSetInterpolationForKey(\"/cameras/{}/orthographic/znear\", [new CameraAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"minZ\", getFloat, () => 1)]);\nSetInterpolationForKey(\"/cameras/{}/perspective/yfov\", [new CameraAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"fov\", getFloat, () => 1)]);\nSetInterpolationForKey(\"/cameras/{}/perspective/zfar\", [new CameraAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"maxZ\", getFloat, () => 1)]);\nSetInterpolationForKey(\"/cameras/{}/perspective/znear\", [new CameraAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"minZ\", getFloat, () => 1)]);\n// add interpolation to the materials mapping\nSetInterpolationForKey(\"/materials/{}/pbrMetallicRoughness/baseColorFactor\", [\n    new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_COLOR3, \"albedoColor\", getColor3, () => 4),\n    new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"alpha\", getAlpha, () => 4),\n]);\nSetInterpolationForKey(\"/materials/{}/pbrMetallicRoughness/metallicFactor\", [new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"metallic\", getFloat, () => 1)]);\nSetInterpolationForKey(\"/materials/{}/pbrMetallicRoughness/metallicFactor\", [new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"roughness\", getFloat, () => 1)]);\nconst baseColorTextureInterpolation = getTextureTransformTree(\"albedoTexture\");\nSetInterpolationForKey(\"/materials/{}/pbrMetallicRoughness/baseColorTexture/extensions/KHR_texture_transform/scale\", baseColorTextureInterpolation.scale);\nSetInterpolationForKey(\"/materials/{}/pbrMetallicRoughness/baseColorTexture/extensions/KHR_texture_transform/offset\", baseColorTextureInterpolation.offset);\nSetInterpolationForKey(\"/materials/{}/pbrMetallicRoughness/baseColorTexture/extensions/KHR_texture_transform/rotation\", baseColorTextureInterpolation.rotation);\nconst metallicRoughnessTextureInterpolation = getTextureTransformTree(\"metallicTexture\");\nSetInterpolationForKey(\"//materials/{}/pbrMetallicRoughness/metallicRoughnessTexture/scale\", metallicRoughnessTextureInterpolation.scale);\nSetInterpolationForKey(\"//materials/{}/pbrMetallicRoughness/metallicRoughnessTexture/offset\", metallicRoughnessTextureInterpolation.offset);\nSetInterpolationForKey(\"//materials/{}/pbrMetallicRoughness/metallicRoughnessTexture/rotation\", metallicRoughnessTextureInterpolation.rotation);\nSetInterpolationForKey(\"/materials/{}/emissiveFactor\", [new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_COLOR3, \"emissiveColor\", getColor3, () => 3)]);\nconst normalTextureInterpolation = getTextureTransformTree(\"bumpTexture\");\nSetInterpolationForKey(\"/materials/{}/normalTexture/scale\", [new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"bumpTexture.level\", getFloat, () => 1)]);\nSetInterpolationForKey(\"/materials/{}/normalTexture/extensions/KHR_texture_transform/scale\", normalTextureInterpolation.scale);\nSetInterpolationForKey(\"/materials/{}/normalTexture/extensions/KHR_texture_transform/offset\", normalTextureInterpolation.offset);\nSetInterpolationForKey(\"/materials/{}/normalTexture/extensions/KHR_texture_transform/rotation\", normalTextureInterpolation.rotation);\nSetInterpolationForKey(\"/materials/{}/occlusionTexture/strength\", [new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"ambientTextureStrength\", getFloat, () => 1)]);\nconst occlusionTextureInterpolation = getTextureTransformTree(\"ambientTexture\");\nSetInterpolationForKey(\"/materials/{}/occlusionTexture/extensions/KHR_texture_transform/scale\", occlusionTextureInterpolation.scale);\nSetInterpolationForKey(\"/materials/{}/occlusionTexture/extensions/KHR_texture_transform/offset\", occlusionTextureInterpolation.offset);\nSetInterpolationForKey(\"/materials/{}/occlusionTexture/extensions/KHR_texture_transform/rotation\", occlusionTextureInterpolation.rotation);\nconst emissiveTextureInterpolation = getTextureTransformTree(\"emissiveTexture\");\nSetInterpolationForKey(\"/materials/{}/emissiveTexture/extensions/KHR_texture_transform/scale\", emissiveTextureInterpolation.scale);\nSetInterpolationForKey(\"/materials/{}/emissiveTexture/extensions/KHR_texture_transform/offset\", emissiveTextureInterpolation.offset);\nSetInterpolationForKey(\"/materials/{}/emissiveTexture/extensions/KHR_texture_transform/rotation\", emissiveTextureInterpolation.rotation);\n// materials extensions\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_anisotropy/anisotropyStrength\", [\n    new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"anisotropy.intensity\", getFloat, () => 1),\n]);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_anisotropy/anisotropyRotation\", [\n    new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"anisotropy.angle\", getFloat, () => 1),\n]);\nconst anisotropyTextureInterpolation = getTextureTransformTree(\"anisotropy.texture\");\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_anisotropy/anisotropyTexture/extensions/KHR_texture_transform/scale\", anisotropyTextureInterpolation.scale);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_anisotropy/anisotropyTexture/extensions/KHR_texture_transform/offset\", anisotropyTextureInterpolation.offset);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_anisotropy/anisotropyTexture/extensions/KHR_texture_transform/rotation\", anisotropyTextureInterpolation.rotation);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_clearcoat/clearcoatFactor\", [\n    new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"clearCoat.intensity\", getFloat, () => 1),\n]);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_clearcoat/clearcoatRoughnessFactor\", [\n    new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"clearCoat.roughness\", getFloat, () => 1),\n]);\nconst clearcoatTextureInterpolation = getTextureTransformTree(\"clearCoat.texture\");\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_clearcoat/clearcoatTexture/extensions/KHR_texture_transform/scale\", clearcoatTextureInterpolation.scale);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_clearcoat/clearcoatTexture/extensions/KHR_texture_transform/offset\", clearcoatTextureInterpolation.offset);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_clearcoat/clearcoatTexture/extensions/KHR_texture_transform/rotation\", clearcoatTextureInterpolation.rotation);\nconst clearcoatNormalTextureInterpolation = getTextureTransformTree(\"clearCoat.bumpTexture\");\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_clearcoat/clearcoatNormalTexture/scale\", [\n    new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"clearCoat.bumpTexture.level\", getFloat, () => 1),\n]);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_clearcoat/clearcoatNormalTexture/extensions/KHR_texture_transform/scale\", clearcoatNormalTextureInterpolation.scale);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_clearcoat/clearcoatNormalTexture/extensions/KHR_texture_transform/offset\", clearcoatNormalTextureInterpolation.offset);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_clearcoat/clearcoatNormalTexture/extensions/KHR_texture_transform/rotation\", clearcoatNormalTextureInterpolation.rotation);\nconst clearcoatRoughnessTextureInterpolation = getTextureTransformTree(\"clearCoat.textureRoughness\");\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_clearcoat/clearcoatRoughnessTexture/extensions/KHR_texture_transform/scale\", clearcoatRoughnessTextureInterpolation.scale);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_clearcoat/clearcoatRoughnessTexture/extensions/KHR_texture_transform/offset\", clearcoatRoughnessTextureInterpolation.offset);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_clearcoat/clearcoatRoughnessTexture/extensions/KHR_texture_transform/rotation\", clearcoatRoughnessTextureInterpolation.rotation);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_dispersion/dispersionFactor\", [\n    new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"subSurface.dispersion\", getFloat, () => 1),\n]);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_emissive_strength/emissiveStrength\", [\n    new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"emissiveIntensity\", getFloat, () => 1),\n]);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_ior/ior\", [\n    new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"indexOfRefraction\", getFloat, () => 1),\n]);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_iridescence/iridescenceFactor\", [\n    new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"iridescence.intensity\", getFloat, () => 1),\n]);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_iridescence/iridescenceIor\", [\n    new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"iridescence.indexOfRefraction\", getFloat, () => 1),\n]);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_iridescence/iridescenceThicknessMinimum\", [\n    new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"iridescence.minimumThickness\", getFloat, () => 1),\n]);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_iridescence/iridescenceThicknessMaximum\", [\n    new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"iridescence.maximumThickness\", getFloat, () => 1),\n]);\nconst iridescenceTextureInterpolation = getTextureTransformTree(\"iridescence.texture\");\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_iridescence/iridescenceTexture/extensions/KHR_texture_transform/scale\", iridescenceTextureInterpolation.scale);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_iridescence/iridescenceTexture/extensions/KHR_texture_transform/offset\", iridescenceTextureInterpolation.offset);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_iridescence/iridescenceTexture/extensions/KHR_texture_transform/rotation\", iridescenceTextureInterpolation.rotation);\nconst iridescenceThicknessTextureInterpolation = getTextureTransformTree(\"iridescence.thicknessTexture\");\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_iridescence/iridescenceThicknessTexture/extensions/KHR_texture_transform/scale\", iridescenceThicknessTextureInterpolation.scale);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_iridescence/iridescenceThicknessTexture/extensions/KHR_texture_transform/offset\", iridescenceThicknessTextureInterpolation.offset);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_iridescence/iridescenceThicknessTexture/extensions/KHR_texture_transform/rotation\", iridescenceThicknessTextureInterpolation.rotation);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_sheen/sheenColorFactor\", [\n    new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_COLOR3, \"sheen.color\", getColor3, () => 3),\n]);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_sheen/sheenRoughnessFactor\", [\n    new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"sheen.roughness\", getFloat, () => 1),\n]);\nconst sheenTextureInterpolation = getTextureTransformTree(\"sheen.texture\");\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_sheen/sheenColorTexture/extensions/KHR_texture_transform/scale\", sheenTextureInterpolation.scale);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_sheen/sheenColorTexture/extensions/KHR_texture_transform/offset\", sheenTextureInterpolation.offset);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_sheen/sheenColorTexture/extensions/KHR_texture_transform/rotation\", sheenTextureInterpolation.rotation);\nconst sheenRoughnessTextureInterpolation = getTextureTransformTree(\"sheen.textureRoughness\");\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_sheen/sheenRoughnessTexture/extensions/KHR_texture_transform/scale\", sheenRoughnessTextureInterpolation.scale);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_sheen/sheenRoughnessTexture/extensions/KHR_texture_transform/offset\", sheenRoughnessTextureInterpolation.offset);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_sheen/sheenRoughnessTexture/extensions/KHR_texture_transform/rotation\", sheenRoughnessTextureInterpolation.rotation);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_specular/specularFactor\", [\n    new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"metallicF0Factor\", getFloat, () => 1),\n]);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_specular/specularColorFactor\", [\n    new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_COLOR3, \"metallicReflectanceColor\", getColor3, () => 3),\n]);\nconst specularTextureInterpolation = getTextureTransformTree(\"metallicReflectanceTexture\");\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_specular/specularTexture/extensions/KHR_texture_transform/scale\", specularTextureInterpolation.scale);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_specular/specularTexture/extensions/KHR_texture_transform/offset\", specularTextureInterpolation.offset);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_specular/specularTexture/extensions/KHR_texture_transform/rotation\", specularTextureInterpolation.rotation);\nconst specularColorTextureInterpolation = getTextureTransformTree(\"reflectanceTexture\");\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_specular/specularColorTexture/extensions/KHR_texture_transform/scale\", specularColorTextureInterpolation.scale);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_specular/specularColorTexture/extensions/KHR_texture_transform/offset\", specularColorTextureInterpolation.offset);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_specular/specularColorTexture/extensions/KHR_texture_transform/rotation\", specularColorTextureInterpolation.rotation);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_transmission/transmissionFactor\", [\n    new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"subSurface.refractionIntensity\", getFloat, () => 1),\n]);\nconst transmissionTextureInterpolation = getTextureTransformTree(\"subSurface.refractionIntensityTexture\");\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_transmission/transmissionTexture/extensions/KHR_texture_transform/scale\", transmissionTextureInterpolation.scale);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_transmission/transmissionTexture/extensions/KHR_texture_transform/offset\", transmissionTextureInterpolation.offset);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_transmission/transmissionTexture/extensions/KHR_texture_transform/rotation\", transmissionTextureInterpolation.rotation);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_volume/attenuationColor\", [\n    new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_COLOR3, \"subSurface.tintColor\", getColor3, () => 3),\n]);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_volume/attenuationDistance\", [\n    new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"subSurface.tintColorAtDistance\", getFloat, () => 1),\n]);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_volume/thicknessFactor\", [\n    new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"subSurface.maximumThickness\", getFloat, () => 1),\n]);\nconst thicknessTextureInterpolation = getTextureTransformTree(\"subSurface.thicknessTexture\");\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_volume/thicknessTexture/extensions/KHR_texture_transform/scale\", thicknessTextureInterpolation.scale);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_volume/thicknessTexture/extensions/KHR_texture_transform/offset\", thicknessTextureInterpolation.offset);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_volume/thicknessTexture/extensions/KHR_texture_transform/rotation\", thicknessTextureInterpolation.rotation);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_diffuse_transmission/diffuseTransmissionFactor\", [\n    new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"subSurface.translucencyIntensity\", getFloat, () => 1),\n]);\nconst diffuseTransmissionTextureInterpolation = getTextureTransformTree(\"subSurface.translucencyIntensityTexture\");\nSetInterpolationForKey(\"materials/{}/extensions/KHR_materials_diffuse_transmission/diffuseTransmissionTexture/extensions/KHR_texture_transform/scale\", diffuseTransmissionTextureInterpolation.scale);\nSetInterpolationForKey(\"materials/{}/extensions/KHR_materials_diffuse_transmission/diffuseTransmissionTexture/extensions/KHR_texture_transform/offset\", diffuseTransmissionTextureInterpolation.offset);\nSetInterpolationForKey(\"materials/{}/extensions/KHR_materials_diffuse_transmission/diffuseTransmissionTexture/extensions/KHR_texture_transform/rotation\", diffuseTransmissionTextureInterpolation.rotation);\nSetInterpolationForKey(\"/materials/{}/extensions/KHR_materials_diffuse_transmission/diffuseTransmissionColorFactor\", [\n    new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_COLOR3, \"subSurface.translucencyColor\", getColor3, () => 3),\n]);\nconst diffuseTransmissionColorTextureInterpolation = getTextureTransformTree(\"subSurface.translucencyColorTexture\");\nSetInterpolationForKey(\"materials/{}/extensions/KHR_materials_diffuse_transmission/diffuseTransmissionColorTexture/extensions/KHR_texture_transform/scale\", diffuseTransmissionColorTextureInterpolation.scale);\nSetInterpolationForKey(\"materials/{}/extensions/KHR_materials_diffuse_transmission/diffuseTransmissionColorTexture/extensions/KHR_texture_transform/offset\", diffuseTransmissionColorTextureInterpolation.offset);\nSetInterpolationForKey(\"materials/{}/extensions/KHR_materials_diffuse_transmission/diffuseTransmissionColorTexture/extensions/KHR_texture_transform/rotation\", diffuseTransmissionColorTextureInterpolation.rotation);\nSetInterpolationForKey(\"/extensions/KHR_lights_punctual/lights/{}/color\", [new LightAnimationPropertyInfo(Animation.ANIMATIONTYPE_COLOR3, \"diffuse\", getColor3, () => 3)]);\nSetInterpolationForKey(\"/extensions/KHR_lights_punctual/lights/{}/intensity\", [new LightAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"intensity\", getFloat, () => 1)]);\nSetInterpolationForKey(\"/extensions/KHR_lights_punctual/lights/{}/range\", [new LightAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"range\", getFloat, () => 1)]);\nSetInterpolationForKey(\"/extensions/KHR_lights_punctual/lights/{}/spot/innerConeAngle\", [\n    new LightAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"innerAngle\", getFloatBy2, () => 1),\n]);\nSetInterpolationForKey(\"/extensions/KHR_lights_punctual/lights/{}/spot/outerConeAngle\", [\n    new LightAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"angle\", getFloatBy2, () => 1),\n]);\nSetInterpolationForKey(\"/nodes/{}/extensions/EXT_lights_ies/color\", [new LightAnimationPropertyInfo(Animation.ANIMATIONTYPE_COLOR3, \"diffuse\", getColor3, () => 3)]);\nSetInterpolationForKey(\"/nodes/{}/extensions/EXT_lights_ies/multiplier\", [new LightAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"intensity\", getFloat, () => 1)]);\n//# sourceMappingURL=KHR_animation_pointer.data.js.map", "import { Logger } from \"@babylonjs/core/Misc/logger.js\";\nimport { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nimport { GetPathToObjectConverter } from \"./objectModelMapping.js\";\nimport \"./KHR_animation_pointer.data.js\";\nconst NAME = \"KHR_animation_pointer\";\n/**\n * [Specification PR](https://github.com/KhronosGroup/glTF/pull/2147)\n * !!! Experimental Extension Subject to Changes !!!\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class KHR_animation_pointer {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        this._loader = loader;\n        this._pathToObjectConverter = GetPathToObjectConverter(this._loader.gltf);\n    }\n    /**\n     * Defines whether this extension is enabled.\n     */\n    get enabled() {\n        return this._loader.isExtensionUsed(NAME);\n    }\n    /** @internal */\n    dispose() {\n        this._loader = null;\n        delete this._pathToObjectConverter; // GC\n    }\n    /**\n     * Loads a glTF animation channel.\n     * @param context The context when loading the asset\n     * @param animationContext The context of the animation when loading the asset\n     * @param animation The glTF animation property\n     * @param channel The glTF animation channel property\n     * @param onLoad Called for each animation loaded\n     * @returns A void promise that resolves when the load is complete or null if not handled\n     */\n    _loadAnimationChannelAsync(context, animationContext, animation, channel, onLoad) {\n        const extension = channel.target.extensions?.KHR_animation_pointer;\n        if (!extension || !this._pathToObjectConverter) {\n            return null;\n        }\n        if (channel.target.path !== \"pointer\" /* AnimationChannelTargetPath.POINTER */) {\n            Logger.Warn(`${context}/target/path: Value (${channel.target.path}) must be (${\"pointer\" /* AnimationChannelTargetPath.POINTER */}) when using the ${this.name} extension`);\n        }\n        if (channel.target.node != undefined) {\n            Logger.Warn(`${context}/target/node: Value (${channel.target.node}) must not be present when using the ${this.name} extension`);\n        }\n        const extensionContext = `${context}/extensions/${this.name}`;\n        const pointer = extension.pointer;\n        if (!pointer) {\n            throw new Error(`${extensionContext}: Pointer is missing`);\n        }\n        try {\n            const obj = this._pathToObjectConverter.convert(pointer);\n            if (!obj.info.interpolation) {\n                throw new Error(`${extensionContext}/pointer: Interpolation is missing`);\n            }\n            return this._loader._loadAnimationChannelFromTargetInfoAsync(context, animationContext, animation, channel, {\n                object: obj.object,\n                info: obj.info.interpolation,\n            }, onLoad);\n        }\n        catch (e) {\n            Logger.Warn(`${extensionContext}/pointer: Invalid pointer (${pointer}) skipped`);\n            return null;\n        }\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new KHR_animation_pointer(loader));\n//# sourceMappingURL=KHR_animation_pointer.js.map"], "names": ["getColor3", "_target", "source", "offset", "scale", "Color3", "get<PERSON><PERSON><PERSON>", "getFloat", "getMinusFloat", "getNextFloat", "getFloatBy2", "getTextureTransformTree", "textureName", "MaterialAnimationPropertyInfo", "Animation", "CameraAnimationPropertyInfo", "AnimationPropertyInfo", "target", "name", "fps", "keys", "babylonAnimations", "fillMode", "LightAnimationPropertyInfo", "SetInterpolationForKey", "baseColorTextureInterpolation", "metallicRoughnessTextureInterpolation", "normalTextureInterpolation", "occlusionTextureInterpolation", "emissiveTextureInterpolation", "anisotropyTextureInterpolation", "clearcoatTextureInterpolation", "clearcoatNormalTextureInterpolation", "clearcoatRoughnessTextureInterpolation", "iridescenceTextureInterpolation", "iridescenceThicknessTextureInterpolation", "sheenTextureInterpolation", "sheenRoughnessTextureInterpolation", "specularTextureInterpolation", "specularColorTextureInterpolation", "transmissionTextureInterpolation", "thicknessTextureInterpolation", "diffuseTransmissionTextureInterpolation", "diffuseTransmissionColorTextureInterpolation", "NAME", "KHR_animation_pointer", "loader", "GetPathToObjectConverter", "context", "animationContext", "animation", "channel", "onLoad", "extension", "<PERSON><PERSON>", "extensionContext", "pointer", "obj", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "oQAKA,SAASA,EAAUC,EAASC,EAAQC,EAAQC,EAAO,CAC/C,OAAOC,EAAO,UAAUH,EAAQC,CAAM,EAAE,MAAMC,CAAK,CACvD,CACA,SAASE,EAASL,EAASC,EAAQC,EAAQC,EAAO,CAC9C,OAAOF,EAAOC,EAAS,CAAC,EAAIC,CAChC,CACA,SAASG,EAASN,EAASC,EAAQC,EAAQC,EAAO,CAC9C,OAAOF,EAAOC,CAAM,EAAIC,CAC5B,CACA,SAASI,EAAcP,EAASC,EAAQC,EAAQC,EAAO,CACnD,MAAO,CAACF,EAAOC,CAAM,EAAIC,CAC7B,CACA,SAASK,EAAaR,EAASC,EAAQC,EAAQC,EAAO,CAClD,OAAOF,EAAOC,EAAS,CAAC,EAAIC,CAChC,CACA,SAASM,EAAYT,EAASC,EAAQC,EAAQC,EAAO,CACjD,OAAOF,EAAOC,CAAM,EAAIC,EAAQ,CACpC,CACA,SAASO,EAAwBC,EAAa,CAC1C,MAAO,CACH,MAAO,CACH,IAAIC,EAA8BC,EAAU,oBAAqB,GAAGF,CAAW,UAAWL,EAAU,IAAM,CAAC,EAC3G,IAAIM,EAA8BC,EAAU,oBAAqB,GAAGF,CAAW,UAAWH,EAAc,IAAM,CAAC,CAClH,EACD,OAAQ,CACJ,IAAII,EAA8BC,EAAU,oBAAqB,GAAGF,CAAW,WAAYL,EAAU,IAAM,CAAC,EAC5G,IAAIM,EAA8BC,EAAU,oBAAqB,GAAGF,CAAW,WAAYH,EAAc,IAAM,CAAC,CACnH,EACD,SAAU,CAAC,IAAII,EAA8BC,EAAU,oBAAqB,GAAGF,CAAW,QAASJ,EAAe,IAAM,CAAC,CAAC,CAClI,CACA,CACA,MAAMO,UAAoCC,CAAsB,CAE5D,gBAAgBC,EAAQC,EAAMC,EAAKC,EAAM,CACrC,MAAO,CAAC,CAAE,kBAAmBH,EAAO,eAAgB,iBAAkB,KAAK,gBAAgBC,EAAMC,EAAKC,CAAI,CAAG,CAAA,CAChH,CACL,CACA,MAAMP,UAAsCG,CAAsB,CAE9D,gBAAgBC,EAAQC,EAAMC,EAAKC,EAAM,CACrC,MAAMC,EAAoB,CAAA,EAC1B,UAAWC,KAAYL,EAAO,MAC1BI,EAAkB,KAAK,CACnB,kBAAmBJ,EAAO,MAAMK,CAAQ,EAAE,gBAC1C,iBAAkB,KAAK,gBAAgBJ,EAAMC,EAAKC,CAAI,CACtE,CAAa,EAEL,OAAOC,CACV,CACL,CACA,MAAME,UAAmCP,CAAsB,CAE3D,gBAAgBC,EAAQC,EAAMC,EAAKC,EAAM,CACrC,MAAO,CAAC,CAAE,kBAAmBH,EAAO,cAAe,iBAAkB,KAAK,gBAAgBC,EAAMC,EAAKC,CAAI,CAAG,CAAA,CAC/G,CACL,CACAI,EAAuB,gCAAiC,CACpD,IAAIT,EAA4BD,EAAU,oBAAqB,YAAaN,EAAe,IAAM,CAAC,EAClG,IAAIO,EAA4BD,EAAU,oBAAqB,aAAcL,EAAc,IAAM,CAAC,CACtG,CAAC,EACDe,EAAuB,gCAAiC,CACpD,IAAIT,EAA4BD,EAAU,oBAAqB,cAAeN,EAAe,IAAM,CAAC,EACpG,IAAIO,EAA4BD,EAAU,oBAAqB,WAAYL,EAAc,IAAM,CAAC,CACpG,CAAC,EACDe,EAAuB,gCAAiC,CAAC,IAAIT,EAA4BD,EAAU,oBAAqB,OAAQP,EAAU,IAAM,CAAC,CAAC,CAAC,EACnJiB,EAAuB,iCAAkC,CAAC,IAAIT,EAA4BD,EAAU,oBAAqB,OAAQP,EAAU,IAAM,CAAC,CAAC,CAAC,EACpJiB,EAAuB,+BAAgC,CAAC,IAAIT,EAA4BD,EAAU,oBAAqB,MAAOP,EAAU,IAAM,CAAC,CAAC,CAAC,EACjJiB,EAAuB,+BAAgC,CAAC,IAAIT,EAA4BD,EAAU,oBAAqB,OAAQP,EAAU,IAAM,CAAC,CAAC,CAAC,EAClJiB,EAAuB,gCAAiC,CAAC,IAAIT,EAA4BD,EAAU,oBAAqB,OAAQP,EAAU,IAAM,CAAC,CAAC,CAAC,EAEnJiB,EAAuB,qDAAsD,CACzE,IAAIX,EAA8BC,EAAU,qBAAsB,cAAed,EAAW,IAAM,CAAC,EACnG,IAAIa,EAA8BC,EAAU,oBAAqB,QAASR,EAAU,IAAM,CAAC,CAC/F,CAAC,EACDkB,EAAuB,oDAAqD,CAAC,IAAIX,EAA8BC,EAAU,oBAAqB,WAAYP,EAAU,IAAM,CAAC,CAAC,CAAC,EAC7KiB,EAAuB,oDAAqD,CAAC,IAAIX,EAA8BC,EAAU,oBAAqB,YAAaP,EAAU,IAAM,CAAC,CAAC,CAAC,EAC9K,MAAMkB,EAAgCd,EAAwB,eAAe,EAC7Ea,EAAuB,6FAA8FC,EAA8B,KAAK,EACxJD,EAAuB,8FAA+FC,EAA8B,MAAM,EAC1JD,EAAuB,gGAAiGC,EAA8B,QAAQ,EAC9J,MAAMC,EAAwCf,EAAwB,iBAAiB,EACvFa,EAAuB,qEAAsEE,EAAsC,KAAK,EACxIF,EAAuB,sEAAuEE,EAAsC,MAAM,EAC1IF,EAAuB,wEAAyEE,EAAsC,QAAQ,EAC9IF,EAAuB,+BAAgC,CAAC,IAAIX,EAA8BC,EAAU,qBAAsB,gBAAiBd,EAAW,IAAM,CAAC,CAAC,CAAC,EAC/J,MAAM2B,EAA6BhB,EAAwB,aAAa,EACxEa,EAAuB,oCAAqC,CAAC,IAAIX,EAA8BC,EAAU,oBAAqB,oBAAqBP,EAAU,IAAM,CAAC,CAAC,CAAC,EACtKiB,EAAuB,qEAAsEG,EAA2B,KAAK,EAC7HH,EAAuB,sEAAuEG,EAA2B,MAAM,EAC/HH,EAAuB,wEAAyEG,EAA2B,QAAQ,EACnIH,EAAuB,0CAA2C,CAAC,IAAIX,EAA8BC,EAAU,oBAAqB,yBAA0BP,EAAU,IAAM,CAAC,CAAC,CAAC,EACjL,MAAMqB,EAAgCjB,EAAwB,gBAAgB,EAC9Ea,EAAuB,wEAAyEI,EAA8B,KAAK,EACnIJ,EAAuB,yEAA0EI,EAA8B,MAAM,EACrIJ,EAAuB,2EAA4EI,EAA8B,QAAQ,EACzI,MAAMC,EAA+BlB,EAAwB,iBAAiB,EAC9Ea,EAAuB,uEAAwEK,EAA6B,KAAK,EACjIL,EAAuB,wEAAyEK,EAA6B,MAAM,EACnIL,EAAuB,0EAA2EK,EAA6B,QAAQ,EAEvIL,EAAuB,uEAAwE,CAC3F,IAAIX,EAA8BC,EAAU,oBAAqB,uBAAwBP,EAAU,IAAM,CAAC,CAC9G,CAAC,EACDiB,EAAuB,uEAAwE,CAC3F,IAAIX,EAA8BC,EAAU,oBAAqB,mBAAoBP,EAAU,IAAM,CAAC,CAC1G,CAAC,EACD,MAAMuB,EAAiCnB,EAAwB,oBAAoB,EACnFa,EAAuB,6GAA8GM,EAA+B,KAAK,EACzKN,EAAuB,8GAA+GM,EAA+B,MAAM,EAC3KN,EAAuB,gHAAiHM,EAA+B,QAAQ,EAC/KN,EAAuB,mEAAoE,CACvF,IAAIX,EAA8BC,EAAU,oBAAqB,sBAAuBP,EAAU,IAAM,CAAC,CAC7G,CAAC,EACDiB,EAAuB,4EAA6E,CAChG,IAAIX,EAA8BC,EAAU,oBAAqB,sBAAuBP,EAAU,IAAM,CAAC,CAC7G,CAAC,EACD,MAAMwB,EAAgCpB,EAAwB,mBAAmB,EACjFa,EAAuB,2GAA4GO,EAA8B,KAAK,EACtKP,EAAuB,4GAA6GO,EAA8B,MAAM,EACxKP,EAAuB,8GAA+GO,EAA8B,QAAQ,EAC5K,MAAMC,EAAsCrB,EAAwB,uBAAuB,EAC3Fa,EAAuB,gFAAiF,CACpG,IAAIX,EAA8BC,EAAU,oBAAqB,8BAA+BP,EAAU,IAAM,CAAC,CACrH,CAAC,EACDiB,EAAuB,iHAAkHQ,EAAoC,KAAK,EAClLR,EAAuB,kHAAmHQ,EAAoC,MAAM,EACpLR,EAAuB,oHAAqHQ,EAAoC,QAAQ,EACxL,MAAMC,EAAyCtB,EAAwB,4BAA4B,EACnGa,EAAuB,oHAAqHS,EAAuC,KAAK,EACxLT,EAAuB,qHAAsHS,EAAuC,MAAM,EAC1LT,EAAuB,uHAAwHS,EAAuC,QAAQ,EAC9LT,EAAuB,qEAAsE,CACzF,IAAIX,EAA8BC,EAAU,oBAAqB,wBAAyBP,EAAU,IAAM,CAAC,CAC/G,CAAC,EACDiB,EAAuB,4EAA6E,CAChG,IAAIX,EAA8BC,EAAU,oBAAqB,oBAAqBP,EAAU,IAAM,CAAC,CAC3G,CAAC,EACDiB,EAAuB,iDAAkD,CACrE,IAAIX,EAA8BC,EAAU,oBAAqB,oBAAqBP,EAAU,IAAM,CAAC,CAC3G,CAAC,EACDiB,EAAuB,uEAAwE,CAC3F,IAAIX,EAA8BC,EAAU,oBAAqB,wBAAyBP,EAAU,IAAM,CAAC,CAC/G,CAAC,EACDiB,EAAuB,oEAAqE,CACxF,IAAIX,EAA8BC,EAAU,oBAAqB,gCAAiCP,EAAU,IAAM,CAAC,CACvH,CAAC,EACDiB,EAAuB,iFAAkF,CACrG,IAAIX,EAA8BC,EAAU,oBAAqB,+BAAgCP,EAAU,IAAM,CAAC,CACtH,CAAC,EACDiB,EAAuB,iFAAkF,CACrG,IAAIX,EAA8BC,EAAU,oBAAqB,+BAAgCP,EAAU,IAAM,CAAC,CACtH,CAAC,EACD,MAAM2B,EAAkCvB,EAAwB,qBAAqB,EACrFa,EAAuB,+GAAgHU,EAAgC,KAAK,EAC5KV,EAAuB,gHAAiHU,EAAgC,MAAM,EAC9KV,EAAuB,kHAAmHU,EAAgC,QAAQ,EAClL,MAAMC,EAA2CxB,EAAwB,8BAA8B,EACvGa,EAAuB,wHAAyHW,EAAyC,KAAK,EAC9LX,EAAuB,yHAA0HW,EAAyC,MAAM,EAChMX,EAAuB,2HAA4HW,EAAyC,QAAQ,EACpMX,EAAuB,gEAAiE,CACpF,IAAIX,EAA8BC,EAAU,qBAAsB,cAAed,EAAW,IAAM,CAAC,CACvG,CAAC,EACDwB,EAAuB,oEAAqE,CACxF,IAAIX,EAA8BC,EAAU,oBAAqB,kBAAmBP,EAAU,IAAM,CAAC,CACzG,CAAC,EACD,MAAM6B,EAA4BzB,EAAwB,eAAe,EACzEa,EAAuB,wGAAyGY,EAA0B,KAAK,EAC/JZ,EAAuB,yGAA0GY,EAA0B,MAAM,EACjKZ,EAAuB,2GAA4GY,EAA0B,QAAQ,EACrK,MAAMC,EAAqC1B,EAAwB,wBAAwB,EAC3Fa,EAAuB,4GAA6Ga,EAAmC,KAAK,EAC5Kb,EAAuB,6GAA8Ga,EAAmC,MAAM,EAC9Kb,EAAuB,+GAAgHa,EAAmC,QAAQ,EAClLb,EAAuB,iEAAkE,CACrF,IAAIX,EAA8BC,EAAU,oBAAqB,mBAAoBP,EAAU,IAAM,CAAC,CAC1G,CAAC,EACDiB,EAAuB,sEAAuE,CAC1F,IAAIX,EAA8BC,EAAU,qBAAsB,2BAA4Bd,EAAW,IAAM,CAAC,CACpH,CAAC,EACD,MAAMsC,EAA+B3B,EAAwB,4BAA4B,EACzFa,EAAuB,yGAA0Gc,EAA6B,KAAK,EACnKd,EAAuB,0GAA2Gc,EAA6B,MAAM,EACrKd,EAAuB,4GAA6Gc,EAA6B,QAAQ,EACzK,MAAMC,EAAoC5B,EAAwB,oBAAoB,EACtFa,EAAuB,8GAA+Ge,EAAkC,KAAK,EAC7Kf,EAAuB,+GAAgHe,EAAkC,MAAM,EAC/Kf,EAAuB,iHAAkHe,EAAkC,QAAQ,EACnLf,EAAuB,yEAA0E,CAC7F,IAAIX,EAA8BC,EAAU,oBAAqB,iCAAkCP,EAAU,IAAM,CAAC,CACxH,CAAC,EACD,MAAMiC,EAAmC7B,EAAwB,uCAAuC,EACxGa,EAAuB,iHAAkHgB,EAAiC,KAAK,EAC/KhB,EAAuB,kHAAmHgB,EAAiC,MAAM,EACjLhB,EAAuB,oHAAqHgB,EAAiC,QAAQ,EACrLhB,EAAuB,iEAAkE,CACrF,IAAIX,EAA8BC,EAAU,qBAAsB,uBAAwBd,EAAW,IAAM,CAAC,CAChH,CAAC,EACDwB,EAAuB,oEAAqE,CACxF,IAAIX,EAA8BC,EAAU,oBAAqB,iCAAkCP,EAAU,IAAM,CAAC,CACxH,CAAC,EACDiB,EAAuB,gEAAiE,CACpF,IAAIX,EAA8BC,EAAU,oBAAqB,8BAA+BP,EAAU,IAAM,CAAC,CACrH,CAAC,EACD,MAAMkC,EAAgC9B,EAAwB,6BAA6B,EAC3Fa,EAAuB,wGAAyGiB,EAA8B,KAAK,EACnKjB,EAAuB,yGAA0GiB,EAA8B,MAAM,EACrKjB,EAAuB,2GAA4GiB,EAA8B,QAAQ,EACzKjB,EAAuB,wFAAyF,CAC5G,IAAIX,EAA8BC,EAAU,oBAAqB,mCAAoCP,EAAU,IAAM,CAAC,CAC1H,CAAC,EACD,MAAMmC,EAA0C/B,EAAwB,yCAAyC,EACjHa,EAAuB,+HAAgIkB,EAAwC,KAAK,EACpMlB,EAAuB,gIAAiIkB,EAAwC,MAAM,EACtMlB,EAAuB,kIAAmIkB,EAAwC,QAAQ,EAC1MlB,EAAuB,6FAA8F,CACjH,IAAIX,EAA8BC,EAAU,qBAAsB,+BAAgCd,EAAW,IAAM,CAAC,CACxH,CAAC,EACD,MAAM2C,EAA+ChC,EAAwB,qCAAqC,EAClHa,EAAuB,oIAAqImB,EAA6C,KAAK,EAC9MnB,EAAuB,qIAAsImB,EAA6C,MAAM,EAChNnB,EAAuB,uIAAwImB,EAA6C,QAAQ,EACpNnB,EAAuB,kDAAmD,CAAC,IAAID,EAA2BT,EAAU,qBAAsB,UAAWd,EAAW,IAAM,CAAC,CAAC,CAAC,EACzKwB,EAAuB,sDAAuD,CAAC,IAAID,EAA2BT,EAAU,oBAAqB,YAAaP,EAAU,IAAM,CAAC,CAAC,CAAC,EAC7KiB,EAAuB,kDAAmD,CAAC,IAAID,EAA2BT,EAAU,oBAAqB,QAASP,EAAU,IAAM,CAAC,CAAC,CAAC,EACrKiB,EAAuB,gEAAiE,CACpF,IAAID,EAA2BT,EAAU,oBAAqB,aAAcJ,EAAa,IAAM,CAAC,CACpG,CAAC,EACDc,EAAuB,gEAAiE,CACpF,IAAID,EAA2BT,EAAU,oBAAqB,QAASJ,EAAa,IAAM,CAAC,CAC/F,CAAC,EACDc,EAAuB,4CAA6C,CAAC,IAAID,EAA2BT,EAAU,qBAAsB,UAAWd,EAAW,IAAM,CAAC,CAAC,CAAC,EACnKwB,EAAuB,iDAAkD,CAAC,IAAID,EAA2BT,EAAU,oBAAqB,YAAaP,EAAU,IAAM,CAAC,CAAC,CAAC,ECzOxK,MAAMqC,EAAO,wBAMN,MAAMC,CAAsB,CAI/B,YAAYC,EAAQ,CAIhB,KAAK,KAAOF,EACZ,KAAK,QAAUE,EACf,KAAK,uBAAyBC,EAAyB,KAAK,QAAQ,IAAI,CAC3E,CAID,IAAI,SAAU,CACV,OAAO,KAAK,QAAQ,gBAAgBH,CAAI,CAC3C,CAED,SAAU,CACN,KAAK,QAAU,KACf,OAAO,KAAK,sBACf,CAUD,2BAA2BI,EAASC,EAAkBC,EAAWC,EAASC,EAAQ,CAC9E,MAAMC,EAAYF,EAAQ,OAAO,YAAY,sBAC7C,GAAI,CAACE,GAAa,CAAC,KAAK,uBACpB,OAAO,KAEPF,EAAQ,OAAO,OAAS,WACxBG,EAAO,KAAK,GAAGN,CAAO,wBAAwBG,EAAQ,OAAO,IAAI,sCAAoF,KAAK,IAAI,YAAY,EAE1KA,EAAQ,OAAO,MAAQ,MACvBG,EAAO,KAAK,GAAGN,CAAO,wBAAwBG,EAAQ,OAAO,IAAI,wCAAwC,KAAK,IAAI,YAAY,EAElI,MAAMI,EAAmB,GAAGP,CAAO,eAAe,KAAK,IAAI,GACrDQ,EAAUH,EAAU,QAC1B,GAAI,CAACG,EACD,MAAM,IAAI,MAAM,GAAGD,CAAgB,sBAAsB,EAE7D,GAAI,CACA,MAAME,EAAM,KAAK,uBAAuB,QAAQD,CAAO,EACvD,GAAI,CAACC,EAAI,KAAK,cACV,MAAM,IAAI,MAAM,GAAGF,CAAgB,oCAAoC,EAE3E,OAAO,KAAK,QAAQ,yCAAyCP,EAASC,EAAkBC,EAAWC,EAAS,CACxG,OAAQM,EAAI,OACZ,KAAMA,EAAI,KAAK,aAClB,EAAEL,CAAM,CACZ,MACS,CACN,OAAAE,EAAO,KAAK,GAAGC,CAAgB,8BAA8BC,CAAO,WAAW,EACxE,IACV,CACJ,CACL,CACAE,EAAwBd,CAAI,EAC5Be,EAAsBf,EAAM,GAAOE,GAAW,IAAID,EAAsBC,CAAM,CAAC", "x_google_ignoreList": [0, 1]}