import{M as f}from"./MarkdownCode-BRQ4PUpt.js";/* empty css                                                        */const{SvelteComponent:m,attr:u,create_component:c,destroy_component:l,detach:_,element:d,flush:a,init:g,insert:h,mount_component:$,safe_not_equal:w,transition_in:v,transition_out:k}=window.__gradio__svelte__internal;function p(i){let t,n,r;return n=new f({props:{root:i[1],message:i[0],sanitize_html:!0}}),{c(){t=d("div"),c(n.$$.fragment),u(t,"class","svelte-j9uq24")},m(e,o){h(e,t,o),$(n,t,null),r=!0},p(e,[o]){const s={};o&2&&(s.root=e[1]),o&1&&(s.message=e[0]),n.$set(s)},i(e){r||(v(n.$$.fragment,e),r=!0)},o(e){k(n.$$.fragment,e),r=!1},d(e){e&&_(t),l(n)}}}function q(i,t,n){let{info:r}=t,{root:e}=t;return i.$$set=o=>{"info"in o&&n(0,r=o.info),"root"in o&&n(1,e=o.root)},[r,e]}class I extends m{constructor(t){super(),g(this,t,q,p,w,{info:0,root:1})}get info(){return this.$$.ctx[0]}set info(t){this.$$set({info:t}),a()}get root(){return this.$$.ctx[1]}set root(t){this.$$set({root:t}),a()}}export{I};
//# sourceMappingURL=Info-BpelqhYn.js.map
