import{ag as xt,by as pe,A as k,h as y,bz as ee,bA as Bt,bB as Ie,bC as vt,bD as Rt,au as At,bE as Ce,aa as S,bF as Dt,bG as Pt,I as W,j as re,b as q,ac as Ft,bH as Lt,bI as Mt,bJ as Et,aM as U,bK as Gt,bL as Ot,aS as ce,bM as Ut,bN as Nt,bO as $t,bP as Vt,bQ as Wt,bR as kt,bS as qt,bT as Qt,bU as Ht,bV as Yt,bW as zt,ai as jt}from"./index-Cb4A4-Xi.js";import"./bakedVertexAnimation-w9ZZqSSS.js";import"./helperFunctions-DzxrWFCN.js";import"./fresnelFunction-COC4uPN7.js";import"./meshUboDeclaration-DByRfuEp.js";import"./decalFragment-D9EEhdKW.js";import"./audioEngine-D-crK827.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";class C{static ComputeNumMipmapLevels(e,t){return xt(Math.max(e,t))+1}static GetTextureTypeFromFormat(e){switch(e){case"r8unorm":case"r8uint":case"rg8unorm":case"rg8uint":case"rgba8unorm":case"rgba8unorm-srgb":case"rgba8uint":case"bgra8unorm":case"bgra8unorm-srgb":case"rgb10a2uint":case"rgb10a2unorm":case"rgb9e5ufloat":case"rg11b10ufloat":case"bc7-rgba-unorm":case"bc7-rgba-unorm-srgb":case"bc6h-rgb-ufloat":case"bc5-rg-unorm":case"bc3-rgba-unorm":case"bc3-rgba-unorm-srgb":case"bc2-rgba-unorm":case"bc2-rgba-unorm-srgb":case"bc4-r-unorm":case"bc1-rgba-unorm":case"bc1-rgba-unorm-srgb":case"etc2-rgb8unorm":case"etc2-rgb8unorm-srgb":case"etc2-rgb8a1unorm":case"etc2-rgb8a1unorm-srgb":case"etc2-rgba8unorm":case"etc2-rgba8unorm-srgb":case"eac-r11unorm":case"eac-rg11unorm":case"astc-4x4-unorm":case"astc-4x4-unorm-srgb":case"astc-5x4-unorm":case"astc-5x4-unorm-srgb":case"astc-5x5-unorm":case"astc-5x5-unorm-srgb":case"astc-6x5-unorm":case"astc-6x5-unorm-srgb":case"astc-6x6-unorm":case"astc-6x6-unorm-srgb":case"astc-8x5-unorm":case"astc-8x5-unorm-srgb":case"astc-8x6-unorm":case"astc-8x6-unorm-srgb":case"astc-8x8-unorm":case"astc-8x8-unorm-srgb":case"astc-10x5-unorm":case"astc-10x5-unorm-srgb":case"astc-10x6-unorm":case"astc-10x6-unorm-srgb":case"astc-10x8-unorm":case"astc-10x8-unorm-srgb":case"astc-10x10-unorm":case"astc-10x10-unorm-srgb":case"astc-12x10-unorm":case"astc-12x10-unorm-srgb":case"astc-12x12-unorm":case"astc-12x12-unorm-srgb":case"stencil8":return 0;case"r8snorm":case"r8sint":case"rg8snorm":case"rg8sint":case"rgba8snorm":case"rgba8sint":case"bc6h-rgb-float":case"bc5-rg-snorm":case"bc4-r-snorm":case"eac-r11snorm":case"eac-rg11snorm":return 3;case"r16uint":case"r16unorm":case"rg16unorm":case"rgba16unorm":case"rg16uint":case"rgba16uint":case"depth16unorm":return 5;case"r16sint":case"r16snorm":case"rg16snorm":case"rgba16snorm":case"rg16sint":case"rgba16sint":return 4;case"r16float":case"rg16float":case"rgba16float":return 2;case"r32uint":case"rg32uint":case"rgba32uint":return 7;case"r32sint":case"rg32sint":case"rgba32sint":return 7;case"r32float":case"rg32float":case"rgba32float":case"depth32float":case"depth32float-stencil8":case"depth24plus":case"depth24plus-stencil8":return 1}return 0}static GetBlockInformationFromFormat(e){switch(e){case"r8unorm":case"r8snorm":case"r8uint":case"r8sint":return{width:1,height:1,length:1};case"r16uint":case"r16sint":case"r16unorm":case"rg16unorm":case"rgba16unorm":case"r16snorm":case"rg16snorm":case"rgba16snorm":case"r16float":case"rg8unorm":case"rg8snorm":case"rg8uint":case"rg8sint":return{width:1,height:1,length:2};case"r32uint":case"r32sint":case"r32float":case"rg16uint":case"rg16sint":case"rg16float":case"rgba8unorm":case"rgba8unorm-srgb":case"rgba8snorm":case"rgba8uint":case"rgba8sint":case"bgra8unorm":case"bgra8unorm-srgb":case"rgb9e5ufloat":case"rgb10a2uint":case"rgb10a2unorm":case"rg11b10ufloat":return{width:1,height:1,length:4};case"rg32uint":case"rg32sint":case"rg32float":case"rgba16uint":case"rgba16sint":case"rgba16float":return{width:1,height:1,length:8};case"rgba32uint":case"rgba32sint":case"rgba32float":return{width:1,height:1,length:16};case"stencil8":throw"No fixed size for Stencil8 format!";case"depth16unorm":return{width:1,height:1,length:2};case"depth24plus":throw"No fixed size for Depth24Plus format!";case"depth24plus-stencil8":throw"No fixed size for Depth24PlusStencil8 format!";case"depth32float":return{width:1,height:1,length:4};case"depth32float-stencil8":return{width:1,height:1,length:5};case"bc7-rgba-unorm":case"bc7-rgba-unorm-srgb":case"bc6h-rgb-ufloat":case"bc6h-rgb-float":case"bc5-rg-unorm":case"bc5-rg-snorm":case"bc3-rgba-unorm":case"bc3-rgba-unorm-srgb":case"bc2-rgba-unorm":case"bc2-rgba-unorm-srgb":return{width:4,height:4,length:16};case"bc4-r-unorm":case"bc4-r-snorm":case"bc1-rgba-unorm":case"bc1-rgba-unorm-srgb":return{width:4,height:4,length:8};case"etc2-rgb8unorm":case"etc2-rgb8unorm-srgb":case"etc2-rgb8a1unorm":case"etc2-rgb8a1unorm-srgb":case"eac-r11unorm":case"eac-r11snorm":return{width:4,height:4,length:8};case"etc2-rgba8unorm":case"etc2-rgba8unorm-srgb":case"eac-rg11unorm":case"eac-rg11snorm":return{width:4,height:4,length:16};case"astc-4x4-unorm":case"astc-4x4-unorm-srgb":return{width:4,height:4,length:16};case"astc-5x4-unorm":case"astc-5x4-unorm-srgb":return{width:5,height:4,length:16};case"astc-5x5-unorm":case"astc-5x5-unorm-srgb":return{width:5,height:5,length:16};case"astc-6x5-unorm":case"astc-6x5-unorm-srgb":return{width:6,height:5,length:16};case"astc-6x6-unorm":case"astc-6x6-unorm-srgb":return{width:6,height:6,length:16};case"astc-8x5-unorm":case"astc-8x5-unorm-srgb":return{width:8,height:5,length:16};case"astc-8x6-unorm":case"astc-8x6-unorm-srgb":return{width:8,height:6,length:16};case"astc-8x8-unorm":case"astc-8x8-unorm-srgb":return{width:8,height:8,length:16};case"astc-10x5-unorm":case"astc-10x5-unorm-srgb":return{width:10,height:5,length:16};case"astc-10x6-unorm":case"astc-10x6-unorm-srgb":return{width:10,height:6,length:16};case"astc-10x8-unorm":case"astc-10x8-unorm-srgb":return{width:10,height:8,length:16};case"astc-10x10-unorm":case"astc-10x10-unorm-srgb":return{width:10,height:10,length:16};case"astc-12x10-unorm":case"astc-12x10-unorm-srgb":return{width:12,height:10,length:16};case"astc-12x12-unorm":case"astc-12x12-unorm-srgb":return{width:12,height:12,length:16}}return{width:1,height:1,length:4}}static IsHardwareTexture(e){return!!e.release}static IsInternalTexture(e){return!!e.dispose}static IsImageBitmap(e){return e.close!==void 0}static IsImageBitmapArray(e){return Array.isArray(e)&&e[0].close!==void 0}static IsCompressedFormat(e){switch(e){case"bc7-rgba-unorm-srgb":case"bc7-rgba-unorm":case"bc6h-rgb-float":case"bc6h-rgb-ufloat":case"bc5-rg-snorm":case"bc5-rg-unorm":case"bc4-r-snorm":case"bc4-r-unorm":case"bc3-rgba-unorm-srgb":case"bc3-rgba-unorm":case"bc2-rgba-unorm-srgb":case"bc2-rgba-unorm":case"bc1-rgba-unorm-srgb":case"bc1-rgba-unorm":case"etc2-rgb8unorm":case"etc2-rgb8unorm-srgb":case"etc2-rgb8a1unorm":case"etc2-rgb8a1unorm-srgb":case"etc2-rgba8unorm":case"etc2-rgba8unorm-srgb":case"eac-r11unorm":case"eac-r11snorm":case"eac-rg11unorm":case"eac-rg11snorm":case"astc-4x4-unorm":case"astc-4x4-unorm-srgb":case"astc-5x4-unorm":case"astc-5x4-unorm-srgb":case"astc-5x5-unorm":case"astc-5x5-unorm-srgb":case"astc-6x5-unorm":case"astc-6x5-unorm-srgb":case"astc-6x6-unorm":case"astc-6x6-unorm-srgb":case"astc-8x5-unorm":case"astc-8x5-unorm-srgb":case"astc-8x6-unorm":case"astc-8x6-unorm-srgb":case"astc-8x8-unorm":case"astc-8x8-unorm-srgb":case"astc-10x5-unorm":case"astc-10x5-unorm-srgb":case"astc-10x6-unorm":case"astc-10x6-unorm-srgb":case"astc-10x8-unorm":case"astc-10x8-unorm-srgb":case"astc-10x10-unorm":case"astc-10x10-unorm-srgb":case"astc-12x10-unorm":case"astc-12x10-unorm-srgb":case"astc-12x12-unorm":case"astc-12x12-unorm-srgb":return!0}return!1}static GetWebGPUTextureFormat(e,t,r=!1){switch(t){case 15:return"depth16unorm";case 16:return"depth24plus";case 13:return"depth24plus-stencil8";case 14:return"depth32float";case 18:return"depth32float-stencil8";case 19:return"stencil8";case 36492:return r?"bc7-rgba-unorm-srgb":"bc7-rgba-unorm";case 36495:return"bc6h-rgb-ufloat";case 36494:return"bc6h-rgb-float";case 33779:return r?"bc3-rgba-unorm-srgb":"bc3-rgba-unorm";case 33778:return r?"bc2-rgba-unorm-srgb":"bc2-rgba-unorm";case 33777:case 33776:return r?"bc1-rgba-unorm-srgb":"bc1-rgba-unorm";case 37808:return r?"astc-4x4-unorm-srgb":"astc-4x4-unorm";case 36196:case 37492:return r?"etc2-rgb8unorm-srgb":"etc2-rgb8unorm";case 37496:return r?"etc2-rgba8unorm-srgb":"etc2-rgba8unorm"}switch(e){case 3:switch(t){case 6:return"r8snorm";case 7:return"rg8snorm";case 4:throw"RGB format not supported in WebGPU";case 8:return"r8sint";case 9:return"rg8sint";case 10:throw"RGB_INTEGER format not supported in WebGPU";case 11:return"rgba8sint";default:return"rgba8snorm"}case 0:switch(t){case 6:return"r8unorm";case 7:return"rg8unorm";case 4:throw"TEXTUREFORMAT_RGB format not supported in WebGPU";case 5:return r?"rgba8unorm-srgb":"rgba8unorm";case 12:return r?"bgra8unorm-srgb":"bgra8unorm";case 8:return"r8uint";case 9:return"rg8uint";case 10:throw"RGB_INTEGER format not supported in WebGPU";case 11:return"rgba8uint";case 0:throw"TEXTUREFORMAT_ALPHA format not supported in WebGPU";case 1:throw"TEXTUREFORMAT_LUMINANCE format not supported in WebGPU";case 2:throw"TEXTUREFORMAT_LUMINANCE_ALPHA format not supported in WebGPU";default:return"rgba8unorm"}case 4:switch(t){case 8:return"r16sint";case 9:return"rg16sint";case 10:throw"TEXTUREFORMAT_RGB_INTEGER format not supported in WebGPU";case 11:return"rgba16sint";default:return"rgba16sint"}case 5:switch(t){case 8:return"r16uint";case 9:return"rg16uint";case 10:throw"TEXTUREFORMAT_RGB_INTEGER format not supported in WebGPU";case 11:return"rgba16uint";default:return"rgba16uint"}case 6:switch(t){case 8:return"r32sint";case 9:return"rg32sint";case 10:throw"TEXTUREFORMAT_RGB_INTEGER format not supported in WebGPU";case 11:return"rgba32sint";default:return"rgba32sint"}case 7:switch(t){case 8:return"r32uint";case 9:return"rg32uint";case 10:throw"TEXTUREFORMAT_RGB_INTEGER format not supported in WebGPU";case 11:return"rgba32uint";default:return"rgba32uint"}case 1:switch(t){case 6:return"r32float";case 7:return"rg32float";case 4:throw"TEXTUREFORMAT_RGB format not supported in WebGPU";case 5:return"rgba32float";default:return"rgba32float"}case 2:switch(t){case 6:return"r16float";case 7:return"rg16float";case 4:throw"TEXTUREFORMAT_RGB format not supported in WebGPU";case 5:return"rgba16float";default:return"rgba16float"}case 10:throw"TEXTURETYPE_UNSIGNED_SHORT_5_6_5 format not supported in WebGPU";case 13:switch(t){case 5:return"rg11b10ufloat";case 11:throw"TEXTUREFORMAT_RGBA_INTEGER format not supported in WebGPU when type is TEXTURETYPE_UNSIGNED_INT_10F_11F_11F_REV";default:return"rg11b10ufloat"}case 14:switch(t){case 5:return"rgb9e5ufloat";case 11:throw"TEXTUREFORMAT_RGBA_INTEGER format not supported in WebGPU when type is TEXTURETYPE_UNSIGNED_INT_5_9_9_9_REV";default:return"rgb9e5ufloat"}case 8:throw"TEXTURETYPE_UNSIGNED_SHORT_4_4_4_4 format not supported in WebGPU";case 9:throw"TEXTURETYPE_UNSIGNED_SHORT_5_5_5_1 format not supported in WebGPU";case 11:switch(t){case 5:return"rgb10a2unorm";case 11:return"rgb10a2uint";default:return"rgb10a2unorm"}}return r?"rgba8unorm-srgb":"rgba8unorm"}static GetNumChannelsFromWebGPUTextureFormat(e){switch(e){case"r8unorm":case"r8snorm":case"r8uint":case"r8sint":case"bc4-r-unorm":case"bc4-r-snorm":case"r16uint":case"r16sint":case"depth16unorm":case"r16float":case"r16unorm":case"r16snorm":case"r32uint":case"r32sint":case"r32float":case"depth32float":case"stencil8":case"depth24plus":case"eac-r11unorm":case"eac-r11snorm":return 1;case"rg8unorm":case"rg8snorm":case"rg8uint":case"rg8sint":case"depth32float-stencil8":case"bc5-rg-unorm":case"bc5-rg-snorm":case"rg16uint":case"rg16sint":case"rg16float":case"rg16unorm":case"rg16snorm":case"rg32uint":case"rg32sint":case"rg32float":case"depth24plus-stencil8":case"eac-rg11unorm":case"eac-rg11snorm":return 2;case"rgb9e5ufloat":case"rg11b10ufloat":case"bc6h-rgb-ufloat":case"bc6h-rgb-float":case"etc2-rgb8unorm":case"etc2-rgb8unorm-srgb":return 3;case"rgba8unorm":case"rgba8unorm-srgb":case"rgba8snorm":case"rgba8uint":case"rgba8sint":case"bgra8unorm":case"bgra8unorm-srgb":case"rgba16unorm":case"rgba16snorm":case"rgb10a2uint":case"rgb10a2unorm":case"bc7-rgba-unorm":case"bc7-rgba-unorm-srgb":case"bc3-rgba-unorm":case"bc3-rgba-unorm-srgb":case"bc2-rgba-unorm":case"bc2-rgba-unorm-srgb":case"bc1-rgba-unorm":case"bc1-rgba-unorm-srgb":case"rgba16uint":case"rgba16sint":case"rgba16float":case"rgba32uint":case"rgba32sint":case"rgba32float":case"etc2-rgb8a1unorm":case"etc2-rgb8a1unorm-srgb":case"etc2-rgba8unorm":case"etc2-rgba8unorm-srgb":case"astc-4x4-unorm":case"astc-4x4-unorm-srgb":case"astc-5x4-unorm":case"astc-5x4-unorm-srgb":case"astc-5x5-unorm":case"astc-5x5-unorm-srgb":case"astc-6x5-unorm":case"astc-6x5-unorm-srgb":case"astc-6x6-unorm":case"astc-6x6-unorm-srgb":case"astc-8x5-unorm":case"astc-8x5-unorm-srgb":case"astc-8x6-unorm":case"astc-8x6-unorm-srgb":case"astc-8x8-unorm":case"astc-8x8-unorm-srgb":case"astc-10x5-unorm":case"astc-10x5-unorm-srgb":case"astc-10x6-unorm":case"astc-10x6-unorm-srgb":case"astc-10x8-unorm":case"astc-10x8-unorm-srgb":case"astc-10x10-unorm":case"astc-10x10-unorm-srgb":case"astc-12x10-unorm":case"astc-12x10-unorm-srgb":case"astc-12x12-unorm":case"astc-12x12-unorm-srgb":return 4}throw`Unknown format ${e}!`}static HasStencilAspect(e){switch(e){case"stencil8":case"depth32float-stencil8":case"depth24plus-stencil8":return!0}return!1}static HasDepthAndStencilAspects(e){switch(e){case"depth32float-stencil8":case"depth24plus-stencil8":return!0}return!1}static GetDepthFormatOnly(e){switch(e){case"depth16unorm":return"depth16unorm";case"depth24plus":return"depth24plus";case"depth24plus-stencil8":return"depth24plus";case"depth32float":return"depth32float";case"depth32float-stencil8":return"depth32float"}return e}static GetSample(e){return e>1?4:1}}class yt{constructor(){this._gpuTimeInFrameId=-1,this.counter=new pe}_addDuration(e,t){e<this._gpuTimeInFrameId||(this._gpuTimeInFrameId!==e?(this.counter._fetchResult(),this.counter.fetchNewFrame(),this.counter.addCount(t,!1),this._gpuTimeInFrameId=e):this.counter.addCount(t,!1))}}class A extends k{constructor(){super(...arguments),this.dbgShowShaderCode=!1,this.dbgSanityChecks=!0,this.dbgVerboseLogsNumFrames=10,this.dbgLogIfNotDrawWrapper=!0,this.dbgShowEmptyEnableEffectCalls=!0,this.dbgVerboseLogsForFirstFrames=!1,this._currentRenderPass=null,this._snapshotRenderingMode=0,this._timestampIndex=0,this._debugStackRenderPass=[]}get enableGPUTimingMeasurements(){return this._timestampQuery.enable}set enableGPUTimingMeasurements(e){this._timestampQuery.enable!==e&&(this.gpuTimeInFrameForMainPass=e?new yt:void 0,this._timestampQuery.enable=e)}_currentPassIsMainPass(){return this._currentRenderTarget===null}_endCurrentRenderPass(){if(!this._currentRenderPass)return 0;if(this._debugStackRenderPass.length!==0)for(let t=0;t<this._debugStackRenderPass.length;++t)this._currentRenderPass.popDebugGroup();const e=this._currentPassIsMainPass()?2:1;return!this._snapshotRendering.endRenderPass(this._currentRenderPass)&&!this.compatibilityMode&&(this._bundleList.run(this._currentRenderPass),this._bundleList.reset()),this._currentRenderPass.end(),this._timestampQuery.endPass(this._timestampIndex,this._currentRenderTarget&&this._currentRenderTarget.gpuTimeInFrame?this._currentRenderTarget.gpuTimeInFrame:this.gpuTimeInFrameForMainPass),this._timestampIndex+=2,this.dbgVerboseLogsForFirstFrames&&(this._count===void 0&&(this._count=0),(!this._count||this._count<this.dbgVerboseLogsNumFrames)&&y.Log("frame #"+this._count+" - "+(e===2?"main":"render target")+" end pass"+(e===1?" - internalTexture.uniqueId="+this._currentRenderTarget?.texture?.uniqueId:""))),this._debugPopGroup?.(0),this._currentRenderPass=null,e}_generateMipmaps(e,t){t=t??this._renderEncoder;const r=e._hardwareTexture;if(!r)return;t===this._renderEncoder&&this._endCurrentRenderPass();const n=e._hardwareTexture.format,i=C.ComputeNumMipmapLevels(e.width,e.height);this.dbgVerboseLogsForFirstFrames&&(this._count===void 0&&(this._count=0),(!this._count||this._count<this.dbgVerboseLogsNumFrames)&&y.Log("frame #"+this._count+" - generate mipmaps - width="+e.width+", height="+e.height+", isCube="+e.isCube+", command encoder="+(t===this._renderEncoder?"render":"copy"))),e.isCube?this._textureHelper.generateCubeMipmaps(r,n,i,t):this._textureHelper.generateMipmaps(r,n,i,0,e.is3D,t)}}var Te;(function(s){s.LowPower="low-power",s.HighPerformance="high-performance"})(Te||(Te={}));var Be;(function(s){s.DepthClipControl="depth-clip-control",s.Depth32FloatStencil8="depth32float-stencil8",s.TextureCompressionBC="texture-compression-bc",s.TextureCompressionBCSliced3D="texture-compression-bc-sliced-3d",s.TextureCompressionETC2="texture-compression-etc2",s.TextureCompressionASTC="texture-compression-astc",s.TextureCompressionASTCSliced3D="texture-compression-astc-sliced-3d",s.TimestampQuery="timestamp-query",s.IndirectFirstInstance="indirect-first-instance",s.ShaderF16="shader-f16",s.RG11B10UFloatRenderable="rg11b10ufloat-renderable",s.BGRA8UnormStorage="bgra8unorm-storage",s.Float32Filterable="float32-filterable",s.Float32Blendable="float32-blendable",s.ClipDistances="clip-distances",s.DualSourceBlending="dual-source-blending"})(Be||(Be={}));var ve;(function(s){s.Unmapped="unmapped",s.Pending="pending",s.Mapped="mapped"})(ve||(ve={}));var T;(function(s){s[s.MapRead=1]="MapRead",s[s.MapWrite=2]="MapWrite",s[s.CopySrc=4]="CopySrc",s[s.CopyDst=8]="CopyDst",s[s.Index=16]="Index",s[s.Vertex=32]="Vertex",s[s.Uniform=64]="Uniform",s[s.Storage=128]="Storage",s[s.Indirect=256]="Indirect",s[s.QueryResolve=512]="QueryResolve"})(T||(T={}));var Re;(function(s){s[s.Read=1]="Read",s[s.Write=2]="Write"})(Re||(Re={}));var Ae;(function(s){s.E1d="1d",s.E2d="2d",s.E3d="3d"})(Ae||(Ae={}));var De;(function(s){s[s.CopySrc=1]="CopySrc",s[s.CopyDst=2]="CopyDst",s[s.TextureBinding=4]="TextureBinding",s[s.StorageBinding=8]="StorageBinding",s[s.RenderAttachment=16]="RenderAttachment"})(De||(De={}));var Pe;(function(s){s.E1d="1d",s.E2d="2d",s.E2dArray="2d-array",s.Cube="cube",s.CubeArray="cube-array",s.E3d="3d"})(Pe||(Pe={}));var Fe;(function(s){s.All="all",s.StencilOnly="stencil-only",s.DepthOnly="depth-only"})(Fe||(Fe={}));var Le;(function(s){s.R8Unorm="r8unorm",s.R8Snorm="r8snorm",s.R8Uint="r8uint",s.R8Sint="r8sint",s.R16Uint="r16uint",s.R16Sint="r16sint",s.R16Float="r16float",s.RG8Unorm="rg8unorm",s.RG8Snorm="rg8snorm",s.RG8Uint="rg8uint",s.RG8Sint="rg8sint",s.R16Unorm="r16unorm",s.R16Snorm="r16snorm",s.RG16Unorm="rg16unorm",s.RG16Snorm="rg16snorm",s.RGBA16Unorm="rgba16unorm",s.RGBA16Snorm="rgba16snorm",s.R32Uint="r32uint",s.R32Sint="r32sint",s.R32Float="r32float",s.RG16Uint="rg16uint",s.RG16Sint="rg16sint",s.RG16Float="rg16float",s.RGBA8Unorm="rgba8unorm",s.RGBA8UnormSRGB="rgba8unorm-srgb",s.RGBA8Snorm="rgba8snorm",s.RGBA8Uint="rgba8uint",s.RGBA8Sint="rgba8sint",s.BGRA8Unorm="bgra8unorm",s.BGRA8UnormSRGB="bgra8unorm-srgb",s.RGB9E5UFloat="rgb9e5ufloat",s.RGB10A2UINT="rgb10a2uint",s.RGB10A2Unorm="rgb10a2unorm",s.RG11B10UFloat="rg11b10ufloat",s.RG32Uint="rg32uint",s.RG32Sint="rg32sint",s.RG32Float="rg32float",s.RGBA16Uint="rgba16uint",s.RGBA16Sint="rgba16sint",s.RGBA16Float="rgba16float",s.RGBA32Uint="rgba32uint",s.RGBA32Sint="rgba32sint",s.RGBA32Float="rgba32float",s.Stencil8="stencil8",s.Depth16Unorm="depth16unorm",s.Depth24Plus="depth24plus",s.Depth24PlusStencil8="depth24plus-stencil8",s.Depth32Float="depth32float",s.BC1RGBAUnorm="bc1-rgba-unorm",s.BC1RGBAUnormSRGB="bc1-rgba-unorm-srgb",s.BC2RGBAUnorm="bc2-rgba-unorm",s.BC2RGBAUnormSRGB="bc2-rgba-unorm-srgb",s.BC3RGBAUnorm="bc3-rgba-unorm",s.BC3RGBAUnormSRGB="bc3-rgba-unorm-srgb",s.BC4RUnorm="bc4-r-unorm",s.BC4RSnorm="bc4-r-snorm",s.BC5RGUnorm="bc5-rg-unorm",s.BC5RGSnorm="bc5-rg-snorm",s.BC6HRGBUFloat="bc6h-rgb-ufloat",s.BC6HRGBFloat="bc6h-rgb-float",s.BC7RGBAUnorm="bc7-rgba-unorm",s.BC7RGBAUnormSRGB="bc7-rgba-unorm-srgb",s.ETC2RGB8Unorm="etc2-rgb8unorm",s.ETC2RGB8UnormSRGB="etc2-rgb8unorm-srgb",s.ETC2RGB8A1Unorm="etc2-rgb8a1unorm",s.ETC2RGB8A1UnormSRGB="etc2-rgb8a1unorm-srgb",s.ETC2RGBA8Unorm="etc2-rgba8unorm",s.ETC2RGBA8UnormSRGB="etc2-rgba8unorm-srgb",s.EACR11Unorm="eac-r11unorm",s.EACR11Snorm="eac-r11snorm",s.EACRG11Unorm="eac-rg11unorm",s.EACRG11Snorm="eac-rg11snorm",s.ASTC4x4Unorm="astc-4x4-unorm",s.ASTC4x4UnormSRGB="astc-4x4-unorm-srgb",s.ASTC5x4Unorm="astc-5x4-unorm",s.ASTC5x4UnormSRGB="astc-5x4-unorm-srgb",s.ASTC5x5Unorm="astc-5x5-unorm",s.ASTC5x5UnormSRGB="astc-5x5-unorm-srgb",s.ASTC6x5Unorm="astc-6x5-unorm",s.ASTC6x5UnormSRGB="astc-6x5-unorm-srgb",s.ASTC6x6Unorm="astc-6x6-unorm",s.ASTC6x6UnormSRGB="astc-6x6-unorm-srgb",s.ASTC8x5Unorm="astc-8x5-unorm",s.ASTC8x5UnormSRGB="astc-8x5-unorm-srgb",s.ASTC8x6Unorm="astc-8x6-unorm",s.ASTC8x6UnormSRGB="astc-8x6-unorm-srgb",s.ASTC8x8Unorm="astc-8x8-unorm",s.ASTC8x8UnormSRGB="astc-8x8-unorm-srgb",s.ASTC10x5Unorm="astc-10x5-unorm",s.ASTC10x5UnormSRGB="astc-10x5-unorm-srgb",s.ASTC10x6Unorm="astc-10x6-unorm",s.ASTC10x6UnormSRGB="astc-10x6-unorm-srgb",s.ASTC10x8Unorm="astc-10x8-unorm",s.ASTC10x8UnormSRGB="astc-10x8-unorm-srgb",s.ASTC10x10Unorm="astc-10x10-unorm",s.ASTC10x10UnormSRGB="astc-10x10-unorm-srgb",s.ASTC12x10Unorm="astc-12x10-unorm",s.ASTC12x10UnormSRGB="astc-12x10-unorm-srgb",s.ASTC12x12Unorm="astc-12x12-unorm",s.ASTC12x12UnormSRGB="astc-12x12-unorm-srgb",s.Depth32FloatStencil8="depth32float-stencil8"})(Le||(Le={}));var Me;(function(s){s.ClampToEdge="clamp-to-edge",s.Repeat="repeat",s.MirrorRepeat="mirror-repeat"})(Me||(Me={}));var Ee;(function(s){s.Nearest="nearest",s.Linear="linear"})(Ee||(Ee={}));var Ge;(function(s){s.Nearest="nearest",s.Linear="linear"})(Ge||(Ge={}));var Oe;(function(s){s.Never="never",s.Less="less",s.Equal="equal",s.LessEqual="less-equal",s.Greater="greater",s.NotEqual="not-equal",s.GreaterEqual="greater-equal",s.Always="always"})(Oe||(Oe={}));var Ue;(function(s){s[s.Vertex=1]="Vertex",s[s.Fragment=2]="Fragment",s[s.Compute=4]="Compute"})(Ue||(Ue={}));var Ne;(function(s){s.Uniform="uniform",s.Storage="storage",s.ReadOnlyStorage="read-only-storage"})(Ne||(Ne={}));var $e;(function(s){s.Filtering="filtering",s.NonFiltering="non-filtering",s.Comparison="comparison"})($e||($e={}));var Ve;(function(s){s.Float="float",s.UnfilterableFloat="unfilterable-float",s.Depth="depth",s.Sint="sint",s.Uint="uint"})(Ve||(Ve={}));var We;(function(s){s.WriteOnly="write-only",s.ReadOnly="read-only",s.ReadWrite="read-write"})(We||(We={}));var ke;(function(s){s.Error="error",s.Warning="warning",s.Info="info"})(ke||(ke={}));var qe;(function(s){s.Validation="validation",s.Internal="internal"})(qe||(qe={}));var Qe;(function(s){s.Auto="auto"})(Qe||(Qe={}));var He;(function(s){s.PointList="point-list",s.LineList="line-list",s.LineStrip="line-strip",s.TriangleList="triangle-list",s.TriangleStrip="triangle-strip"})(He||(He={}));var Ye;(function(s){s.CCW="ccw",s.CW="cw"})(Ye||(Ye={}));var ze;(function(s){s.None="none",s.Front="front",s.Back="back"})(ze||(ze={}));var je;(function(s){s[s.Red=1]="Red",s[s.Green=2]="Green",s[s.Blue=4]="Blue",s[s.Alpha=8]="Alpha",s[s.All=15]="All"})(je||(je={}));var Ke;(function(s){s.Zero="zero",s.One="one",s.Src="src",s.OneMinusSrc="one-minus-src",s.SrcAlpha="src-alpha",s.OneMinusSrcAlpha="one-minus-src-alpha",s.Dst="dst",s.OneMinusDst="one-minus-dst",s.DstAlpha="dst-alpha",s.OneMinusDstAlpha="one-minus-dst-alpha",s.SrcAlphaSaturated="src-alpha-saturated",s.Constant="constant",s.OneMinusConstant="one-minus-constant",s.Src1="src1",s.OneMinusSrc1="one-minus-src1",s.Src1Alpha="src1-alpha",s.OneMinusSrc1Alpha="one-minus-src1-alpha"})(Ke||(Ke={}));var Xe;(function(s){s.Add="add",s.Subtract="subtract",s.ReverseSubtract="reverse-subtract",s.Min="min",s.Max="max"})(Xe||(Xe={}));var Je;(function(s){s.Keep="keep",s.Zero="zero",s.Replace="replace",s.Invert="invert",s.IncrementClamp="increment-clamp",s.DecrementClamp="decrement-clamp",s.IncrementWrap="increment-wrap",s.DecrementWrap="decrement-wrap"})(Je||(Je={}));var Ze;(function(s){s.Uint16="uint16",s.Uint32="uint32"})(Ze||(Ze={}));var et;(function(s){s.Uint8x2="uint8x2",s.Uint8x4="uint8x4",s.Sint8x2="sint8x2",s.Sint8x4="sint8x4",s.Unorm8x2="unorm8x2",s.Unorm8x4="unorm8x4",s.Snorm8x2="snorm8x2",s.Snorm8x4="snorm8x4",s.Uint16x2="uint16x2",s.Uint16x4="uint16x4",s.Sint16x2="sint16x2",s.Sint16x4="sint16x4",s.Unorm16x2="unorm16x2",s.Unorm16x4="unorm16x4",s.Snorm16x2="snorm16x2",s.Snorm16x4="snorm16x4",s.Float16x2="float16x2",s.Float16x4="float16x4",s.Float32="float32",s.Float32x2="float32x2",s.Float32x3="float32x3",s.Float32x4="float32x4",s.Uint32="uint32",s.Uint32x2="uint32x2",s.Uint32x3="uint32x3",s.Uint32x4="uint32x4",s.Sint32="sint32",s.Sint32x2="sint32x2",s.Sint32x3="sint32x3",s.Sint32x4="sint32x4",s.UNORM10x10x10x2="unorm10-10-10-2"})(et||(et={}));var tt;(function(s){s.Vertex="vertex",s.Instance="instance"})(tt||(tt={}));var rt;(function(s){s.Beginning="beginning",s.End="end"})(rt||(rt={}));var st;(function(s){s.Beginning="beginning",s.End="end"})(st||(st={}));var nt;(function(s){s.Load="load",s.Clear="clear"})(nt||(nt={}));var it;(function(s){s.Store="store",s.Discard="discard"})(it||(it={}));var at;(function(s){s.Occlusion="occlusion",s.Timestamp="timestamp"})(at||(at={}));var ot;(function(s){s.Opaque="opaque",s.Premultiplied="premultiplied"})(ot||(ot={}));var ut;(function(s){s.Standard="standard",s.Extended="extended"})(ut||(ut={}));var ct;(function(s){s.Unknown="unknown",s.Destroyed="destroyed"})(ct||(ct={}));var lt;(function(s){s.Validation="validation",s.OutOfMemory="out-of-memory",s.Internal="internal"})(lt||(lt={}));class L{constructor(){this.shaderLanguage=0}_addUniformToLeftOverUBO(e,t,r){let n=0;[e,t,n]=this._getArraySize(e,t,r);for(let i=0;i<this._webgpuProcessingContext.leftOverUniforms.length;i++)if(this._webgpuProcessingContext.leftOverUniforms[i].name===e)return;this._webgpuProcessingContext.leftOverUniforms.push({name:e,type:t,length:n})}_buildLeftOverUBO(){if(!this._webgpuProcessingContext.leftOverUniforms.length)return"";const e=L.LeftOvertUBOName;let t=this._webgpuProcessingContext.availableBuffers[e];return t||(t={binding:this._webgpuProcessingContext.getNextFreeUBOBinding()},this._webgpuProcessingContext.availableBuffers[e]=t,this._addBufferBindingDescription(e,t,"uniform",!0),this._addBufferBindingDescription(e,t,"uniform",!1)),this._generateLeftOverUBOCode(e,t)}_collectBindingNames(){for(let e=0;e<this._webgpuProcessingContext.bindGroupLayoutEntries.length;e++){const t=this._webgpuProcessingContext.bindGroupLayoutEntries[e];if(t===void 0){this._webgpuProcessingContext.bindGroupLayoutEntries[e]=[];continue}for(let r=0;r<t.length;r++){const n=this._webgpuProcessingContext.bindGroupLayoutEntries[e][r],i=this._webgpuProcessingContext.bindGroupLayoutEntryInfo[e][n.binding].name,a=this._webgpuProcessingContext.bindGroupLayoutEntryInfo[e][n.binding].nameInArrayOfTexture;n&&(n.texture||n.externalTexture||n.storageTexture?this._webgpuProcessingContext.textureNames.push(a):n.sampler?this._webgpuProcessingContext.samplerNames.push(i):n.buffer&&this._webgpuProcessingContext.bufferNames.push(i))}}}_preCreateBindGroupEntries(){const e=this._webgpuProcessingContext.bindGroupEntries;for(let t=0;t<this._webgpuProcessingContext.bindGroupLayoutEntries.length;t++){const r=this._webgpuProcessingContext.bindGroupLayoutEntries[t],n=[];for(let i=0;i<r.length;i++){const a=this._webgpuProcessingContext.bindGroupLayoutEntries[t][i];a.sampler||a.texture||a.storageTexture||a.externalTexture?n.push({binding:a.binding,resource:void 0}):a.buffer&&n.push({binding:a.binding,resource:{buffer:void 0,offset:0,size:0}})}e[t]=n}}_addTextureBindingDescription(e,t,r,n,i,a){let{groupIndex:o,bindingIndex:u}=t.textures[r];if(this._webgpuProcessingContext.bindGroupLayoutEntries[o]||(this._webgpuProcessingContext.bindGroupLayoutEntries[o]=[],this._webgpuProcessingContext.bindGroupLayoutEntryInfo[o]=[]),!this._webgpuProcessingContext.bindGroupLayoutEntryInfo[o][u]){let h;n===null?h=this._webgpuProcessingContext.bindGroupLayoutEntries[o].push({binding:u,visibility:0,externalTexture:{}}):i?h=this._webgpuProcessingContext.bindGroupLayoutEntries[o].push({binding:u,visibility:0,storageTexture:{access:"write-only",format:i,viewDimension:n}}):h=this._webgpuProcessingContext.bindGroupLayoutEntries[o].push({binding:u,visibility:0,texture:{sampleType:t.sampleType,viewDimension:n,multisampled:!1}});const c=t.isTextureArray?e+r:e;this._webgpuProcessingContext.bindGroupLayoutEntryInfo[o][u]={name:e,index:h-1,nameInArrayOfTexture:c}}u=this._webgpuProcessingContext.bindGroupLayoutEntryInfo[o][u].index,a?this._webgpuProcessingContext.bindGroupLayoutEntries[o][u].visibility|=1:this._webgpuProcessingContext.bindGroupLayoutEntries[o][u].visibility|=2}_addSamplerBindingDescription(e,t,r){let{groupIndex:n,bindingIndex:i}=t.binding;if(this._webgpuProcessingContext.bindGroupLayoutEntries[n]||(this._webgpuProcessingContext.bindGroupLayoutEntries[n]=[],this._webgpuProcessingContext.bindGroupLayoutEntryInfo[n]=[]),!this._webgpuProcessingContext.bindGroupLayoutEntryInfo[n][i]){const a=this._webgpuProcessingContext.bindGroupLayoutEntries[n].push({binding:i,visibility:0,sampler:{type:t.type}});this._webgpuProcessingContext.bindGroupLayoutEntryInfo[n][i]={name:e,index:a-1}}i=this._webgpuProcessingContext.bindGroupLayoutEntryInfo[n][i].index,r?this._webgpuProcessingContext.bindGroupLayoutEntries[n][i].visibility|=1:this._webgpuProcessingContext.bindGroupLayoutEntries[n][i].visibility|=2}_addBufferBindingDescription(e,t,r,n){let{groupIndex:i,bindingIndex:a}=t.binding;if(this._webgpuProcessingContext.bindGroupLayoutEntries[i]||(this._webgpuProcessingContext.bindGroupLayoutEntries[i]=[],this._webgpuProcessingContext.bindGroupLayoutEntryInfo[i]=[]),!this._webgpuProcessingContext.bindGroupLayoutEntryInfo[i][a]){const o=this._webgpuProcessingContext.bindGroupLayoutEntries[i].push({binding:a,visibility:0,buffer:{type:r}});this._webgpuProcessingContext.bindGroupLayoutEntryInfo[i][a]={name:e,index:o-1}}a=this._webgpuProcessingContext.bindGroupLayoutEntryInfo[i][a].index,n?this._webgpuProcessingContext.bindGroupLayoutEntries[i][a].visibility|=1:this._webgpuProcessingContext.bindGroupLayoutEntries[i][a].visibility|=2}}L.LeftOvertUBOName="LeftOver";L.InternalsUBOName="Internals";L.UniformSizes={bool:1,int:1,float:1,vec2:2,ivec2:2,uvec2:2,vec3:3,ivec3:3,uvec3:3,vec4:4,ivec4:4,uvec4:4,mat2:4,mat3:12,mat4:16,i32:1,u32:1,f32:1,mat2x2:4,mat3x3:12,mat4x4:16,mat2x2f:4,mat3x3f:12,mat4x4f:16,vec2i:2,vec3i:3,vec4i:4,vec2u:2,vec3u:3,vec4u:4,vec2f:2,vec3f:3,vec4f:4,vec2h:1,vec3h:2,vec4h:2};L._SamplerFunctionByWebGLSamplerType={sampler2D:"sampler2D",sampler2DArray:"sampler2DArray",sampler2DShadow:"sampler2DShadow",sampler2DArrayShadow:"sampler2DArrayShadow",samplerCube:"samplerCube",sampler3D:"sampler3D"};L._TextureTypeByWebGLSamplerType={sampler2D:"texture2D",sampler2DArray:"texture2DArray",sampler2DShadow:"texture2D",sampler2DArrayShadow:"texture2DArray",samplerCube:"textureCube",samplerCubeArray:"textureCubeArray",sampler3D:"texture3D"};L._GpuTextureViewDimensionByWebGPUTextureType={textureCube:"cube",textureCubeArray:"cube-array",texture2D:"2d",texture2DArray:"2d-array",texture3D:"3d"};L._SamplerTypeByWebGLSamplerType={sampler2DShadow:"samplerShadow",sampler2DArrayShadow:"samplerShadow"};L._IsComparisonSamplerByWebGPUSamplerType={samplerShadow:!0,samplerArrayShadow:!0,sampler:!1};class Kt{get isAsync(){return!1}get isReady(){return!!this.stages}constructor(e,t){this.bindGroupLayouts={},this._name="unnamed",this.shaderProcessingContext=e,this._leftOverUniformsByName={},this.engine=t,this.vertexBufferKindToType={}}_handlesSpectorRebuildCallback(){}_fillEffectInformation(e,t,r,n,i,a,o,u){const h=this.engine;h._doNotHandleContextLost&&(e._fragmentSourceCode="",e._vertexSourceCode="");const c=this.shaderProcessingContext.availableTextures;let l;for(l=0;l<i.length;l++){const f=i[l],_=c[i[l]];_==null||_==null?(i.splice(l,1),l--):a[f]=l}for(const f of h.getAttributes(this,o))u.push(f);this.buildUniformLayout();const d=[],g=[];for(l=0;l<o.length;l++){const f=u[l];f>=0&&(d.push(o[l]),g.push(f))}this.shaderProcessingContext.attributeNamesFromEffect=d,this.shaderProcessingContext.attributeLocationsFromEffect=g}buildUniformLayout(){if(this.shaderProcessingContext.leftOverUniforms.length){this.uniformBuffer?.dispose(),this.uniformBuffer=new ee(this.engine,void 0,void 0,"leftOver-"+this._name);for(const e of this.shaderProcessingContext.leftOverUniforms){const t=e.type.replace(/^(.*?)(<.*>)?$/,"$1"),r=L.UniformSizes[t];this.uniformBuffer.addUniform(e.name,r,e.length),this._leftOverUniformsByName[e.name]=e.type}this.uniformBuffer.create()}}setEngine(e){this.engine=e}dispose(){this.uniformBuffer&&this.uniformBuffer.dispose()}setInt(e,t){!this.uniformBuffer||!this._leftOverUniformsByName[e]||this.uniformBuffer.updateInt(e,t)}setInt2(e,t,r){!this.uniformBuffer||!this._leftOverUniformsByName[e]||this.uniformBuffer.updateInt2(e,t,r)}setInt3(e,t,r,n){!this.uniformBuffer||!this._leftOverUniformsByName[e]||this.uniformBuffer.updateInt3(e,t,r,n)}setInt4(e,t,r,n,i){!this.uniformBuffer||!this._leftOverUniformsByName[e]||this.uniformBuffer.updateInt4(e,t,r,n,i)}setIntArray(e,t){!this.uniformBuffer||!this._leftOverUniformsByName[e]||this.uniformBuffer.updateIntArray(e,t)}setIntArray2(e,t){this.setIntArray(e,t)}setIntArray3(e,t){this.setIntArray(e,t)}setIntArray4(e,t){this.setIntArray(e,t)}setUInt(e,t){!this.uniformBuffer||!this._leftOverUniformsByName[e]||this.uniformBuffer.updateUInt(e,t)}setUInt2(e,t,r){!this.uniformBuffer||!this._leftOverUniformsByName[e]||this.uniformBuffer.updateUInt2(e,t,r)}setUInt3(e,t,r,n){!this.uniformBuffer||!this._leftOverUniformsByName[e]||this.uniformBuffer.updateUInt3(e,t,r,n)}setUInt4(e,t,r,n,i){!this.uniformBuffer||!this._leftOverUniformsByName[e]||this.uniformBuffer.updateUInt4(e,t,r,n,i)}setUIntArray(e,t){!this.uniformBuffer||!this._leftOverUniformsByName[e]||this.uniformBuffer.updateUIntArray(e,t)}setUIntArray2(e,t){this.setUIntArray(e,t)}setUIntArray3(e,t){this.setUIntArray(e,t)}setUIntArray4(e,t){this.setUIntArray(e,t)}setArray(e,t){!this.uniformBuffer||!this._leftOverUniformsByName[e]||this.uniformBuffer.updateArray(e,t)}setArray2(e,t){this.setArray(e,t)}setArray3(e,t){this.setArray(e,t)}setArray4(e,t){this.setArray(e,t)}setMatrices(e,t){!this.uniformBuffer||!this._leftOverUniformsByName[e]||this.uniformBuffer.updateMatrices(e,t)}setMatrix(e,t){!this.uniformBuffer||!this._leftOverUniformsByName[e]||this.uniformBuffer.updateMatrix(e,t)}setMatrix3x3(e,t){!this.uniformBuffer||!this._leftOverUniformsByName[e]||this.uniformBuffer.updateMatrix3x3(e,t)}setMatrix2x2(e,t){!this.uniformBuffer||!this._leftOverUniformsByName[e]||this.uniformBuffer.updateMatrix2x2(e,t)}setFloat(e,t){!this.uniformBuffer||!this._leftOverUniformsByName[e]||this.uniformBuffer.updateFloat(e,t)}setVector2(e,t){this.setFloat2(e,t.x,t.y)}setFloat2(e,t,r){!this.uniformBuffer||!this._leftOverUniformsByName[e]||this.uniformBuffer.updateFloat2(e,t,r)}setVector3(e,t){this.setFloat3(e,t.x,t.y,t.z)}setFloat3(e,t,r,n){!this.uniformBuffer||!this._leftOverUniformsByName[e]||this.uniformBuffer.updateFloat3(e,t,r,n)}setVector4(e,t){this.setFloat4(e,t.x,t.y,t.z,t.w)}setQuaternion(e,t){this.setFloat4(e,t.x,t.y,t.z,t.w)}setFloat4(e,t,r,n,i){!this.uniformBuffer||!this._leftOverUniformsByName[e]||this.uniformBuffer.updateFloat4(e,t,r,n,i)}setColor3(e,t){this.setFloat3(e,t.r,t.g,t.b)}setColor4(e,t,r){this.setFloat4(e,t.r,t.g,t.b,r)}setDirectColor4(e,t){this.setFloat4(e,t.r,t.g,t.b,t.a)}_getVertexShaderCode(){return this.sources?.vertex}_getFragmentShaderCode(){return this.sources?.fragment}}const Xt=4,Jt=65536,ht={mat2:2,mat3:3,mat4:4,mat2x2:2,mat3x3:3,mat4x4:4};class O{static get KnownUBOs(){return O._SimplifiedKnownBindings?O._SimplifiedKnownUBOs:O._KnownUBOs}constructor(e,t=!1){this.vertexBufferKindToNumberOfComponents={},this.shaderLanguage=e,this._attributeNextLocation=0,this._varyingNextLocation=0,this.freeGroupIndex=0,this.freeBindingIndex=0,this.availableVaryings={},this.availableAttributes={},this.availableBuffers={},this.availableTextures={},this.availableSamplers={},this.orderedAttributes=[],this.bindGroupLayoutEntries=[],this.bindGroupLayoutEntryInfo=[],this.bindGroupEntries=[],this.bufferNames=[],this.textureNames=[],this.samplerNames=[],this.leftOverUniforms=[],t||this._findStartingGroupBinding()}_findStartingGroupBinding(){const e=O.KnownUBOs,t=[];for(const r in e){const n=e[r].binding;n.groupIndex!==-1&&(t[n.groupIndex]===void 0?t[n.groupIndex]=n.bindingIndex:t[n.groupIndex]=Math.max(t[n.groupIndex],n.bindingIndex))}this.freeGroupIndex=t.length-1,this.freeGroupIndex===0?(this.freeGroupIndex++,this.freeBindingIndex=0):this.freeBindingIndex=t[t.length-1]+1}getAttributeNextLocation(e,t=0){const r=this._attributeNextLocation;return this._attributeNextLocation+=(ht[e]??1)*(t||1),r}getVaryingNextLocation(e,t=0){const r=this._varyingNextLocation;return this._varyingNextLocation+=(ht[e]??1)*(t||1),r}getNextFreeUBOBinding(){return this._getNextFreeBinding(1)}_getNextFreeBinding(e){if(this.freeBindingIndex>Jt-e&&(this.freeGroupIndex++,this.freeBindingIndex=0),this.freeGroupIndex===Xt)throw"Too many textures or UBOs have been declared and it is not supported in WebGPU.";const t={groupIndex:this.freeGroupIndex,bindingIndex:this.freeBindingIndex};return this.freeBindingIndex+=e,t}}O._SimplifiedKnownBindings=!0;O._SimplifiedKnownUBOs={Scene:{binding:{groupIndex:0,bindingIndex:0}},Light0:{binding:{groupIndex:-1,bindingIndex:-1}},Light1:{binding:{groupIndex:-1,bindingIndex:-1}},Light2:{binding:{groupIndex:-1,bindingIndex:-1}},Light3:{binding:{groupIndex:-1,bindingIndex:-1}},Light4:{binding:{groupIndex:-1,bindingIndex:-1}},Light5:{binding:{groupIndex:-1,bindingIndex:-1}},Light6:{binding:{groupIndex:-1,bindingIndex:-1}},Light7:{binding:{groupIndex:-1,bindingIndex:-1}},Light8:{binding:{groupIndex:-1,bindingIndex:-1}},Light9:{binding:{groupIndex:-1,bindingIndex:-1}},Light10:{binding:{groupIndex:-1,bindingIndex:-1}},Light11:{binding:{groupIndex:-1,bindingIndex:-1}},Light12:{binding:{groupIndex:-1,bindingIndex:-1}},Light13:{binding:{groupIndex:-1,bindingIndex:-1}},Light14:{binding:{groupIndex:-1,bindingIndex:-1}},Light15:{binding:{groupIndex:-1,bindingIndex:-1}},Light16:{binding:{groupIndex:-1,bindingIndex:-1}},Light17:{binding:{groupIndex:-1,bindingIndex:-1}},Light18:{binding:{groupIndex:-1,bindingIndex:-1}},Light19:{binding:{groupIndex:-1,bindingIndex:-1}},Light20:{binding:{groupIndex:-1,bindingIndex:-1}},Light21:{binding:{groupIndex:-1,bindingIndex:-1}},Light22:{binding:{groupIndex:-1,bindingIndex:-1}},Light23:{binding:{groupIndex:-1,bindingIndex:-1}},Light24:{binding:{groupIndex:-1,bindingIndex:-1}},Light25:{binding:{groupIndex:-1,bindingIndex:-1}},Light26:{binding:{groupIndex:-1,bindingIndex:-1}},Light27:{binding:{groupIndex:-1,bindingIndex:-1}},Light28:{binding:{groupIndex:-1,bindingIndex:-1}},Light29:{binding:{groupIndex:-1,bindingIndex:-1}},Light30:{binding:{groupIndex:-1,bindingIndex:-1}},Light31:{binding:{groupIndex:-1,bindingIndex:-1}},Material:{binding:{groupIndex:-1,bindingIndex:-1}},Mesh:{binding:{groupIndex:-1,bindingIndex:-1}},Internals:{binding:{groupIndex:-1,bindingIndex:-1}}};O._KnownUBOs={Scene:{binding:{groupIndex:0,bindingIndex:0}},Light0:{binding:{groupIndex:1,bindingIndex:0}},Light1:{binding:{groupIndex:1,bindingIndex:1}},Light2:{binding:{groupIndex:1,bindingIndex:2}},Light3:{binding:{groupIndex:1,bindingIndex:3}},Light4:{binding:{groupIndex:1,bindingIndex:4}},Light5:{binding:{groupIndex:1,bindingIndex:5}},Light6:{binding:{groupIndex:1,bindingIndex:6}},Light7:{binding:{groupIndex:1,bindingIndex:7}},Light8:{binding:{groupIndex:1,bindingIndex:8}},Light9:{binding:{groupIndex:1,bindingIndex:9}},Light10:{binding:{groupIndex:1,bindingIndex:10}},Light11:{binding:{groupIndex:1,bindingIndex:11}},Light12:{binding:{groupIndex:1,bindingIndex:12}},Light13:{binding:{groupIndex:1,bindingIndex:13}},Light14:{binding:{groupIndex:1,bindingIndex:14}},Light15:{binding:{groupIndex:1,bindingIndex:15}},Light16:{binding:{groupIndex:1,bindingIndex:16}},Light17:{binding:{groupIndex:1,bindingIndex:17}},Light18:{binding:{groupIndex:1,bindingIndex:18}},Light19:{binding:{groupIndex:1,bindingIndex:19}},Light20:{binding:{groupIndex:1,bindingIndex:20}},Light21:{binding:{groupIndex:1,bindingIndex:21}},Light22:{binding:{groupIndex:1,bindingIndex:22}},Light23:{binding:{groupIndex:1,bindingIndex:23}},Light24:{binding:{groupIndex:1,bindingIndex:24}},Light25:{binding:{groupIndex:1,bindingIndex:25}},Light26:{binding:{groupIndex:1,bindingIndex:26}},Light27:{binding:{groupIndex:1,bindingIndex:27}},Light28:{binding:{groupIndex:1,bindingIndex:28}},Light29:{binding:{groupIndex:1,bindingIndex:29}},Light30:{binding:{groupIndex:1,bindingIndex:30}},Light31:{binding:{groupIndex:1,bindingIndex:31}},Material:{binding:{groupIndex:2,bindingIndex:0}},Mesh:{binding:{groupIndex:2,bindingIndex:1}},Internals:{binding:{groupIndex:2,bindingIndex:2}}};function J(s,e,t,r){let n=r,i=0,a="";for(;n<t.length;){const o=t.charAt(n);if(a)o===a?a==='"'||a==="'"?t.charAt(n-1)!=="\\"&&(a=""):a="":a==="*/"&&o==="*"&&n+1<t.length&&(t.charAt(n+1)==="/"&&(a=""),a===""&&n++);else switch(o){case s:i++;break;case e:i--;break;case'"':case"'":case"`":a=o;break;case"/":if(n+1<t.length){const u=t.charAt(n+1);u==="/"?a=`
`:u==="*"&&(a="*/")}break}if(n++,i===0)break}return i===0?n-1:-1}function dt(s,e){for(;e<s.length;){const t=s[e];if(t!==" "&&t!==`
`&&t!=="\r"&&t!=="	"&&t!==`
`&&t!==" ")break;e++}return e}function ue(s){const e=s.charCodeAt(0);return e>=48&&e<=57||e>=65&&e<=90||e>=97&&e<=122||e==95}function le(s){let e=0,t="",r=!1;const n=[];for(;e<s.length;){const i=s.charAt(e);if(t)i===t?t==='"'||t==="'"?(s.charAt(e-1)!=="\\"&&(t=""),n.push(i)):(t="",r=!1):t==="*/"&&i==="*"&&e+1<s.length?(s.charAt(e+1)==="/"&&(t=""),t===""&&(r=!1,e++)):r||n.push(i);else{switch(i){case'"':case"'":case"`":t=i;break;case"/":if(e+1<s.length){const a=s.charAt(e+1);a==="/"?(t=`
`,r=!0):a==="*"&&(t="*/",r=!0)}break}r||n.push(i)}e++}return n.join("")}function Zt(s,e,t,r){for(;e>=0&&s.charAt(e)!==t&&s.charAt(e)!==r;)e--;return e}function er(s){return s.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function he(s,e,t,r){let n=s.indexOf(e);if(n<0)return s;if(t){for(;n++<s.length&&s.charAt(n)!="{";);if(n<s.length){const i=s.substring(0,n+1),a=s.substring(n+1);s=i+t+a}}if(r){const i=s.lastIndexOf("}");s=s.substring(0,i),s+=r+`
}`}return s}class tr extends L{constructor(){super(...arguments),this._missingVaryings=[],this._textureArrayProcessing=[],this._vertexIsGLES3=!1,this._fragmentIsGLES3=!1,this.shaderLanguage=0,this.parseGLES3=!0}_getArraySize(e,t,r){let n=0;const i=e.indexOf("["),a=e.indexOf("]");if(i>0&&a>0){const o=e.substring(i+1,a);n=+o,isNaN(n)&&(n=+r[o.trim()]),e=e.substring(0,i)}return[e,t,n]}initializeShaders(e){this._webgpuProcessingContext=e,this._missingVaryings.length=0,this._textureArrayProcessing.length=0,this.attributeKeywordName=void 0,this.varyingVertexKeywordName=void 0,this.varyingFragmentKeywordName=void 0}preProcessShaderCode(e,t){const r=`// Internals UBO
uniform ${L.InternalsUBOName} {
float yFactor_;
float textureOutputHeight_;
};
`,n=e.indexOf("// Internals UBO")!==-1;return t?(this._fragmentIsGLES3=e.indexOf("#version 3")!==-1,this._fragmentIsGLES3&&(this.varyingFragmentKeywordName="in"),n?e:r+`##INJECTCODE##
`+e):(this._vertexIsGLES3=e.indexOf("#version 3")!==-1,this._vertexIsGLES3&&(this.attributeKeywordName="in",this.varyingVertexKeywordName="out"),n?e:r+e)}varyingCheck(e,t){const r=/(flat\s)?\s*\bout\b/,n=/(flat\s)?\s*\bin\b/,i=/(flat\s)?\s*\bvarying\b/;return(t&&this._fragmentIsGLES3?n:!t&&this._vertexIsGLES3?r:i).test(e)}varyingProcessor(e,t,r){this._preProcessors=r;const n=/\s*(flat)?\s*out\s+(?:(?:highp)?|(?:lowp)?)\s*(\S+)\s+(\S+)\s*;/gm,i=/\s*(flat)?\s*in\s+(?:(?:highp)?|(?:lowp)?)\s*(\S+)\s+(\S+)\s*;/gm,a=/\s*(flat)?\s*varying\s+(?:(?:highp)?|(?:lowp)?)\s*(\S+)\s+(\S+)\s*;/gm,u=(t&&this._fragmentIsGLES3?i:!t&&this._vertexIsGLES3?n:a).exec(e);if(u!==null){const h=u[1]??"",c=u[2],l=u[3];let d;t?(d=this._webgpuProcessingContext.availableVaryings[l],this._missingVaryings[d]="",d===void 0&&y.Warn(`Invalid fragment shader: The varying named "${l}" is not declared in the vertex shader! This declaration will be ignored.`)):(d=this._webgpuProcessingContext.getVaryingNextLocation(c,this._getArraySize(l,c,r)[2]),this._webgpuProcessingContext.availableVaryings[l]=d,this._missingVaryings[d]=`layout(location = ${d}) ${h} in ${c} ${l};`),e=e.replace(u[0],d===void 0?"":`layout(location = ${d}) ${h} ${t?"in":"out"} ${c} ${l};`)}return e}attributeProcessor(e,t){this._preProcessors=t;const r=/\s*in\s+(\S+)\s+(\S+)\s*;/gm,n=/\s*attribute\s+(\S+)\s+(\S+)\s*;/gm,a=(this._vertexIsGLES3?r:n).exec(e);if(a!==null){const o=a[1],u=a[2],h=this._webgpuProcessingContext.getAttributeNextLocation(o,this._getArraySize(u,o,t)[2]);this._webgpuProcessingContext.availableAttributes[u]=h,this._webgpuProcessingContext.orderedAttributes[h]=u;const c=this._webgpuProcessingContext.vertexBufferKindToNumberOfComponents[u];if(c!==void 0){const l=c<0?c===-1?"int":"ivec"+-c:c===1?"uint":"uvec"+c,d=`_int_${u}_`;e=e.replace(a[0],`layout(location = ${h}) in ${l} ${d}; ${o} ${u} = ${o}(${d});`)}else e=e.replace(a[0],`layout(location = ${h}) in ${o} ${u};`)}return e}uniformProcessor(e,t,r){this._preProcessors=r;const i=/\s*uniform\s+(?:(?:highp)?|(?:lowp)?)\s*(\S+)\s+(\S+)\s*;/gm.exec(e);if(i!==null){let a=i[1],o=i[2];if(a.indexOf("sampler")===0||a.indexOf("sampler")===1){let u=0;[o,a,u]=this._getArraySize(o,a,r);let h=this._webgpuProcessingContext.availableTextures[o];if(!h){h={autoBindSampler:!0,isTextureArray:u>0,isStorageTexture:!1,textures:[],sampleType:"float"};for(let P=0;P<(u||1);++P)h.textures.push(this._webgpuProcessingContext.getNextFreeUBOBinding())}const c=L._SamplerTypeByWebGLSamplerType[a]??"sampler",l=!!L._IsComparisonSamplerByWebGPUSamplerType[c],d=l?"comparison":"filtering",g=o+"Sampler";let f=this._webgpuProcessingContext.availableSamplers[g];f||(f={binding:this._webgpuProcessingContext.getNextFreeUBOBinding(),type:d});const _=a.charAt(0)==="u"?"u":a.charAt(0)==="i"?"i":"";_&&(a=a.substring(1));const p=l?"depth":_==="u"?"uint":_==="i"?"sint":"float";h.sampleType=p;const m=u>0,x=f.binding.groupIndex,b=f.binding.bindingIndex,I=L._SamplerFunctionByWebGLSamplerType[a],w=L._TextureTypeByWebGLSamplerType[a],v=L._GpuTextureViewDimensionByWebGPUTextureType[w];if(!m)u=1,e=`layout(set = ${x}, binding = ${b}) uniform ${c} ${g};
                        layout(set = ${h.textures[0].groupIndex}, binding = ${h.textures[0].bindingIndex}) uniform ${_}${w} ${o}Texture;
                        #define ${o} ${_}${I}(${o}Texture, ${g})`;else{const P=[];P.push(`layout(set = ${x}, binding = ${b}) uniform ${_}${c} ${g};`),e=`
`;for(let R=0;R<u;++R){const M=h.textures[R].groupIndex,E=h.textures[R].bindingIndex;P.push(`layout(set = ${M}, binding = ${E}) uniform ${w} ${o}Texture${R};`),e+=`${R>0?`
`:""}#define ${o}${R} ${_}${I}(${o}Texture${R}, ${g})`}e=P.join(`
`)+e,this._textureArrayProcessing.push(o)}this._webgpuProcessingContext.availableTextures[o]=h,this._webgpuProcessingContext.availableSamplers[g]=f,this._addSamplerBindingDescription(g,f,!t);for(let P=0;P<u;++P)this._addTextureBindingDescription(o,h,P,v,null,!t)}else this._addUniformToLeftOverUBO(o,a,r),e=""}return e}uniformBufferProcessor(e,t){const n=/uniform\s+(\w+)/gm.exec(e);if(n!==null){const i=n[1];let a=this._webgpuProcessingContext.availableBuffers[i];if(!a){const o=O.KnownUBOs[i];let u;o&&o.binding.groupIndex!==-1?u=o.binding:u=this._webgpuProcessingContext.getNextFreeUBOBinding(),a={binding:u},this._webgpuProcessingContext.availableBuffers[i]=a}this._addBufferBindingDescription(i,a,"uniform",!t),e=e.replace("uniform",`layout(set = ${a.binding.groupIndex}, binding = ${a.binding.bindingIndex}) uniform`)}return e}postProcessor(e,t,r,n,i){const a=e.search(/#extension.+GL_EXT_draw_buffers.+require/)!==-1,o=/#extension.+(GL_OVR_multiview2|GL_OES_standard_derivatives|GL_EXT_shader_texture_lod|GL_EXT_frag_depth|GL_EXT_draw_buffers).+(enable|require)/g;if(e=e.replace(o,""),e=e.replace(/texture2D\s*\(/g,"texture("),r){const u=e.indexOf("gl_FragCoord")>=0,h=`
                glFragCoord_ = gl_FragCoord;
                if (yFactor_ == 1.) {
                    glFragCoord_.y = textureOutputHeight_ - glFragCoord_.y;
                }
            `,c=u?`vec4 glFragCoord_;
`:"",l=e.search(/layout *\(location *= *0\) *out/g)!==-1;if(e=e.replace(/texture2DLodEXT\s*\(/g,"textureLod("),e=e.replace(/textureCubeLodEXT\s*\(/g,"textureLod("),e=e.replace(/textureCube\s*\(/g,"texture("),e=e.replace(/gl_FragDepthEXT/g,"gl_FragDepth"),e=e.replace(/gl_FragColor/g,"glFragColor"),e=e.replace(/gl_FragData/g,"glFragData"),e=e.replace(/gl_FragCoord/g,"glFragCoord_"),!this._fragmentIsGLES3)e=e.replace(/void\s+?main\s*\(/g,(a||l?"":`layout(location = 0) out vec4 glFragColor;
`)+"void main(");else{const d=/^\s*out\s+\S+\s+\S+\s*;/gm.exec(e);d!==null&&(e=e.substring(0,d.index)+"layout(location = 0) "+e.substring(d.index))}e=e.replace(/dFdy/g,"(-yFactor_)*dFdy"),e=e.replace("##INJECTCODE##",c),u&&(e=he(e,"void main",h))}else if(e=e.replace(/gl_InstanceID/g,"gl_InstanceIndex"),e=e.replace(/gl_VertexID/g,"gl_VertexIndex"),t.indexOf("#define MULTIVIEW")!==-1)return`#extension GL_OVR_multiview2 : require
layout (num_views = 2) in;
`+e;if(!r){const u=e.lastIndexOf("}");e=e.substring(0,u),e+=`gl_Position.y *= yFactor_;
`,e+="}"}return e}_applyTextureArrayProcessing(e,t){const r=new RegExp(t+"\\s*\\[(.+)?\\]","gm");let n=r.exec(e);for(;n!==null;){const i=n[1];let a=+i;this._preProcessors&&isNaN(a)&&(a=+this._preProcessors[i.trim()]),e=e.replace(n[0],t+a),n=r.exec(e)}return e}_generateLeftOverUBOCode(e,t){let r=`layout(set = ${t.binding.groupIndex}, binding = ${t.binding.bindingIndex}) uniform ${e} {
    `;for(const n of this._webgpuProcessingContext.leftOverUniforms)n.length>0?r+=`    ${n.type} ${n.name}[${n.length}];
`:r+=`    ${n.type} ${n.name};
`;return r+=`};

`,r}finalizeShaders(e,t){for(let n=0;n<this._textureArrayProcessing.length;++n){const i=this._textureArrayProcessing[n];e=this._applyTextureArrayProcessing(e,i),t=this._applyTextureArrayProcessing(t,i)}for(let n=0;n<this._missingVaryings.length;++n){const i=this._missingVaryings[n];i&&i.length>0&&(t=i+`
`+t)}const r=this._buildLeftOverUBO();return e=r+e,t=r+t,this._collectBindingNames(),this._preCreateBindGroupEntries(),this._preProcessors=null,this._webgpuProcessingContext.vertexBufferKindToNumberOfComponents={},{vertexCode:e,fragmentCode:t}}}const pt="fragmentOutputs.fragDepth",rr="uniforms",sr="internals",nr={texture_1d:"1d",texture_2d:"2d",texture_2d_array:"2d-array",texture_3d:"3d",texture_cube:"cube",texture_cube_array:"cube-array",texture_multisampled_2d:"2d",texture_depth_2d:"2d",texture_depth_2d_array:"2d-array",texture_depth_cube:"cube",texture_depth_cube_array:"cube-array",texture_depth_multisampled_2d:"2d",texture_storage_1d:"1d",texture_storage_2d:"2d",texture_storage_2d_array:"2d-array",texture_storage_3d:"3d",texture_external:null};class ir extends L{constructor(){super(...arguments),this.shaderLanguage=1,this.uniformRegexp=/uniform\s+(\w+)\s*:\s*(.+)\s*;/,this.textureRegexp=/var\s+(\w+)\s*:\s*((array<\s*)?(texture_\w+)\s*(<\s*(.+)\s*>)?\s*(,\s*\w+\s*>\s*)?);/,this.noPrecision=!0,this.pureMode=!1}preProcessor(e,t,r,n,i){for(const a in r){if(a==="__VERSION__")continue;const o=r[a];(!isNaN(parseInt(o))||!isNaN(parseFloat(o)))&&(e=`const ${a} = ${o};
`+e)}return e}_getArraySize(e,t,r){let n=0;const i=t.lastIndexOf(">");if(t.indexOf("array")>=0&&i>0){let a=i;for(;a>0&&t.charAt(a)!==" "&&t.charAt(a)!==",";)a--;const o=t.substring(a+1,i);for(n=+o,isNaN(n)&&(n=+r[o.trim()]);a>0&&(t.charAt(a)===" "||t.charAt(a)===",");)a--;t=t.substring(t.indexOf("<")+1,a+1)}return[e,t,n]}initializeShaders(e){this._webgpuProcessingContext=e,this._attributesInputWGSL=[],this._attributesWGSL=[],this._attributesConversionCodeWGSL=[],this._hasNonFloatAttribute=!1,this._varyingsWGSL=[],this._varyingNamesWGSL=[],this._stridedUniformArrays=[]}preProcessShaderCode(e){const t=this.pureMode?"":`struct ${L.InternalsUBOName} {
  yFactor_: f32,
  textureOutputHeight_: f32,
};
var<uniform> ${sr} : ${L.InternalsUBOName};
`;return e.indexOf(t)!==-1?e:t+le(e)}varyingCheck(e){return/(flat|linear|perspective)?\s*(center|centroid|sample)?\s*\bvarying\b/.test(e)}varyingProcessor(e,t,r){const i=/\s*(flat|linear|perspective)?\s*(center|centroid|sample)?\s*varying\s+(?:(?:highp)?|(?:lowp)?)\s*(\S+)\s*:\s*(.+)\s*;/gm.exec(e);if(i!==null){const a=i[1]??"perspective",o=i[2]??"center",u=i[4],h=i[3],c=a==="flat"?`@interpolate(${a})`:`@interpolate(${a}, ${o})`;let l;t?(l=this._webgpuProcessingContext.availableVaryings[h],l===void 0&&y.Warn(`Invalid fragment shader: The varying named "${h}" is not declared in the vertex shader! This declaration will be ignored.`)):(l=this._webgpuProcessingContext.getVaryingNextLocation(u,this._getArraySize(h,u,r)[2]),this._webgpuProcessingContext.availableVaryings[h]=l,this._varyingsWGSL.push(`  @location(${l}) ${c} ${h} : ${u},`),this._varyingNamesWGSL.push(h)),e=""}return e}attributeProcessor(e,t){const n=/\s*attribute\s+(\S+)\s*:\s*(.+)\s*;/gm.exec(e);if(n!==null){const i=n[2],a=n[1],o=this._webgpuProcessingContext.getAttributeNextLocation(i,this._getArraySize(a,i,t)[2]);this._webgpuProcessingContext.availableAttributes[a]=o,this._webgpuProcessingContext.orderedAttributes[o]=a;const u=this._webgpuProcessingContext.vertexBufferKindToNumberOfComponents[a];if(u!==void 0){const h=u<0?u===-1?"i32":"vec"+-u+"<i32>":u===1?"u32":"vec"+u+"<u32>",c=`_int_${a}_`;this._attributesInputWGSL.push(`@location(${o}) ${c} : ${h},`),this._attributesWGSL.push(`${a} : ${i},`),this._attributesConversionCodeWGSL.push(`vertexInputs.${a} = ${i}(vertexInputs_.${c});`),this._hasNonFloatAttribute=!0}else this._attributesInputWGSL.push(`@location(${o}) ${a} : ${i},`),this._attributesWGSL.push(`${a} : ${i},`),this._attributesConversionCodeWGSL.push(`vertexInputs.${a} = vertexInputs_.${a};`);e=""}return e}uniformProcessor(e,t,r){const n=this.uniformRegexp.exec(e);if(n!==null){const i=n[2],a=n[1];this._addUniformToLeftOverUBO(a,i,r),e=""}return e}textureProcessor(e,t,r){const n=this.textureRegexp.exec(e);if(n!==null){const i=n[1],a=n[2],o=!!n[3],u=n[4],h=u.indexOf("storage")>0,c=n[6],l=h?c.substring(0,c.indexOf(",")).trim():null;let d=o?this._getArraySize(i,a,r)[2]:0,g=this._webgpuProcessingContext.availableTextures[i];if(g)d=g.textures.length;else{g={isTextureArray:d>0,isStorageTexture:h,textures:[],sampleType:"float"},d=d||1;for(let m=0;m<d;++m)g.textures.push(this._webgpuProcessingContext.getNextFreeUBOBinding())}this._webgpuProcessingContext.availableTextures[i]=g;const f=u.indexOf("depth")>0,_=nr[u],p=f?"depth":c==="u32"?"uint":c==="i32"?"sint":"float";if(g.sampleType=p,_===void 0)throw`Can't get the texture dimension corresponding to the texture function "${u}"!`;for(let m=0;m<d;++m){const{groupIndex:x,bindingIndex:b}=g.textures[m];m===0&&(e=`@group(${x}) @binding(${b}) ${e}`),this._addTextureBindingDescription(i,g,m,_,l,!t)}}return e}postProcessor(e){const t=/#define (.+?) (.+?)$/gm;let r;for(;(r=t.exec(e))!==null;)e=e.replace(new RegExp(r[1],"g"),r[2]);return e}finalizeShaders(e,t){const r=t.indexOf("fragmentInputs.position")>=0&&!this.pureMode?`
            if (internals.yFactor_ == 1.) {
                fragmentInputs.position.y = internals.textureOutputHeight_ - fragmentInputs.position.y;
            }
        `:"";e=this._processSamplers(e,!0),t=this._processSamplers(t,!1),e=this._processCustomBuffers(e,!0),t=this._processCustomBuffers(t,!1);const n=this._buildLeftOverUBO();e=n+e,t=n+t,e=e.replace(/#define (\w+)\s+(\d+\.?\d*)/g,"const $1 = $2;"),e=e.replace(/#define /g,"//#define "),e=this._processStridedUniformArrays(e);let i=`struct VertexInputs {
  @builtin(vertex_index) vertexIndex : u32,
  @builtin(instance_index) instanceIndex : u32,
`;this._attributesInputWGSL.length>0&&(i+=this._attributesInputWGSL.join(`
`)),i+=`
};
var<private> vertexInputs`+(this._hasNonFloatAttribute?"_":"")+` : VertexInputs;
`,this._hasNonFloatAttribute&&(i+=`struct VertexInputs_ {
  vertexIndex : u32, instanceIndex : u32,
`,i+=this._attributesWGSL.join(`
`),i+=`
};
var<private> vertexInputs : VertexInputs_;
`);let a=`struct FragmentInputs {
  @builtin(position) position : vec4<f32>,
`;this._varyingsWGSL.length>0&&(a+=this._varyingsWGSL.join(`
`)),a+=`
};
var<private> vertexOutputs : FragmentInputs;
`,e=i+a+e;let o=`
  vertexInputs${this._hasNonFloatAttribute?"_":""} = input;
`;this._hasNonFloatAttribute&&(o+=`vertexInputs.vertexIndex = vertexInputs_.vertexIndex;
vertexInputs.instanceIndex = vertexInputs_.instanceIndex;
`,o+=this._attributesConversionCodeWGSL.join(`
`),o+=`
`);const u=this.pureMode?"  return vertexOutputs;":`  vertexOutputs.position.y = vertexOutputs.position.y * internals.yFactor_;
  return vertexOutputs;`;let h=e.indexOf("#define DISABLE_UNIFORMITY_ANALYSIS")!==-1;e=(h?`diagnostic(off, derivative_uniformity);
`:"")+`diagnostic(off, chromium.unreachable_code);
`+he(e,"fn main",o,u),t=t.replace(/#define (\w+)\s+(\d+\.?\d*)/g,"const $1 = $2;"),t=t.replace(/#define /g,"//#define "),t=this._processStridedUniformArrays(t),this.pureMode||(t=t.replace(/dpdy/g,"(-internals.yFactor_)*dpdy"));let c=`struct FragmentInputs {
  @builtin(position) position : vec4<f32>,
  @builtin(front_facing) frontFacing : bool,
`;this._varyingsWGSL.length>0&&(c+=this._varyingsWGSL.join(`
`)),c+=`
};
var<private> fragmentInputs : FragmentInputs;
`;let l=`struct FragmentOutputs {
`;const d="fragmentOutputs\\.fragData";let g=t.match(new RegExp(d+"0","g")),f=0;if(g){l+=` @location(${f}) fragData0 : vec4<f32>,
`,f++;for(let I=1;I<8;I++)g=t.match(new RegExp(d+I,"g")),g&&(l+=` @location(${f}) fragData${f} : vec4<f32>,
`,f++);t.indexOf("MRT_AND_COLOR")!==-1&&(l+=`  @location(${f}) color : vec4<f32>,
`,f++)}const _=/oitDepthSampler/;g=t.match(_),g&&(l+=` @location(${f++}) depth : vec2<f32>,
`,l+=` @location(${f++}) frontColor : vec4<f32>,
`,l+=` @location(${f++}) backColor : vec4<f32>,
`),f===0&&(l+=`  @location(0) color : vec4<f32>,
`,f++);let p=!1,m=0;for(;!p&&(m=t.indexOf(pt,m),!(m<0));){const I=m;for(p=!0;m>1&&t.charAt(m)!==`
`;){if(t.charAt(m)==="/"&&t.charAt(m-1)==="/"){p=!1;break}m--}m=I+pt.length}p&&(l+=`  @builtin(frag_depth) fragDepth: f32,
`),l+=`};
var<private> fragmentOutputs : FragmentOutputs;
`,t=c+l+t;const x=`  fragmentInputs = input;
  `+r,b="  return fragmentOutputs;";return h=t.indexOf("#define DISABLE_UNIFORMITY_ANALYSIS")!==-1,t=(h?`diagnostic(off, derivative_uniformity);
`:"")+`diagnostic(off, chromium.unreachable_code);
`+he(t,"fn main",x,b),this._collectBindingNames(),this._preCreateBindGroupEntries(),this._webgpuProcessingContext.vertexBufferKindToNumberOfComponents={},{vertexCode:e,fragmentCode:t}}_generateLeftOverUBOCode(e,t){let r="",n=`struct ${e} {
`;for(const i of this._webgpuProcessingContext.leftOverUniforms){const a=i.type.replace(/^(.*?)(<.*>)?$/,"$1"),o=L.UniformSizes[a];if(i.length>0)if(o<=2){const u=`${e}_${this._stridedUniformArrays.length}_strided_arr`;r+=`struct ${u} {
                        @size(16)
                        el: ${a},
                    }`,this._stridedUniformArrays.push(i.name),n+=` @align(16) ${i.name} : array<${u}, ${i.length}>,
`}else n+=` ${i.name} : array<${i.type}, ${i.length}>,
`;else n+=`  ${i.name} : ${i.type},
`}return n+=`};
`,n=`${r}
${n}`,n+=`@group(${t.binding.groupIndex}) @binding(${t.binding.bindingIndex}) var<uniform> ${rr} : ${e};
`,n}_processSamplers(e,t){const r=/var\s+(\w+Sampler)\s*:\s*(sampler|sampler_comparison)\s*;/gm;for(;;){const n=r.exec(e);if(n===null)break;const i=n[1],a=n[2],o=i.length-7,u=i.lastIndexOf("Sampler")===o?i.substring(0,o):null,h=a==="sampler_comparison"?"comparison":"filtering";if(u){const f=this._webgpuProcessingContext.availableTextures[u];f&&(f.autoBindSampler=!0)}let c=this._webgpuProcessingContext.availableSamplers[i];c||(c={binding:this._webgpuProcessingContext.getNextFreeUBOBinding(),type:h},this._webgpuProcessingContext.availableSamplers[i]=c),this._addSamplerBindingDescription(i,c,t);const l=e.substring(0,n.index),d=`@group(${c.binding.groupIndex}) @binding(${c.binding.bindingIndex}) `,g=e.substring(n.index);e=l+d+g,r.lastIndex+=d.length}return e}_processCustomBuffers(e,t){const r=/var<\s*(uniform|storage)\s*(,\s*(read|read_write)\s*)?>\s+(\S+)\s*:\s*(\S+)\s*;/gm;for(;;){const n=r.exec(e);if(n===null)break;const i=n[1],a=n[3];let o=n[4];const u=n[5];let h=this._webgpuProcessingContext.availableBuffers[o];if(!h){const _=i==="uniform"?O.KnownUBOs[u]:null;let p;_?(o=u,p=_.binding,p.groupIndex===-1&&(p=this._webgpuProcessingContext.availableBuffers[o]?.binding,p||(p=this._webgpuProcessingContext.getNextFreeUBOBinding()))):p=this._webgpuProcessingContext.getNextFreeUBOBinding(),h={binding:p},this._webgpuProcessingContext.availableBuffers[o]=h}this._addBufferBindingDescription(o,this._webgpuProcessingContext.availableBuffers[o],a==="read_write"?"storage":i==="storage"?"read-only-storage":"uniform",t);const c=h.binding.groupIndex,l=h.binding.bindingIndex,d=e.substring(0,n.index),g=`@group(${c}) @binding(${l}) `,f=e.substring(n.index);e=d+g+f,r.lastIndex+=g.length}return e}_processStridedUniformArrays(e){for(const t of this._stridedUniformArrays)e=e.replace(new RegExp(`${t}\\s*\\[(.*?)\\]`,"g"),`${t}[$1].el`);return e}}class te{get underlyingResource(){return this._webgpuTexture}getMSAATexture(e){return this._webgpuMSAATexture?.[e]??null}setMSAATexture(e,t){this._webgpuMSAATexture||(this._webgpuMSAATexture=[]),this._webgpuMSAATexture[t]=e}releaseMSAATexture(e){if(this._webgpuMSAATexture)if(e!==void 0)this._engine._textureHelper.releaseTexture(this._webgpuMSAATexture[e]),delete this._webgpuMSAATexture[e];else{for(const t of this._webgpuMSAATexture)this._engine._textureHelper.releaseTexture(t);this._webgpuMSAATexture=null}}constructor(e,t=null){this._engine=e,this._originalFormatIsRGB=!1,this.format="rgba8unorm",this.textureUsages=0,this.textureAdditionalUsages=0,this._webgpuTexture=t,this._webgpuMSAATexture=null,this.view=null,this.viewForWriting=null}set(e){this._webgpuTexture=e}setUsage(e,t,r,n,i,a,o,u){let h="2d",c=1;n?(h=r?"cube-array":"cube",c=6*(u||1)):i?(h="3d",c=1):r&&(h="2d-array",c=u);const l=C.GetDepthFormatOnly(this.format),d=C.HasDepthAndStencilAspects(this.format)?"depth-only":"all";this.createView({label:`TextureView${i?"3D":n?"Cube":"2D"}${r?"_Array"+c:""}_${a}x${o}_${t?"wmips":"womips"}_${this.format}_${h}`,format:l,dimension:h,mipLevelCount:t?xt(Math.max(a,o))+1:1,baseArrayLayer:0,baseMipLevel:0,arrayLayerCount:c,aspect:d})}createView(e,t=!1){if(this.view=this._webgpuTexture.createView(e),t&&e){const r=e.mipLevelCount;e.mipLevelCount=1,this.viewForWriting=this._webgpuTexture.createView(e),e.mipLevelCount=r}}reset(){this._webgpuTexture=null,this._webgpuMSAATexture=null,this.view=null,this.viewForWriting=null}release(){this._webgpuTexture?.destroy(),this.releaseMSAATexture(),this._copyInvertYTempTexture?.destroy(),this.reset()}}const ar=`
    const pos = array<vec2<f32>, 4>( vec2f(-1.0f, 1.0f),  vec2f(1.0f, 1.0f),  vec2f(-1.0f, -1.0f),  vec2f(1.0f, -1.0f));
    const tex = array<vec2<f32>, 4>( vec2f(0.0f, 0.0f),  vec2f(1.0f, 0.0f),  vec2f(0.0f, 1.0f),  vec2f(1.0f, 1.0f));

    varying vTex: vec2f;

    @vertex
    fn main(input : VertexInputs) -> FragmentInputs {
        vertexOutputs.vTex = tex[input.vertexIndex];
        vertexOutputs.position = vec4f(pos[input.vertexIndex], 0.0, 1.0);
    }
    `,or=`
    var imgSampler: sampler;
    var img: texture_2d<f32>;

    varying vTex: vec2f;

    @fragment
    fn main(input: FragmentInputs) -> FragmentOutputs {
        fragmentOutputs.color = textureSample(img, imgSampler, input.vTex);
    }
    `,St=`
    const pos = array<vec2<f32>, 4>( vec2f(-1.0f, 1.0f),  vec2f(1.0f, 1.0f),  vec2f(-1.0f, -1.0f),  vec2f(1.0f, -1.0f));
    const tex = array<vec2<f32>, 4>( vec2f(0.0f, 0.0f),  vec2f(1.0f, 0.0f),  vec2f(0.0f, 1.0f),  vec2f(1.0f, 1.0f));

    var img: texture_2d<f32>;

    #ifdef INVERTY
        varying vTextureSize: vec2f;
    #endif

    @vertex
    fn main(input : VertexInputs) -> FragmentInputs {
        #ifdef INVERTY
            vertexOutputs.vTextureSize = vec2f(textureDimensions(img, 0));
        #endif
        vertexOutputs.position =  vec4f(pos[input.vertexIndex], 0.0, 1.0);
    }
    `,ur=`
    var img: texture_2d<f32>;

    #ifdef INVERTY
        varying vTextureSize: vec2f;
    #endif

    @fragment
    fn main(input: FragmentInputs) -> FragmentOutputs {
    #ifdef INVERTY
        var color: vec4f = textureLoad(img, vec2i(i32(input.position.x), i32(input.vTextureSize.y - input.position.y)), 0);
    #else
        var color: vec4f = textureLoad(img, vec2i(input.position.xy), 0);
    #endif
    #ifdef PREMULTIPLYALPHA
        color = vec4f(color.rgb * color.a, color.a);
    #endif
        fragmentOutputs.color = color;
    }
    `,cr=St,lr=`
    var img: texture_2d<f32>;
    uniform ofstX: f32;
    uniform ofstY: f32;
    uniform width: f32;
    uniform height: f32;

    #ifdef INVERTY
        varying vTextureSize: vec2f;
    #endif

    @fragment
    fn main(input: FragmentInputs) -> FragmentOutputs {
        if (input.position.x < uniforms.ofstX || input.position.x >= uniforms.ofstX + uniforms.width) {
            discard;
        }
        if (input.position.y < uniforms.ofstY || input.position.y >= uniforms.ofstY + uniforms.height) {
            discard;
        }
    #ifdef INVERTY
        var color: vec4f = textureLoad(img, vec2i(i32(input.position.x), i32(uniforms.ofstY + uniforms.height - (input.position.y - uniforms.ofstY))), 0);
    #else
        var color: vec4f = textureLoad(img, vec2i(input.position.xy), 0);
    #endif
    #ifdef PREMULTIPLYALPHA
        color = vec4f(color.rgb * color.a, color.a);
    #endif
        fragmentOutputs.color = color;
    }
    `,hr=`
    const pos = array<vec2<f32>, 4>( vec2f(-1.0f, 1.0f),  vec2f(1.0f, 1.0f),  vec2f(-1.0f, -1.0f),  vec2f(1.0f, -1.0f));

    @vertex
    fn main(input : VertexInputs) -> FragmentInputs {
        vertexOutputs.position =  vec4f(pos[input.vertexIndex], 0.0, 1.0);
    }
    `,dr=`
    uniform color: vec4f;


    @fragment
    fn main(input: FragmentInputs) -> FragmentOutputs {
        fragmentOutputs.color = uniforms.color;
    }
    `,pr=`
    struct VertexOutput {
        @builtin(position) Position : vec4<f32>,
        @location(0) fragUV : vec2<f32>
    }

    @vertex
    fn main(
        @builtin(vertex_index) VertexIndex : u32
    ) -> VertexOutput {
        var pos = array<vec2<f32>, 4>(
            vec2(-1.0,  1.0),
            vec2( 1.0,  1.0),
            vec2(-1.0, -1.0),
            vec2( 1.0, -1.0)
        );
        var tex = array<vec2<f32>, 4>(
            vec2(0.0, 0.0),
            vec2(1.0, 0.0),
            vec2(0.0, 1.0),
            vec2(1.0, 1.0)
        );

        var output: VertexOutput;

        output.Position = vec4<f32>(pos[VertexIndex], 0.0, 1.0);
        output.fragUV = tex[VertexIndex];

        return output;
    }
    `,fr=`
    @group(0) @binding(0) var videoSampler: sampler;
    @group(0) @binding(1) var videoTexture: texture_external;

    @fragment
    fn main(
        @location(0) fragUV: vec2<f32>
    ) -> @location(0) vec4<f32> {
        return textureSampleBaseClampToEdge(videoTexture, videoSampler, fragUV);
    }
    `,gr=`
    @group(0) @binding(0) var videoSampler: sampler;
    @group(0) @binding(1) var videoTexture: texture_external;

    @fragment
    fn main(
        @location(0) fragUV: vec2<f32>
    ) -> @location(0) vec4<f32> {
        return textureSampleBaseClampToEdge(videoTexture, videoSampler, vec2<f32>(fragUV.x, 1.0 - fragUV.y));
    }
    `;var N;(function(s){s[s.MipMap=0]="MipMap",s[s.InvertYPremultiplyAlpha=1]="InvertYPremultiplyAlpha",s[s.Clear=2]="Clear",s[s.InvertYPremultiplyAlphaWithOfst=3]="InvertYPremultiplyAlphaWithOfst"})(N||(N={}));var Y;(function(s){s[s.DontInvertY=0]="DontInvertY",s[s.InvertY=1]="InvertY"})(Y||(Y={}));const ft=[{vertex:ar,fragment:or},{vertex:St,fragment:ur},{vertex:hr,fragment:dr},{vertex:cr,fragment:lr}],V={"":0,r8unorm:1,r8uint:2,r8sint:3,r16uint:4,r16sint:5,r16float:6,rg8unorm:7,rg8uint:8,rg8sint:9,r32uint:10,r32sint:11,r32float:12,rg16uint:13,rg16sint:14,rg16float:15,rgba8unorm:16,"rgba8unorm-srgb":17,rgba8uint:18,rgba8sint:19,bgra8unorm:20,"bgra8unorm-srgb":21,rgb10a2uint:22,rgb10a2unorm:23,rg32uint:24,rg32sint:25,rg32float:26,rgba16uint:27,rgba16sint:28,rgba16float:29,rgba32uint:30,rgba32sint:31,rgba32float:32,stencil8:33,depth16unorm:34,depth24plus:35,"depth24plus-stencil8":36,depth32float:37,"depth32float-stencil8":38,r16unorm:39,rg16unorm:40,rgba16unorm:41,r16snorm:42,rg16snorm:43,rgba16snorm:44};class _r{constructor(e,t,r,n){if(this._pipelines={},this._compiledShaders=[],this._videoPipelines={},this._videoCompiledShaders=[],this._deferredReleaseTextures=[],this._engine=e,this._device=t,this._bufferManager=r,n.indexOf("rg11b10ufloat-renderable")!==-1){const i=Object.keys(V);V.rg11b10ufloat=V[i[i.length-1]]+1}this._mipmapSampler=t.createSampler({minFilter:"linear"}),this._videoSampler=t.createSampler({minFilter:"linear"}),this._ubCopyWithOfst=this._bufferManager.createBuffer(4*4,T.Uniform|T.CopyDst,"UBCopyWithOffset").underlyingResource,this._getPipeline("rgba8unorm"),this._getVideoPipeline("rgba8unorm")}_getPipeline(e,t=N.MipMap,r){const n=t===N.MipMap?1:t===N.InvertYPremultiplyAlpha?((r.invertY?1:0)<<1)+((r.premultiplyAlpha?1:0)<<2):t===N.Clear?8:t===N.InvertYPremultiplyAlphaWithOfst?((r.invertY?1:0)<<4)+((r.premultiplyAlpha?1:0)<<5):0;this._pipelines[e]||(this._pipelines[e]=[]);let i=this._pipelines[e][n];if(!i){let a="";(t===N.InvertYPremultiplyAlpha||t===N.InvertYPremultiplyAlphaWithOfst)&&(r.invertY&&(a+=`#define INVERTY
`),r.premultiplyAlpha&&(a+=`#define PREMULTIPLYALPHA
`));let o=this._compiledShaders[n];if(!o){let h=ft[t].vertex,c=ft[t].fragment;const l={defines:a.split(`
`),indexParameters:null,isFragment:!1,shouldUseHighPrecisionShader:!0,processor:this._engine._getShaderProcessor(1),supportsUniformBuffers:!0,shadersRepository:"",includesShadersStore:{},version:(this._engine.version*100).toString(),platformName:this._engine.shaderPlatformName,processingContext:this._engine._getShaderProcessingContext(1,!0),isNDCHalfZRange:this._engine.isNDCHalfZRange,useReverseDepthBuffer:this._engine.useReverseDepthBuffer};Bt(l),l.processor.pureMode=!0,Ie(h,l,_=>{h=_},this._engine),l.isFragment=!0,Ie(c,l,_=>{c=_},this._engine);const d=vt(h,c,l);l.processor.pureMode=!1;const g=this._device.createShaderModule({label:`BabylonWebGPUDevice${this._engine.uniqueId}_InternalVertexShader_${n}`,code:d.vertexCode}),f=this._device.createShaderModule({label:`BabylonWebGPUDevice${this._engine.uniqueId}_InternalFragmentShader_${n}`,code:d.fragmentCode});o=this._compiledShaders[n]=[g,f]}const u=this._device.createRenderPipeline({label:`BabylonWebGPUDevice${this._engine.uniqueId}_InternalPipeline_${e}_${n}`,layout:"auto",vertex:{module:o[0],entryPoint:"main"},fragment:{module:o[1],entryPoint:"main",targets:[{format:e}]},primitive:{topology:"triangle-strip",stripIndexFormat:"uint16"}});i=this._pipelines[e][n]=[u,u.getBindGroupLayout(0)]}return i}_getVideoPipeline(e,t=Y.DontInvertY){const r=t===Y.InvertY?1:0;this._videoPipelines[e]||(this._videoPipelines[e]=[]);let n=this._videoPipelines[e][r];if(!n){let i=this._videoCompiledShaders[r];if(!i){const o=this._device.createShaderModule({code:pr,label:`BabylonWebGPUDevice${this._engine.uniqueId}_CopyVideoToTexture_VertexShader`}),u=this._device.createShaderModule({code:r===0?fr:gr,label:`BabylonWebGPUDevice${this._engine.uniqueId}_CopyVideoToTexture_FragmentShader_${r===0?"DontInvertY":"InvertY"}`});i=this._videoCompiledShaders[r]=[o,u]}const a=this._device.createRenderPipeline({label:`BabylonWebGPUDevice${this._engine.uniqueId}_InternalVideoPipeline_${e}_${r===0?"DontInvertY":"InvertY"}`,layout:"auto",vertex:{module:i[0],entryPoint:"main"},fragment:{module:i[1],entryPoint:"main",targets:[{format:e}]},primitive:{topology:"triangle-strip",stripIndexFormat:"uint16"}});n=this._videoPipelines[e][r]=[a,a.getBindGroupLayout(0)]}return n}setCommandEncoder(e){this._commandEncoderForCreation=e}copyVideoToTexture(e,t,r,n=!1,i){const a=i===void 0,[o,u]=this._getVideoPipeline(r,n?Y.InvertY:Y.DontInvertY);a&&(i=this._device.createCommandEncoder({})),i.pushDebugGroup?.(`copy video to texture - invertY=${n}`);const h=t._hardwareTexture,c={label:`BabylonWebGPUDevice${this._engine.uniqueId}_copyVideoToTexture_${r}_${n?"InvertY":"DontInvertY"}${t.label?"_"+t.label:""}`,colorAttachments:[{view:h.underlyingResource.createView({format:r,dimension:"2d",mipLevelCount:1,baseArrayLayer:0,baseMipLevel:0,arrayLayerCount:1,aspect:"all"}),loadOp:"load",storeOp:"store"}]},l=i.beginRenderPass(c),d={layout:u,entries:[{binding:0,resource:this._videoSampler},{binding:1,resource:this._device.importExternalTexture({source:e.underlyingResource})}]},g=this._device.createBindGroup(d);l.setPipeline(o),l.setBindGroup(0,g),l.draw(4,1,0,0),l.end(),i.popDebugGroup?.(),a&&(this._device.queue.submit([i.finish()]),i=null)}invertYPreMultiplyAlpha(e,t,r,n,i=!1,a=!1,o=0,u=0,h=1,c=0,l=0,d=0,g=0,f,_){const p=d!==0,m=f===void 0,[x,b]=this._getPipeline(n,p?N.InvertYPremultiplyAlphaWithOfst:N.InvertYPremultiplyAlpha,{invertY:i,premultiplyAlpha:a});o=Math.max(o,0),m&&(f=this._device.createCommandEncoder({})),f.pushDebugGroup?.(`internal process texture - invertY=${i} premultiplyAlpha=${a}`);let I;if(C.IsHardwareTexture(e)?(I=e.underlyingResource,i&&!a&&h===1&&o===0||(e=void 0)):(I=e,e=void 0),!I)return;p&&this._bufferManager.setRawData(this._ubCopyWithOfst,0,new Float32Array([c,l,d,g]),0,4*4);const w=e,v=w?._copyInvertYTempTexture??this.createTexture({width:t,height:r,layers:1},!1,!1,!1,!1,!1,n,1,f,21,void 0,"TempTextureForCopyWithInvertY"),P=w?._copyInvertYRenderPassDescr??{label:`BabylonWebGPUDevice${this._engine.uniqueId}_invertYPreMultiplyAlpha_${n}_${i?"InvertY":"DontInvertY"}_${a?"PremultiplyAlpha":"DontPremultiplyAlpha"}`,colorAttachments:[{view:v.createView({format:n,dimension:"2d",baseMipLevel:0,mipLevelCount:1,arrayLayerCount:1,baseArrayLayer:0}),loadOp:"load",storeOp:"store"}]},R=f.beginRenderPass(P);let M=p?w?._copyInvertYBindGroupWithOfst:w?._copyInvertYBindGroup;if(!M){const E={layout:b,entries:[{binding:0,resource:I.createView({format:n,dimension:"2d",baseMipLevel:u,mipLevelCount:1,arrayLayerCount:h,baseArrayLayer:o})}]};p&&E.entries.push({binding:1,resource:{buffer:this._ubCopyWithOfst}}),M=this._device.createBindGroup(E)}R.setPipeline(x),R.setBindGroup(0,M),R.draw(4,1,0,0),R.end(),f.copyTextureToTexture({texture:v},{texture:I,mipLevel:u,origin:{x:0,y:0,z:o}},{width:d||t,height:g||r,depthOrArrayLayers:1}),w?(w._copyInvertYTempTexture=v,w._copyInvertYRenderPassDescr=P,p?w._copyInvertYBindGroupWithOfst=M:w._copyInvertYBindGroup=M):this._deferredReleaseTextures.push([v,null]),f.popDebugGroup?.(),m&&(this._device.queue.submit([f.finish()]),f=null)}createTexture(e,t=!1,r=!1,n=!1,i=!1,a=!1,o="rgba8unorm",u=1,h,c=-1,l=0,d){u=C.GetSample(u);const g=e.layers||1,f={width:e.width,height:e.height,depthOrArrayLayers:g},_=V[o]?16:0,p=C.IsCompressedFormat(o),m=t?C.ComputeNumMipmapLevels(e.width,e.height):1,x=c>=0?c:7;l|=t&&!p?1|_:0,!p&&!a&&(l|=_|2);const b=this._device.createTexture({label:`BabylonWebGPUDevice${this._engine.uniqueId}_Texture${a?"3D":"2D"}_${d?d+"_":""}${f.width}x${f.height}x${f.depthOrArrayLayers}_${t?"wmips":"womips"}_${o}_samples${u}`,size:f,dimension:a?"3d":"2d",format:o,usage:x|l,sampleCount:u,mipLevelCount:m});return C.IsImageBitmap(e)&&(this.updateTexture(e,b,e.width,e.height,g,o,0,0,n,i,0,0),t&&r&&this.generateMipmaps(b,o,m,0,a,h)),b}createCubeTexture(e,t=!1,r=!1,n=!1,i=!1,a="rgba8unorm",o=1,u,h=-1,c=0,l){o=C.GetSample(o);const d=C.IsImageBitmapArray(e)?e[0].width:e.width,g=C.IsImageBitmapArray(e)?e[0].height:e.height,f=V[a]?16:0,_=C.IsCompressedFormat(a),p=t?C.ComputeNumMipmapLevels(d,g):1,m=h>=0?h:7;c|=t&&!_?1|f:0,_||(c|=f|2);const x=this._device.createTexture({label:`BabylonWebGPUDevice${this._engine.uniqueId}_TextureCube_${l?l+"_":""}${d}x${g}x6_${t?"wmips":"womips"}_${a}_samples${o}`,size:{width:d,height:g,depthOrArrayLayers:6},dimension:"2d",format:a,usage:m|c,sampleCount:o,mipLevelCount:p});return C.IsImageBitmapArray(e)&&(this.updateCubeTextures(e,x,d,g,a,n,i,0,0),t&&r&&this.generateCubeMipmaps(x,a,p,u)),x}generateCubeMipmaps(e,t,r,n){const i=n===void 0;i&&(n=this._device.createCommandEncoder({})),n.pushDebugGroup?.(`create cube mipmaps - ${r} levels`);for(let a=0;a<6;++a)this.generateMipmaps(e,t,r,a,!1,n);n.popDebugGroup?.(),i&&(this._device.queue.submit([n.finish()]),n=null)}generateMipmaps(e,t,r,n=0,i=!1,a){const o=a===void 0,[u,h]=this._getPipeline(t);n=Math.max(n,0),o&&(a=this._device.createCommandEncoder({})),a.pushDebugGroup?.(`create mipmaps for face #${n} - ${r} levels`);let c;if(C.IsHardwareTexture(e)?(c=e.underlyingResource,e._mipmapGenRenderPassDescr=e._mipmapGenRenderPassDescr||[],e._mipmapGenBindGroup=e._mipmapGenBindGroup||[]):(c=e,e=void 0),!c)return;const l=e;for(let d=1;d<r;++d){const g=l?._mipmapGenRenderPassDescr[n]?.[d-1]??{label:`BabylonWebGPUDevice${this._engine.uniqueId}_generateMipmaps_${t}_faceIndex${n}_level${d}`,colorAttachments:[{view:c.createView({format:t,dimension:i?"3d":"2d",baseMipLevel:d,mipLevelCount:1,arrayLayerCount:1,baseArrayLayer:n}),loadOp:"load",storeOp:"store"}]};l&&(l._mipmapGenRenderPassDescr[n]=l._mipmapGenRenderPassDescr[n]||[],l._mipmapGenRenderPassDescr[n][d-1]=g);const f=a.beginRenderPass(g),_=l?._mipmapGenBindGroup[n]?.[d-1]??this._device.createBindGroup({layout:h,entries:[{binding:0,resource:c.createView({format:t,dimension:i?"3d":"2d",baseMipLevel:d-1,mipLevelCount:1,arrayLayerCount:1,baseArrayLayer:n})},{binding:1,resource:this._mipmapSampler}]});l&&(l._mipmapGenBindGroup[n]=l._mipmapGenBindGroup[n]||[],l._mipmapGenBindGroup[n][d-1]=_),f.setPipeline(u),f.setBindGroup(0,_),f.draw(4,1,0,0),f.end()}a.popDebugGroup?.(),o&&(this._device.queue.submit([a.finish()]),a=null)}createGPUTextureForInternalTexture(e,t,r,n,i,a){e._hardwareTexture||(e._hardwareTexture=new te(this._engine)),t===void 0&&(t=e.width),r===void 0&&(r=e.height),n===void 0&&(n=e.depth);const o=e._hardwareTexture,u=((i??0)&1)!==0;o.format=C.GetWebGPUTextureFormat(e.type,e.format,e._useSRGBBuffer),o.textureUsages=e._source===5||e.source===6?21:e._source===12?20:-1,o.textureAdditionalUsages=u?8:0;const h=e.generateMipMaps,c=n||1;let l;if(e._maxLodLevel!==null?l=e._maxLodLevel:l=h?C.ComputeNumMipmapLevels(t,r):1,e.isCube){const d=this.createCubeTexture({width:t,height:r},e.generateMipMaps,e.generateMipMaps,e.invertY,!1,o.format,1,this._commandEncoderForCreation,o.textureUsages,o.textureAdditionalUsages,e.label);o.set(d);const g=e.is3D?1:c,f=C.GetDepthFormatOnly(o.format),_=C.HasDepthAndStencilAspects(o.format)?"depth-only":"all",p=e.is2DArray?"cube-array":"cube";o.createView({label:`BabylonWebGPUDevice${this._engine.uniqueId}_TextureViewCube${e.is2DArray?"_Array"+g:""}_${t}x${r}_${h?"wmips":"womips"}_${f}_${p}_${_}_${e.label??"noname"}`,format:f,dimension:p,mipLevelCount:l,baseArrayLayer:0,baseMipLevel:0,arrayLayerCount:6,aspect:_},u)}else{const d=this.createTexture({width:t,height:r,layers:c},e.generateMipMaps,e.generateMipMaps,e.invertY,!1,e.is3D,o.format,1,this._commandEncoderForCreation,o.textureUsages,o.textureAdditionalUsages,e.label);o.set(d);const g=e.is3D?1:c,f=C.GetDepthFormatOnly(o.format),_=C.HasDepthAndStencilAspects(o.format)?"depth-only":"all",p=e.is2DArray?"2d-array":e.is3D?"3d":"2d";o.createView({label:`BabylonWebGPUDevice${this._engine.uniqueId}_TextureView${e.is3D?"3D":"2D"}${e.is2DArray?"_Array"+g:""}_${t}x${r}${e.is3D?"x"+c:""}_${h?"wmips":"womips"}_${f}_${p}_${_}_${e.label??"noname"}`,format:f,dimension:p,mipLevelCount:l,baseArrayLayer:0,baseMipLevel:0,arrayLayerCount:g,aspect:_},u)}return e.width=e.baseWidth=t,e.height=e.baseHeight=r,e.depth=e.baseDepth=n,a||this.createMSAATexture(e,e.samples),o}createMSAATexture(e,t,r=!0,n=0){const i=e._hardwareTexture;if(r&&i?.releaseMSAATexture(),!i||(t??1)<=1)return;const a=e.width,o=e.height,u=this.createTexture({width:a,height:o,layers:1},!1,!1,!1,!1,!1,i.format,t,this._commandEncoderForCreation,16,0,e.label?"MSAA_"+e.label:"MSAA");i.setMSAATexture(u,n)}updateCubeTextures(e,t,r,n,i,a=!1,o=!1,u=0,h=0){const c=[0,3,1,4,2,5];for(let l=0;l<c.length;++l){const d=e[c[l]];this.updateTexture(d,t,r,n,1,i,l,0,a,o,u,h)}}updateTexture(e,t,r,n,i,a,o=0,u=0,h=!1,c=!1,l=0,d=0,g){const f=C.IsInternalTexture(t)?t._hardwareTexture.underlyingResource:t,_=C.GetBlockInformationFromFormat(a),p=C.IsInternalTexture(t)?t._hardwareTexture:t,m={texture:f,origin:{x:l,y:d,z:Math.max(o,0)},mipLevel:u,premultipliedAlpha:c},x={width:Math.ceil(r/_.width)*_.width,height:Math.ceil(n/_.height)*_.height,depthOrArrayLayers:i||1};if(e.byteLength!==void 0){e=e;const b=Math.ceil(r/_.width)*_.length;if(Math.ceil(b/256)*256===b){const w=this._device.createCommandEncoder({}),v=this._bufferManager.createRawBuffer(e.byteLength,T.MapWrite|T.CopySrc,!0,"TempBufferForUpdateTexture"+(f?"_"+f.label:"")),P=v.getMappedRange();new Uint8Array(P).set(e),v.unmap(),w.copyBufferToTexture({buffer:v,offset:0,bytesPerRow:b,rowsPerImage:n},m,x),this._device.queue.submit([w.finish()]),this._bufferManager.releaseBuffer(v)}else this._device.queue.writeTexture(m,e,{offset:0,bytesPerRow:b,rowsPerImage:n},x);if(h||c)if(C.IsInternalTexture(t)){const w=l===0&&d===0&&r===t.width&&n===t.height;this.invertYPreMultiplyAlpha(p,t.width,t.height,a,h,c,o,u,i||1,l,d,w?0:r,w?0:n,void 0,g)}else throw"updateTexture: Can't process the texture data because a GPUTexture was provided instead of an InternalTexture!"}else e=e,this._device.queue.copyExternalImageToTexture({source:e,flipY:h},m,x)}readPixels(e,t,r,n,i,a,o=0,u=0,h=null,c=!1){const l=C.GetBlockInformationFromFormat(a),d=Math.ceil(n/l.width)*l.length,g=Math.ceil(d/256)*256,f=g*i,_=this._bufferManager.createRawBuffer(f,T.MapRead|T.CopyDst,void 0,"TempBufferForReadPixels"+(e.label?"_"+e.label:"")),p=this._device.createCommandEncoder({});return p.copyTextureToBuffer({texture:e,mipLevel:u,origin:{x:t,y:r,z:Math.max(o,0)}},{buffer:_,offset:0,bytesPerRow:g},{width:n,height:i,depthOrArrayLayers:1}),this._device.queue.submit([p.finish()]),this._bufferManager.readDataFromBuffer(_,f,n,i,d,g,C.GetTextureTypeFromFormat(a),0,h,!0,c)}releaseTexture(e){if(C.IsInternalTexture(e)){const t=e._hardwareTexture,r=e._irradianceTexture;this._deferredReleaseTextures.push([t,r])}else this._deferredReleaseTextures.push([e,null])}destroyDeferredTextures(){for(let e=0;e<this._deferredReleaseTextures.length;++e){const[t,r]=this._deferredReleaseTextures[e];t&&(C.IsHardwareTexture(t)?t.release():t.destroy()),r?.dispose()}this._deferredReleaseTextures.length=0}}class mr extends Rt{set buffer(e){this._buffer=e}constructor(e,t=0){super(),this.engineId=-1,this.capacity=t,e&&(this._buffer=e)}get underlyingResource(){return this._buffer}}class se{static _IsGPUBuffer(e){return e.underlyingResource===void 0}static _FlagsToString(e,t=""){let r=t;for(let n=0;n<=9;++n)e&1<<n&&(r&&(r+="_"),r+=T[1<<n]);return r}constructor(e,t){this._deferredReleaseBuffers=[],this._engine=e,this._device=t}createRawBuffer(e,t,r=!1,n){const i=e.byteLength!==void 0?e.byteLength+3&-4:e+3&-4,a={label:"BabylonWebGPUDevice"+this._engine.uniqueId+"_"+se._FlagsToString(t,n??"Buffer")+"_size"+i,mappedAtCreation:r,size:i,usage:t};return this._device.createBuffer(a)}createBuffer(e,t,r){const n=e.byteLength!==void 0,i=new mr,a="DataBufferUniqueId="+i.uniqueId;return i.buffer=this.createRawBuffer(e,t,void 0,r?a+"-"+r:a),i.references=1,i.capacity=n?e.byteLength:e,i.engineId=this._engine.uniqueId,n&&this.setSubData(i,0,e),i}setRawData(e,t,r,n,i){n+=r.byteOffset,this._device.queue.writeBuffer(e,t,r.buffer,n,i)}setSubData(e,t,r,n=0,i=0){const a=e.underlyingResource;i=i||r.byteLength-n;const o=t&3;n-=o,t-=o;const u=i;if(i=i+o+3&-4,r.buffer.byteLength-r.byteOffset<i){const c=new Uint8Array(i);c.set(new Uint8Array(r.buffer,r.byteOffset+n,u)),r=c,n=0}this.setRawData(a,t,r,n,i)}_getHalfFloatAsFloatRGBAArrayBuffer(e,t,r){r||(r=new Float32Array(e));const n=new Uint16Array(t);for(;e--;)r[e]=At(n[e]);return r}readDataFromBuffer(e,t,r,n,i,a,o=0,u=0,h=null,c=!0,l=!1){const d=o===1?2:o===2?1:0,g=this._engine.uniqueId;return new Promise((f,_)=>{e.mapAsync(1,u,t).then(()=>{const p=e.getMappedRange(u,t);let m=h;if(l)m===null?m=Ce(o,t,!0,p):m=Ce(o,m.buffer,void 0,p);else if(m===null)switch(d){case 0:m=new Uint8Array(t),m.set(new Uint8Array(p));break;case 1:m=this._getHalfFloatAsFloatRGBAArrayBuffer(t/2,p);break;case 2:m=new Float32Array(t/4),m.set(new Float32Array(p));break}else switch(d){case 0:m=new Uint8Array(m.buffer),m.set(new Uint8Array(p));break;case 1:m=this._getHalfFloatAsFloatRGBAArrayBuffer(t/2,p,h);break;case 2:m=new Float32Array(m.buffer),m.set(new Float32Array(p));break}if(i!==a){d===1&&!l&&(i*=2,a*=2);const x=new Uint8Array(m.buffer);let b=i,I=0;for(let w=1;w<n;++w){I=w*a;for(let v=0;v<i;++v)x[b++]=x[I++]}d!==0&&!l?m=new Float32Array(x.buffer,0,b/4):m=new Uint8Array(x.buffer,0,b)}e.unmap(),c&&this.releaseBuffer(e),f(m)},p=>{this._engine.isDisposed||this._engine.uniqueId!==g?f(new Uint8Array):_(p)})})}releaseBuffer(e){return se._IsGPUBuffer(e)?(this._deferredReleaseBuffers.push(e),!0):(e.references--,e.references===0?(this._deferredReleaseBuffers.push(e.underlyingResource),!0):!1)}destroyDeferredBuffers(){for(let e=0;e<this._deferredReleaseBuffers.length;++e)this._deferredReleaseBuffers[e].destroy();this._deferredReleaseBuffers.length=0}}const br=[0,0,3,7,0,2,6,2,4,1,5,3,1],xr=[0,64,32,96,16,80,48,112,8],yr=[0,128,128,0,0,0,0,128,0,0,0,0,128];class Q{constructor(e){this._samplers={},this._device=e,this.disabled=!1}static GetSamplerHashCode(e){const t=e._cachedAnisotropicFilteringLevel?e._cachedAnisotropicFilteringLevel:1;return br[e.samplingMode]+xr[(e._comparisonFunction||514)-512+1]+yr[e.samplingMode]+((e._cachedWrapU??1)<<8)+((e._cachedWrapV??1)<<10)+((e._cachedWrapR??1)<<12)+((e.useMipMaps?1:0)<<14)+(t<<15)}static _GetSamplerFilterDescriptor(e,t){let r,n,i,a,o;const u=e.useMipMaps;switch(e.samplingMode){case 11:r="linear",n="linear",i="nearest",u||(a=o=0);break;case 3:case 3:r="linear",n="linear",u?i="linear":(i="nearest",a=o=0);break;case 8:r="nearest",n="nearest",u?i="linear":(i="nearest",a=o=0);break;case 4:r="nearest",n="nearest",i="nearest",u||(a=o=0);break;case 5:r="nearest",n="linear",i="nearest",u||(a=o=0);break;case 6:r="nearest",n="linear",u?i="linear":(i="nearest",a=o=0);break;case 7:r="nearest",n="linear",i="nearest",a=o=0;break;case 1:case 1:r="nearest",n="nearest",i="nearest",a=o=0;break;case 9:r="linear",n="nearest",i="nearest",u||(a=o=0);break;case 10:r="linear",n="nearest",u?i="linear":(i="nearest",a=o=0);break;case 2:case 2:r="linear",n="linear",t>1?i="linear":(i="nearest",a=o=0);break;case 12:r="linear",n="nearest",i="nearest",a=o=0;break;default:r="nearest",n="nearest",i="nearest",a=o=0;break}return t>1&&(a!==0||o!==0)?{magFilter:"linear",minFilter:"linear",mipmapFilter:"linear",anisotropyEnabled:!0}:{magFilter:r,minFilter:n,mipmapFilter:i,lodMinClamp:a,lodMaxClamp:o}}static _GetWrappingMode(e){switch(e){case 1:return"repeat";case 0:return"clamp-to-edge";case 2:return"mirror-repeat"}return"repeat"}static _GetSamplerWrappingDescriptor(e){return{addressModeU:this._GetWrappingMode(e._cachedWrapU),addressModeV:this._GetWrappingMode(e._cachedWrapV),addressModeW:this._GetWrappingMode(e._cachedWrapR)}}static _GetSamplerDescriptor(e,t){let r=(e.useMipMaps||e.samplingMode===2)&&e._cachedAnisotropicFilteringLevel?e._cachedAnisotropicFilteringLevel:1;e.samplingMode!==11&&e.samplingMode!==3&&e.samplingMode!==2&&(r=1);const n=this._GetSamplerFilterDescriptor(e,r);return{label:t,...n,...this._GetSamplerWrappingDescriptor(e),compare:e._comparisonFunction?Q.GetCompareFunction(e._comparisonFunction):void 0,maxAnisotropy:n.anisotropyEnabled?r:1}}static GetCompareFunction(e){switch(e){case 519:return"always";case 514:return"equal";case 516:return"greater";case 518:return"greater-equal";case 513:return"less";case 515:return"less-equal";case 512:return"never";case 517:return"not-equal";default:return"less"}}getSampler(e,t=!1,r=0,n){if(this.disabled)return this._device.createSampler(Q._GetSamplerDescriptor(e,n));t?r=0:r===0&&(r=Q.GetSamplerHashCode(e));let i=t?void 0:this._samplers[r];return i||(i=this._device.createSampler(Q._GetSamplerDescriptor(e,n)),t||(this._samplers[r]=i)),i}}const Sr={[S.PositionKind]:!0,[S.NormalKind]:!0,[S.TangentKind]:!0,[S.UVKind]:!0,[S.UV2Kind]:!0,[S.UV3Kind]:!0,[S.UV4Kind]:!0,[S.UV5Kind]:!0,[S.UV6Kind]:!0,[S.ColorKind]:!0,[S.ColorInstanceKind]:!0,[S.MatricesIndicesKind]:!0,[S.MatricesWeightsKind]:!0,[S.MatricesIndicesExtraKind]:!0,[S.MatricesWeightsExtraKind]:!0};function wr(s){switch(s){case S.BYTE:case S.SHORT:case S.INT:case S.FLOAT:return!0;case S.UNSIGNED_BYTE:case S.UNSIGNED_SHORT:case S.UNSIGNED_INT:return!1;default:throw new Error(`Invalid type '${s}'`)}}function Ir(s,e){const t=e.getEngine(),r=e._pipelineContext;if(!r?.vertexBufferKindToType)return;let n=null;for(const i in s){const a=s[i];if(!a||!Sr[i])continue;const o=a.normalized?S.FLOAT:a.type,u=r.vertexBufferKindToType[i];(o!==S.FLOAT&&u===void 0||u!==void 0&&u!==o)&&(n||(n=t._getShaderProcessingContext(e.shaderLanguage,!1)),r.vertexBufferKindToType[i]=o,o!==S.FLOAT&&(n.vertexBufferKindToNumberOfComponents[i]=S.DeduceStride(i),wr(o)&&(n.vertexBufferKindToNumberOfComponents[i]*=-1)))}if(n){const i=t._caps.parallelShaderCompile;t._caps.parallelShaderCompile=void 0,e._processShaderCodeAsync(null,t._features._checkNonFloatVertexBuffersDontRecreatePipelineContext,n),t._caps.parallelShaderCompile=i}}var F;(function(s){s[s.StencilReadMask=0]="StencilReadMask",s[s.StencilWriteMask=1]="StencilWriteMask",s[s.DepthBias=2]="DepthBias",s[s.DepthBiasSlopeScale=3]="DepthBiasSlopeScale",s[s.DepthStencilState=4]="DepthStencilState",s[s.MRTAttachments1=5]="MRTAttachments1",s[s.MRTAttachments2=6]="MRTAttachments2",s[s.RasterizationState=7]="RasterizationState",s[s.ColorStates=8]="ColorStates",s[s.ShaderStage=9]="ShaderStage",s[s.TextureStage=10]="TextureStage",s[s.VertexState=11]="VertexState",s[s.NumStates=12]="NumStates"})(F||(F={}));const Z={0:1,1:2,768:3,769:4,770:5,771:6,772:7,773:8,774:9,775:10,776:11,32769:12,32770:13,32771:12,32772:13},H={0:0,7680:1,7681:2,7682:3,7683:4,5386:5,34055:6,34056:7};class D{constructor(e,t){this.mrtTextureCount=0,this._device=e,this._useTextureStage=!0,this._states=new Array(30),this._statesLength=0,this._stateDirtyLowestIndex=0,this._emptyVertexBuffer=t,this._mrtFormats=[],this._parameter={token:void 0,pipeline:null},this.disabled=!1,this.vertexBuffers=[],this._kMaxVertexBufferStride=e.limits.maxVertexBufferArrayStride||2048,this.reset()}reset(){this._isDirty=!0,this.vertexBuffers.length=0,this.setAlphaToCoverage(!1),this.resetDepthCullingState(),this.setClampDepth(!1),this.setDepthBias(0),this._webgpuColorFormat=["bgra8unorm"],this.setColorFormat("bgra8unorm"),this.setMRT([]),this.setAlphaBlendEnabled(!1),this.setAlphaBlendFactors([null,null,null,null],[null,null]),this.setWriteMask(15),this.setDepthStencilFormat("depth24plus-stencil8"),this.setStencilEnabled(!1),this.resetStencilState(),this.setBuffers(null,null,null),this._setTextureState(0)}get colorFormats(){return this._mrtAttachments1>0?this._mrtFormats:this._webgpuColorFormat}getRenderPipeline(e,t,r,n=0){if(r=C.GetSample(r),this.disabled){const a=D._GetTopology(e);return this._setVertexState(t),this._setTextureState(n),this._parameter.pipeline=this._createRenderPipeline(t,a,r),D.NumCacheMiss++,D._NumPipelineCreationCurrentFrame++,this._parameter.pipeline}if(this._setShaderStage(t.uniqueId),this._setRasterizationState(e,r),this._setColorStates(),this._setDepthStencilState(),this._setVertexState(t),this._setTextureState(n),this.lastStateDirtyLowestIndex=this._stateDirtyLowestIndex,!this._isDirty&&this._parameter.pipeline)return this._stateDirtyLowestIndex=this._statesLength,D.NumCacheHitWithoutHash++,this._parameter.pipeline;if(this._getRenderPipeline(this._parameter),this._isDirty=!1,this._stateDirtyLowestIndex=this._statesLength,this._parameter.pipeline)return D.NumCacheHitWithHash++,this._parameter.pipeline;const i=D._GetTopology(e);return this._parameter.pipeline=this._createRenderPipeline(t,i,r),this._setRenderPipeline(this._parameter),D.NumCacheMiss++,D._NumPipelineCreationCurrentFrame++,this._parameter.pipeline}endFrame(){D.NumPipelineCreationLastFrame=D._NumPipelineCreationCurrentFrame,D._NumPipelineCreationCurrentFrame=0}setAlphaToCoverage(e){this._alphaToCoverageEnabled=e}setFrontFace(e){this._frontFace=e}setCullEnabled(e){this._cullEnabled=e}setCullFace(e){this._cullFace=e}setClampDepth(e){this._clampDepth=e}resetDepthCullingState(){this.setDepthCullingState(!1,2,1,0,0,!0,!0,519)}setDepthCullingState(e,t,r,n,i,a,o,u){this._depthWriteEnabled=o,this._depthTestEnabled=a,this._depthCompare=(u??519)-512,this._cullFace=r,this._cullEnabled=e,this._frontFace=t,this.setDepthBiasSlopeScale(n),this.setDepthBias(i)}setDepthBias(e){this._depthBias!==e&&(this._depthBias=e,this._states[F.DepthBias]=e,this._isDirty=!0,this._stateDirtyLowestIndex=Math.min(this._stateDirtyLowestIndex,F.DepthBias))}setDepthBiasSlopeScale(e){this._depthBiasSlopeScale!==e&&(this._depthBiasSlopeScale=e,this._states[F.DepthBiasSlopeScale]=e,this._isDirty=!0,this._stateDirtyLowestIndex=Math.min(this._stateDirtyLowestIndex,F.DepthBiasSlopeScale))}setColorFormat(e){this._webgpuColorFormat[0]=e,this._colorFormat=V[e??""]}setMRTAttachments(e){this.mrtAttachments=e;let t=0;for(let r=0;r<e.length;++r)e[r]!==0&&(t+=1<<r);this._mrtEnabledMask!==t&&(this._mrtEnabledMask=t,this._isDirty=!0,this._stateDirtyLowestIndex=Math.min(this._stateDirtyLowestIndex,F.MRTAttachments1))}setMRT(e,t){if(t=t??e.length,t>10)throw"Can't handle more than 10 attachments for a MRT in cache render pipeline!";this.mrtTextureArray=e,this.mrtTextureCount=t,this._mrtEnabledMask=65535;const r=[0,0];let n=0,i=0,a=0;for(let o=0;o<t;++o){const h=e[o]?._hardwareTexture;this._mrtFormats[a]=h?.format??this._webgpuColorFormat[0],r[n]+=V[this._mrtFormats[a]??""]<<i,i+=6,a++,i>=32&&(i=0,n++)}this._mrtFormats.length=a,(this._mrtAttachments1!==r[0]||this._mrtAttachments2!==r[1])&&(this._mrtAttachments1=r[0],this._mrtAttachments2=r[1],this._states[F.MRTAttachments1]=r[0],this._states[F.MRTAttachments2]=r[1],this._isDirty=!0,this._stateDirtyLowestIndex=Math.min(this._stateDirtyLowestIndex,F.MRTAttachments1))}setAlphaBlendEnabled(e){this._alphaBlendEnabled=e}setAlphaBlendFactors(e,t){this._alphaBlendFuncParams=e,this._alphaBlendEqParams=t}setWriteMask(e){this._writeMask=e}setDepthStencilFormat(e){this._webgpuDepthStencilFormat=e,this._depthStencilFormat=e===void 0?0:V[e]}setDepthTestEnabled(e){this._depthTestEnabled=e}setDepthWriteEnabled(e){this._depthWriteEnabled=e}setDepthCompare(e){this._depthCompare=(e??519)-512}setStencilEnabled(e){this._stencilEnabled=e}setStencilCompare(e){this._stencilFrontCompare=(e??519)-512}setStencilDepthFailOp(e){this._stencilFrontDepthFailOp=e===null?1:H[e]}setStencilPassOp(e){this._stencilFrontPassOp=e===null?2:H[e]}setStencilFailOp(e){this._stencilFrontFailOp=e===null?1:H[e]}setStencilReadMask(e){this._stencilReadMask!==e&&(this._stencilReadMask=e,this._states[F.StencilReadMask]=e,this._isDirty=!0,this._stateDirtyLowestIndex=Math.min(this._stateDirtyLowestIndex,F.StencilReadMask))}setStencilWriteMask(e){this._stencilWriteMask!==e&&(this._stencilWriteMask=e,this._states[F.StencilWriteMask]=e,this._isDirty=!0,this._stateDirtyLowestIndex=Math.min(this._stateDirtyLowestIndex,F.StencilWriteMask))}resetStencilState(){this.setStencilState(!1,519,7680,7681,7680,255,255)}setStencilState(e,t,r,n,i,a,o){this._stencilEnabled=e,this._stencilFrontCompare=(t??519)-512,this._stencilFrontDepthFailOp=r===null?1:H[r],this._stencilFrontPassOp=n===null?2:H[n],this._stencilFrontFailOp=i===null?1:H[i],this.setStencilReadMask(a),this.setStencilWriteMask(o)}setBuffers(e,t,r){this._vertexBuffers=e,this._overrideVertexBuffers=r,this._indexBuffer=t}static _GetTopology(e){switch(e){case 0:return"triangle-list";case 2:return"point-list";case 1:return"line-list";case 3:return"point-list";case 4:return"line-list";case 5:throw"LineLoop is an unsupported fillmode in WebGPU";case 6:return"line-strip";case 7:return"triangle-strip";case 8:throw"TriangleFan is an unsupported fillmode in WebGPU";default:return"triangle-list"}}static _GetAphaBlendOperation(e){switch(e){case 32774:return"add";case 32778:return"subtract";case 32779:return"reverse-subtract";case 32775:return"min";case 32776:return"max";default:return"add"}}static _GetAphaBlendFactor(e){switch(e){case 0:return"zero";case 1:return"one";case 768:return"src";case 769:return"one-minus-src";case 770:return"src-alpha";case 771:return"one-minus-src-alpha";case 772:return"dst-alpha";case 773:return"one-minus-dst-alpha";case 774:return"dst";case 775:return"one-minus-dst";case 776:return"src-alpha-saturated";case 32769:return"constant";case 32770:return"one-minus-constant";case 32771:return"constant";case 32772:return"one-minus-constant";case 35065:return"src1";case 35066:return"one-minus-src1";case 34185:return"src1-alpha";case 35067:return"one-minus-src1-alpha";default:return"one"}}static _GetCompareFunction(e){switch(e){case 0:return"never";case 1:return"less";case 2:return"equal";case 3:return"less-equal";case 4:return"greater";case 5:return"not-equal";case 6:return"greater-equal";case 7:return"always"}return"never"}static _GetStencilOpFunction(e){switch(e){case 0:return"zero";case 1:return"keep";case 2:return"replace";case 3:return"increment-clamp";case 4:return"decrement-clamp";case 5:return"invert";case 6:return"increment-wrap";case 7:return"decrement-wrap"}return"keep"}static _GetVertexInputDescriptorFormat(e){const t=e.type,r=e.normalized,n=e.getSize();switch(t){case S.BYTE:switch(n){case 1:case 2:return r?"snorm8x2":"sint8x2";case 3:case 4:return r?"snorm8x4":"sint8x4"}break;case S.UNSIGNED_BYTE:switch(n){case 1:case 2:return r?"unorm8x2":"uint8x2";case 3:case 4:return r?"unorm8x4":"uint8x4"}break;case S.SHORT:switch(n){case 1:case 2:return r?"snorm16x2":"sint16x2";case 3:case 4:return r?"snorm16x4":"sint16x4"}break;case S.UNSIGNED_SHORT:switch(n){case 1:case 2:return r?"unorm16x2":"uint16x2";case 3:case 4:return r?"unorm16x4":"uint16x4"}break;case S.INT:switch(n){case 1:return"sint32";case 2:return"sint32x2";case 3:return"sint32x3";case 4:return"sint32x4"}break;case S.UNSIGNED_INT:switch(n){case 1:return"uint32";case 2:return"uint32x2";case 3:return"uint32x3";case 4:return"uint32x4"}break;case S.FLOAT:switch(n){case 1:return"float32";case 2:return"float32x2";case 3:return"float32x3";case 4:return"float32x4"}break}throw new Error(`Invalid Format '${e.getKind()}' - type=${t}, normalized=${r}, size=${n}`)}_getAphaBlendState(){return this._alphaBlendEnabled?{srcFactor:D._GetAphaBlendFactor(this._alphaBlendFuncParams[2]),dstFactor:D._GetAphaBlendFactor(this._alphaBlendFuncParams[3]),operation:D._GetAphaBlendOperation(this._alphaBlendEqParams[1])}:null}_getColorBlendState(){return this._alphaBlendEnabled?{srcFactor:D._GetAphaBlendFactor(this._alphaBlendFuncParams[0]),dstFactor:D._GetAphaBlendFactor(this._alphaBlendFuncParams[1]),operation:D._GetAphaBlendOperation(this._alphaBlendEqParams[0])}:null}_setShaderStage(e){this._shaderId!==e&&(this._shaderId=e,this._states[F.ShaderStage]=e,this._isDirty=!0,this._stateDirtyLowestIndex=Math.min(this._stateDirtyLowestIndex,F.ShaderStage))}_setRasterizationState(e,t){const r=this._frontFace,n=this._cullEnabled?this._cullFace:0,i=this._clampDepth?1:0,a=this._alphaToCoverageEnabled?1:0,o=r-1+(n<<1)+(i<<3)+(a<<4)+(e<<5)+(t<<8);this._rasterizationState!==o&&(this._rasterizationState=o,this._states[F.RasterizationState]=this._rasterizationState,this._isDirty=!0,this._stateDirtyLowestIndex=Math.min(this._stateDirtyLowestIndex,F.RasterizationState))}_setColorStates(){let e=((this._writeMask?1:0)<<22)+(this._colorFormat<<23)+((this._depthWriteEnabled?1:0)<<29);this._alphaBlendEnabled&&(e+=((this._alphaBlendFuncParams[0]===null?2:Z[this._alphaBlendFuncParams[0]])<<0)+((this._alphaBlendFuncParams[1]===null?2:Z[this._alphaBlendFuncParams[1]])<<4)+((this._alphaBlendFuncParams[2]===null?2:Z[this._alphaBlendFuncParams[2]])<<8)+((this._alphaBlendFuncParams[3]===null?2:Z[this._alphaBlendFuncParams[3]])<<12)+((this._alphaBlendEqParams[0]===null?1:this._alphaBlendEqParams[0]-32773)<<16)+((this._alphaBlendEqParams[1]===null?1:this._alphaBlendEqParams[1]-32773)<<19)),e!==this._colorStates&&(this._colorStates=e,this._states[F.ColorStates]=this._colorStates,this._isDirty=!0,this._stateDirtyLowestIndex=Math.min(this._stateDirtyLowestIndex,F.ColorStates))}_setDepthStencilState(){const e=this._stencilEnabled?this._stencilFrontCompare+(this._stencilFrontDepthFailOp<<3)+(this._stencilFrontPassOp<<6)+(this._stencilFrontFailOp<<9):591,t=this._depthStencilFormat+((this._depthTestEnabled?this._depthCompare:7)<<6)+(e<<10);this._depthStencilState!==t&&(this._depthStencilState=t,this._states[F.DepthStencilState]=this._depthStencilState,this._isDirty=!0,this._stateDirtyLowestIndex=Math.min(this._stateDirtyLowestIndex,F.DepthStencilState))}_setVertexState(e){const t=this._statesLength;let r=F.VertexState;const n=e._pipelineContext,i=n.shaderProcessingContext.attributeNamesFromEffect,a=n.shaderProcessingContext.attributeLocationsFromEffect;let o,u=0;for(let h=0;h<i.length;h++){const c=a[h];let l=(this._overrideVertexBuffers&&this._overrideVertexBuffers[i[h]])??this._vertexBuffers[i[h]];l||(l=this._emptyVertexBuffer,D.LogErrorIfNoVertexBuffer&&y.Error(`No vertex buffer is provided for the "${i[h]}" attribute. A default empty vertex buffer will be used, but this may generate errors in some browsers.`));const d=l.effectiveBuffer?.underlyingResource;if(l._validOffsetRange===void 0){const f=l.effectiveByteOffset,_=l.getSize(!0),p=l.effectiveByteStride;l._validOffsetRange=f+_<=this._kMaxVertexBufferStride&&p===0||p!==0&&f+_<=p}o&&o===d&&l._validOffsetRange||(this.vertexBuffers[u++]=l,o=l._validOffsetRange?d:null);const g=l.hashCode+(c<<7);this._isDirty=this._isDirty||this._states[r]!==g,this._states[r++]=g}this.vertexBuffers.length=u,this._statesLength=r,this._isDirty=this._isDirty||r!==t,this._isDirty&&(this._stateDirtyLowestIndex=Math.min(this._stateDirtyLowestIndex,F.VertexState))}_setTextureState(e){this._textureState!==e&&(this._textureState=e,this._states[F.TextureStage]=this._textureState,this._isDirty=!0,this._stateDirtyLowestIndex=Math.min(this._stateDirtyLowestIndex,F.TextureStage))}_createPipelineLayout(e){if(this._useTextureStage)return this._createPipelineLayoutWithTextureStage(e);const t=[],r=e.shaderProcessingContext.bindGroupLayoutEntries;for(let n=0;n<r.length;n++){const i=r[n];t[n]=this._device.createBindGroupLayout({entries:i})}return e.bindGroupLayouts[0]=t,this._device.createPipelineLayout({bindGroupLayouts:t})}_createPipelineLayoutWithTextureStage(e){const t=e.shaderProcessingContext,r=t.bindGroupLayoutEntries;let n=1;for(let a=0;a<r.length;a++){const o=r[a];for(let u=0;u<o.length;u++){const h=r[a][u];if(h.texture){const c=t.bindGroupLayoutEntryInfo[a][h.binding].name,l=t.availableTextures[c],d=l.autoBindSampler?t.availableSamplers[c+"Sampler"]:null;let g=l.sampleType,f=d?.type??"filtering";if(this._textureState&n&&g!=="depth"&&(l.autoBindSampler&&(f="non-filtering"),g="unfilterable-float"),h.texture.sampleType=g,d){const _=t.bindGroupLayoutEntryInfo[d.binding.groupIndex][d.binding.bindingIndex].index;r[d.binding.groupIndex][_].sampler.type=f}n=n<<1}}}const i=[];for(let a=0;a<r.length;++a)i[a]=this._device.createBindGroupLayout({entries:r[a]});return e.bindGroupLayouts[this._textureState]=i,this._device.createPipelineLayout({bindGroupLayouts:i})}_getVertexInputDescriptor(e){const t=[],r=e._pipelineContext,n=r.shaderProcessingContext.attributeNamesFromEffect,i=r.shaderProcessingContext.attributeLocationsFromEffect;let a,o;for(let u=0;u<n.length;u++){const h=i[u];let c=(this._overrideVertexBuffers&&this._overrideVertexBuffers[n[u]])??this._vertexBuffers[n[u]];c||(c=this._emptyVertexBuffer);let l=c.effectiveBuffer?.underlyingResource,d=c.effectiveByteOffset;const g=!c._validOffsetRange;if(!(a&&o&&a===l)||g){const f={arrayStride:c.effectiveByteStride,stepMode:c.getIsInstanced()?"instance":"vertex",attributes:[]};t.push(f),o=f.attributes,g&&(d=0,l=null)}o.push({shaderLocation:h,offset:d,format:D._GetVertexInputDescriptorFormat(c)}),a=l}return t}_createRenderPipeline(e,t,r){const n=e._pipelineContext,i=this._getVertexInputDescriptor(e),a=this._createPipelineLayout(n),o=[],u=this._getAphaBlendState(),h=this._getColorBlendState();if(this._vertexBuffers&&Ir(this._vertexBuffers,e),this._mrtAttachments1>0)for(let f=0;f<this._mrtFormats.length;++f){const _=this._mrtFormats[f];if(_){const p={format:_,writeMask:this._mrtEnabledMask&1<<f?this._writeMask:0};u&&h&&(p.blend={alpha:u,color:h}),o.push(p)}else o.push(null)}else if(this._webgpuColorFormat[0]){const f={format:this._webgpuColorFormat[0],writeMask:this._writeMask};u&&h&&(f.blend={alpha:u,color:h}),o.push(f)}else o.push(null);const c={compare:D._GetCompareFunction(this._stencilEnabled?this._stencilFrontCompare:7),depthFailOp:D._GetStencilOpFunction(this._stencilEnabled?this._stencilFrontDepthFailOp:1),failOp:D._GetStencilOpFunction(this._stencilEnabled?this._stencilFrontFailOp:1),passOp:D._GetStencilOpFunction(this._stencilEnabled?this._stencilFrontPassOp:1)},l=t==="triangle-list"||t==="triangle-strip";let d;(t==="line-strip"||t==="triangle-strip")&&(d=!this._indexBuffer||this._indexBuffer.is32Bits?"uint32":"uint16");const g=this._webgpuDepthStencilFormat?C.HasStencilAspect(this._webgpuDepthStencilFormat):!1;return this._device.createRenderPipeline({label:`RenderPipeline_${o[0]?.format??"nooutput"}_${this._webgpuDepthStencilFormat??"nodepth"}_samples${r}_textureState${this._textureState}`,layout:a,vertex:{module:n.stages.vertexStage.module,entryPoint:n.stages.vertexStage.entryPoint,buffers:i},primitive:{topology:t,stripIndexFormat:d,frontFace:this._frontFace===1?"ccw":"cw",cullMode:this._cullEnabled?this._cullFace===2?"front":"back":"none"},fragment:n.stages.fragmentStage?{module:n.stages.fragmentStage.module,entryPoint:n.stages.fragmentStage.entryPoint,targets:o}:void 0,multisample:{count:r},depthStencil:this._webgpuDepthStencilFormat===void 0?void 0:{depthWriteEnabled:this._depthWriteEnabled,depthCompare:this._depthTestEnabled?D._GetCompareFunction(this._depthCompare):"always",format:this._webgpuDepthStencilFormat,stencilFront:this._stencilEnabled&&g?c:void 0,stencilBack:this._stencilEnabled&&g?c:void 0,stencilReadMask:this._stencilEnabled&&g?this._stencilReadMask:void 0,stencilWriteMask:this._stencilEnabled&&g?this._stencilWriteMask:void 0,depthBias:this._depthBias,depthBiasClamp:l?this._depthBiasClamp:0,depthBiasSlopeScale:l?this._depthBiasSlopeScale:0}})}}D.LogErrorIfNoVertexBuffer=!1;D.NumCacheHitWithoutHash=0;D.NumCacheHitWithHash=0;D.NumCacheMiss=0;D.NumPipelineCreationLastFrame=0;D._NumPipelineCreationCurrentFrame=0;class de{constructor(){this.values={}}count(){let e=0,t=this.pipeline?1:0;for(const r in this.values){const n=this.values[r],[i,a]=n.count();e+=i,t+=a,e++}return[e,t]}}class $ extends D{static GetNodeCounts(){const e=$._Cache.count();return{nodeCount:e[0],pipelineCount:e[1]}}static _GetPipelines(e,t,r,n){if(e.pipeline){const i=r.slice();i.length=n,t.push(i)}for(const i in e.values){const a=e.values[i];r[n]=parseInt(i),$._GetPipelines(a,t,r,n+1)}}static GetPipelines(){const e=[];return $._GetPipelines($._Cache,e,[],0),e}static ResetCache(){$._Cache=new de}reset(){this._nodeStack=[],this._nodeStack[0]=$._Cache,super.reset()}_getRenderPipeline(e){let t=this._nodeStack[this._stateDirtyLowestIndex];for(let r=this._stateDirtyLowestIndex;r<this._statesLength;++r){let n=t.values[this._states[r]];n||(n=new de,t.values[this._states[r]]=n),t=n,this._nodeStack[r+1]=t}e.token=t,e.pipeline=t.pipeline}_setRenderPipeline(e){e.token.pipeline=e.pipeline}}$._Cache=new de;class Cr extends Dt{constructor(e){super(!1),this._cache=e,this.reset()}get func(){return this._func}set func(e){this._func!==e&&(this._func=e,this._cache.setStencilCompare(e))}get funcMask(){return this._funcMask}set funcMask(e){this._funcMask!==e&&(this._funcMask=e,this._cache.setStencilReadMask(e))}get opStencilFail(){return this._opStencilFail}set opStencilFail(e){this._opStencilFail!==e&&(this._opStencilFail=e,this._cache.setStencilFailOp(e))}get opDepthFail(){return this._opDepthFail}set opDepthFail(e){this._opDepthFail!==e&&(this._opDepthFail=e,this._cache.setStencilDepthFailOp(e))}get opStencilDepthPass(){return this._opStencilDepthPass}set opStencilDepthPass(e){this._opStencilDepthPass!==e&&(this._opStencilDepthPass=e,this._cache.setStencilPassOp(e))}get mask(){return this._mask}set mask(e){this._mask!==e&&(this._mask=e,this._cache.setStencilWriteMask(e))}get enabled(){return this._enabled}set enabled(e){this._enabled!==e&&(this._enabled=e,this._cache.setStencilEnabled(e))}reset(){super.reset(),this._cache.resetStencilState()}apply(){const e=this.stencilMaterial?.enabled;this.enabled=e?this.stencilMaterial.enabled:this.stencilGlobal.enabled,this.enabled&&(this.func=e?this.stencilMaterial.func:this.stencilGlobal.func,this.funcRef=e?this.stencilMaterial.funcRef:this.stencilGlobal.funcRef,this.funcMask=e?this.stencilMaterial.funcMask:this.stencilGlobal.funcMask,this.opStencilFail=e?this.stencilMaterial.opStencilFail:this.stencilGlobal.opStencilFail,this.opDepthFail=e?this.stencilMaterial.opDepthFail:this.stencilGlobal.opDepthFail,this.opStencilDepthPass=e?this.stencilMaterial.opStencilDepthPass:this.stencilGlobal.opStencilDepthPass,this.mask=e?this.stencilMaterial.mask:this.stencilGlobal.mask)}}class Tr extends Pt{constructor(e){super(!1),this._cache=e,this.reset()}get zOffset(){return this._zOffset}set zOffset(e){this._zOffset!==e&&(this._zOffset=e,this._isZOffsetDirty=!0,this._cache.setDepthBiasSlopeScale(e))}get zOffsetUnits(){return this._zOffsetUnits}set zOffsetUnits(e){this._zOffsetUnits!==e&&(this._zOffsetUnits=e,this._isZOffsetDirty=!0,this._cache.setDepthBias(e))}get cullFace(){return this._cullFace}set cullFace(e){this._cullFace!==e&&(this._cullFace=e,this._isCullFaceDirty=!0,this._cache.setCullFace(e??1))}get cull(){return this._cull}set cull(e){this._cull!==e&&(this._cull=e,this._isCullDirty=!0,this._cache.setCullEnabled(!!e))}get depthFunc(){return this._depthFunc}set depthFunc(e){this._depthFunc!==e&&(this._depthFunc=e,this._isDepthFuncDirty=!0,this._cache.setDepthCompare(e))}get depthMask(){return this._depthMask}set depthMask(e){this._depthMask!==e&&(this._depthMask=e,this._isDepthMaskDirty=!0,this._cache.setDepthWriteEnabled(e))}get depthTest(){return this._depthTest}set depthTest(e){this._depthTest!==e&&(this._depthTest=e,this._isDepthTestDirty=!0,this._cache.setDepthTestEnabled(e))}get frontFace(){return this._frontFace}set frontFace(e){this._frontFace!==e&&(this._frontFace=e,this._isFrontFaceDirty=!0,this._cache.setFrontFace(e??2))}reset(){super.reset(),this._cache.resetDepthCullingState()}apply(){}}class wt{static IsExternalTexture(e){return e.underlyingResource!==void 0}getClassName(){return"ExternalTexture"}get underlyingResource(){return this._video}constructor(e){this.useMipMaps=!1,this.type=16,this.format=4294967295,this._video=e,this.uniqueId=W._Counter++}isReady(){return this._video.readyState>=this._video.HAVE_CURRENT_DATA}dispose(){}}class ie{get forceBindGroupCreation(){return this._numExternalTextures>0}get hasFloatOrDepthTextures(){return this._numFloatOrDepthTextures>0}constructor(){this.uniqueId=ie._Counter++,this.updateId=0,this.textureState=0,this.reset()}reset(){this.samplers={},this.textures={},this.isDirty=!0,this._numFloatOrDepthTextures=0,this._numExternalTextures=0}setSampler(e,t){let r=this.samplers[e],n=-1;r?n=r.hashCode:this.samplers[e]=r={sampler:t,hashCode:0},r.sampler=t,r.hashCode=t?Q.GetSamplerHashCode(t):0;const i=n!==r.hashCode;i&&this.updateId++,this.isDirty||(this.isDirty=i)}setTexture(e,t){let r=this.textures[e],n=-1;r?n=r.texture?.uniqueId??-1:this.textures[e]=r={texture:t,isFloatOrDepthTexture:!1,isExternalTexture:!1},r.isExternalTexture&&this._numExternalTextures--,r.isFloatOrDepthTexture&&this._numFloatOrDepthTextures--,t?(r.isFloatOrDepthTexture=t.type===1||t.format>=13&&t.format<=18,r.isExternalTexture=wt.IsExternalTexture(t),r.isFloatOrDepthTexture&&this._numFloatOrDepthTextures++,r.isExternalTexture&&this._numExternalTextures++):(r.isFloatOrDepthTexture=!1,r.isExternalTexture=!1),r.texture=t;const i=n!==(t?.uniqueId??-1);i&&this.updateId++,this.isDirty||(this.isDirty=i)}}ie._Counter=0;class ae{isDirty(e){return this._isDirty||this._materialContextUpdateId!==e}resetIsDirty(e){this._isDirty=!1,this._materialContextUpdateId=e}get useInstancing(){return this._useInstancing}set useInstancing(e){this._useInstancing!==e&&(e?(this.indirectDrawBuffer=this._bufferManager.createRawBuffer(20,T.CopyDst|T.Indirect|T.Storage,void 0,"IndirectDrawBuffer"),this._indirectDrawData=new Uint32Array(5),this._indirectDrawData[3]=0,this._indirectDrawData[4]=0):(this.indirectDrawBuffer&&this._bufferManager.releaseBuffer(this.indirectDrawBuffer),this.indirectDrawBuffer=void 0,this._indirectDrawData=void 0),this._useInstancing=e,this._currentInstanceCount=-1)}constructor(e){this._bufferManager=e,this.uniqueId=ae._Counter++,this._useInstancing=!1,this._currentInstanceCount=0,this.reset()}reset(){this.buffers={},this._isDirty=!0,this._materialContextUpdateId=0,this.fastBundle=void 0,this.bindGroups=void 0}setBuffer(e,t){this._isDirty||(this._isDirty=t?.uniqueId!==this.buffers[e]?.uniqueId),this.buffers[e]=t}setIndirectData(e,t,r){t===this._currentInstanceCount||!this.indirectDrawBuffer||!this._indirectDrawData||(this._currentInstanceCount=t,this._indirectDrawData[0]=e,this._indirectDrawData[1]=t,this._indirectDrawData[2]=r,this._bufferManager.setRawData(this.indirectDrawBuffer,0,this._indirectDrawData,0,20))}dispose(){this.indirectDrawBuffer&&(this._bufferManager.releaseBuffer(this.indirectDrawBuffer),this.indirectDrawBuffer=void 0,this._indirectDrawData=void 0),this.fastBundle=void 0,this.bindGroups=void 0,this.buffers=void 0}}ae._Counter=0;const Br=1<<20,vr=2**35;class K{constructor(){this.values={}}}class B{static get Statistics(){return{totalCreated:B.NumBindGroupsCreatedTotal,lastFrameCreated:B.NumBindGroupsCreatedLastFrame,lookupLastFrame:B.NumBindGroupsLookupLastFrame,noLookupLastFrame:B.NumBindGroupsNoLookupLastFrame}}static ResetCache(){B._Cache=new K,B.NumBindGroupsCreatedTotal=0,B.NumBindGroupsCreatedLastFrame=0,B.NumBindGroupsLookupLastFrame=0,B.NumBindGroupsNoLookupLastFrame=0,B._NumBindGroupsCreatedCurrentFrame=0,B._NumBindGroupsLookupCurrentFrame=0,B._NumBindGroupsNoLookupCurrentFrame=0}constructor(e,t,r){this.disabled=!1,this._device=e,this._cacheSampler=t,this._engine=r}endFrame(){B.NumBindGroupsCreatedLastFrame=B._NumBindGroupsCreatedCurrentFrame,B.NumBindGroupsLookupLastFrame=B._NumBindGroupsLookupCurrentFrame,B.NumBindGroupsNoLookupLastFrame=B._NumBindGroupsNoLookupCurrentFrame,B._NumBindGroupsCreatedCurrentFrame=0,B._NumBindGroupsLookupCurrentFrame=0,B._NumBindGroupsNoLookupCurrentFrame=0}getBindGroups(e,t,r){let n,i=B._Cache;const a=this.disabled||r.forceBindGroupCreation;if(!a){if(!t.isDirty(r.updateId)&&!r.isDirty)return B._NumBindGroupsNoLookupCurrentFrame++,t.bindGroups;for(const u of e.shaderProcessingContext.bufferNames){const h=(t.buffers[u]?.uniqueId??0)+Br;let c=i.values[h];c||(c=new K,i.values[h]=c),i=c}for(const u of e.shaderProcessingContext.samplerNames){const h=r.samplers[u]?.hashCode??0;let c=i.values[h];c||(c=new K,i.values[h]=c),i=c}for(const u of e.shaderProcessingContext.textureNames){const h=(r.textures[u]?.texture?.uniqueId??0)+vr;let c=i.values[h];c||(c=new K,i.values[h]=c),i=c}n=i.bindGroups}if(t.resetIsDirty(r.updateId),r.isDirty=!1,n)return t.bindGroups=n,B._NumBindGroupsLookupCurrentFrame++,n;n=[],t.bindGroups=n,a||(i.bindGroups=n),B.NumBindGroupsCreatedTotal++,B._NumBindGroupsCreatedCurrentFrame++;const o=e.bindGroupLayouts[r.textureState];for(let u=0;u<e.shaderProcessingContext.bindGroupLayoutEntries.length;u++){const h=e.shaderProcessingContext.bindGroupLayoutEntries[u],c=e.shaderProcessingContext.bindGroupEntries[u];for(let d=0;d<h.length;d++){const g=e.shaderProcessingContext.bindGroupLayoutEntries[u][d],f=e.shaderProcessingContext.bindGroupLayoutEntryInfo[u][g.binding],_=f.nameInArrayOfTexture??f.name;if(g.sampler){const p=r.samplers[_];if(p){const m=p.sampler;if(!m){this._engine.dbgSanityChecks&&y.Error(`Trying to bind a null sampler! entry=${JSON.stringify(g)}, name=${_}, bindingInfo=${JSON.stringify(p,(x,b)=>x==="texture"?"<no dump>":b)}, materialContext.uniqueId=${r.uniqueId}`,50);continue}c[d].resource=this._cacheSampler.getSampler(m,!1,p.hashCode,m.label)}else y.Error(`Sampler "${_}" could not be bound. entry=${JSON.stringify(g)}, materialContext=${JSON.stringify(r,(m,x)=>m==="texture"||m==="sampler"?"<no dump>":x)}`,50)}else if(g.texture||g.storageTexture){const p=r.textures[_];if(p){if(this._engine.dbgSanityChecks&&p.texture===null){y.Error(`Trying to bind a null texture! name="${_}", entry=${JSON.stringify(g)}, bindingInfo=${JSON.stringify(p,(x,b)=>x==="texture"?"<no dump>":b)}, materialContext.uniqueId=${r.uniqueId}`,50);continue}const m=p.texture._hardwareTexture;if(this._engine.dbgSanityChecks&&(!m||g.texture&&!m.view||g.storageTexture&&!m.viewForWriting)){y.Error(`Trying to bind a null gpu texture or view! entry=${JSON.stringify(g)}, name=${_}, bindingInfo=${JSON.stringify(p,(x,b)=>x==="texture"?"<no dump>":b)}, isReady=${p.texture?.isReady}, materialContext.uniqueId=${r.uniqueId}`,50);continue}c[d].resource=g.storageTexture?m.viewForWriting:m.view}else y.Error(`Texture "${_}" could not be bound. entry=${JSON.stringify(g)}, materialContext=${JSON.stringify(r,(m,x)=>m==="texture"||m==="sampler"?"<no dump>":x)}`,50)}else if(g.externalTexture){const p=r.textures[_];if(p){if(this._engine.dbgSanityChecks&&p.texture===null){y.Error(`Trying to bind a null external texture! entry=${JSON.stringify(g)}, name=${_}, bindingInfo=${JSON.stringify(p,(x,b)=>x==="texture"?"<no dump>":b)}, materialContext.uniqueId=${r.uniqueId}`,50);continue}const m=p.texture.underlyingResource;if(this._engine.dbgSanityChecks&&!m){y.Error(`Trying to bind a null gpu external texture! entry=${JSON.stringify(g)}, name=${_}, bindingInfo=${JSON.stringify(p,(x,b)=>x==="texture"?"<no dump>":b)}, isReady=${p.texture?.isReady}, materialContext.uniqueId=${r.uniqueId}`,50);continue}c[d].resource=this._device.importExternalTexture({source:m})}else y.Error(`Texture "${_}" could not be bound. entry=${JSON.stringify(g)}, materialContext=${JSON.stringify(r,(m,x)=>m==="texture"||m==="sampler"?"<no dump>":x)}`,50)}else if(g.buffer){const p=t.buffers[_];if(p){const m=p.underlyingResource;c[d].resource.buffer=m,c[d].resource.size=p.capacity}else y.Error(`Can't find buffer "${_}". entry=${JSON.stringify(g)}, buffers=${JSON.stringify(t.buffers)}, drawContext.uniqueId=${t.uniqueId}`,50)}}const l=o[u];n[u]=this._device.createBindGroup({layout:l,entries:c})}return n}}B.NumBindGroupsCreatedTotal=0;B.NumBindGroupsCreatedLastFrame=0;B.NumBindGroupsLookupLastFrame=0;B.NumBindGroupsNoLookupLastFrame=0;B._Cache=new K;B._NumBindGroupsCreatedCurrentFrame=0;B._NumBindGroupsLookupCurrentFrame=0;B._NumBindGroupsNoLookupCurrentFrame=0;const gt="clearQuadVertexShader",Rr=`uniform depthValue: f32;const pos=array(
vec2f(-1.0,1.0),
vec2f(1.0,1.0),
vec2f(-1.0,-1.0),
vec2f(1.0,-1.0)
);
#define CUSTOM_VERTEX_DEFINITIONS
@vertex
fn main(input : VertexInputs)->FragmentInputs {
#define CUSTOM_VERTEX_MAIN_BEGIN
vertexOutputs.position=vec4f(pos[input.vertexIndex],uniforms.depthValue,1.0);
#define CUSTOM_VERTEX_MAIN_END
}
`;re.ShadersStoreWGSL[gt]||(re.ShadersStoreWGSL[gt]=Rr);const _t="clearQuadPixelShader",Ar=`uniform color: vec4f;@fragment
fn main(input: FragmentInputs)->FragmentOutputs {fragmentOutputs.color=uniforms.color;}
`;re.ShadersStoreWGSL[_t]||(re.ShadersStoreWGSL[_t]=Ar);class Dr{setDepthStencilFormat(e){this._depthTextureFormat=e,this._cacheRenderPipeline.setDepthStencilFormat(e)}setColorFormat(e){this._cacheRenderPipeline.setColorFormat(e)}setMRTAttachments(e,t,r){this._cacheRenderPipeline.setMRT(t,r),this._cacheRenderPipeline.setMRTAttachments(e)}constructor(e,t,r){this._bindGroups={},this._bundleCache={},this._keyTemp=[],this._device=e,this._engine=t,this._cacheRenderPipeline=new $(this._device,r),this._cacheRenderPipeline.setDepthTestEnabled(!1),this._cacheRenderPipeline.setStencilReadMask(255),this._effect=t.createEffect("clearQuad",[],["color","depthValue"],void 0,void 0,void 0,void 0,void 0,void 0,1)}clear(e,t,r,n,i=1){let a,o=null,u;const h=!!this._engine._currentRenderTarget;if(e)a=e;else{let p=0;this._keyTemp.length=0;for(let x=0;x<this._cacheRenderPipeline.colorFormats.length;++x)this._keyTemp[p++]=V[this._cacheRenderPipeline.colorFormats[x]??""];const m=V[this._depthTextureFormat??0];if(this._keyTemp[p]=(t?t.r+t.g*256+t.b*256*256+t.a*256*256*256:0)+(r?2**32:0)+(n?2**33:0)+(this._engine.useReverseDepthBuffer?2**34:0)+(h?2**35:0)+(i>1?2**36:0)+m*2**37,u=this._keyTemp.join("_"),o=this._bundleCache[u],o)return o;a=this._device.createRenderBundleEncoder({label:"clearQuadRenderBundle",colorFormats:this._cacheRenderPipeline.colorFormats,depthStencilFormat:this._depthTextureFormat,sampleCount:C.GetSample(i)})}this._cacheRenderPipeline.setDepthWriteEnabled(!!r),this._cacheRenderPipeline.setStencilEnabled(!!n&&!!this._depthTextureFormat&&C.HasStencilAspect(this._depthTextureFormat)),this._cacheRenderPipeline.setStencilWriteMask(n?255:0),this._cacheRenderPipeline.setStencilCompare(n?519:512),this._cacheRenderPipeline.setStencilPassOp(n?7681:7680),this._cacheRenderPipeline.setWriteMask(t?15:0);const c=this._cacheRenderPipeline.getRenderPipeline(7,this._effect,i),l=this._effect._pipelineContext;t&&this._effect.setDirectColor4("color",t),this._effect.setFloat("depthValue",this._engine.useReverseDepthBuffer?this._engine._clearReverseDepthValue:this._engine._clearDepthValue),l.uniformBuffer.update();const d=h?this._engine._ubInvertY:this._engine._ubDontInvertY,g=l.uniformBuffer.getBuffer(),f=g.uniqueId+"-"+d.uniqueId;let _=this._bindGroups[f];if(!_){const p=l.bindGroupLayouts[0];_=this._bindGroups[f]=[],_.push(this._device.createBindGroup({label:`clearQuadBindGroup0-${f}`,layout:p[0],entries:[]})),O._SimplifiedKnownBindings||_.push(this._device.createBindGroup({label:`clearQuadBindGroup1-${f}`,layout:p[1],entries:[]})),_.push(this._device.createBindGroup({label:`clearQuadBindGroup${O._SimplifiedKnownBindings?1:2}-${f}`,layout:p[O._SimplifiedKnownBindings?1:2],entries:[{binding:0,resource:{buffer:d.underlyingResource,size:d.capacity}},{binding:1,resource:{buffer:g.underlyingResource,size:g.capacity}}]}))}a.setPipeline(c);for(let p=0;p<_.length;++p)a.setBindGroup(p,_[p]);return a.draw(4,1,0,0),e||(o=a.finish(),this._bundleCache[u]=o),o}}class fe{constructor(e,t,r,n){this.x=Math.floor(e),this.y=Math.floor(t),this.w=Math.floor(r),this.h=Math.floor(n)}run(e){e.setViewport(this.x,this.y,this.w,this.h,0,1)}clone(){return new fe(this.x,this.y,this.w,this.h)}}class ge{constructor(e,t,r,n){this.x=e,this.y=t,this.w=r,this.h=n}run(e){e.setScissorRect(this.x,this.y,this.w,this.h)}clone(){return new ge(this.x,this.y,this.w,this.h)}}class ne{constructor(e){this.ref=e}run(e){e.setStencilReference(this.ref)}clone(){return new ne(this.ref)}}class _e{constructor(e){this.color=e}run(e){e.setBlendConstant(this.color)}clone(){return new _e(this.color)}}class me{constructor(e){this.query=e}run(e){e.beginOcclusionQuery(this.query)}clone(){return new me(this.query)}}class be{constructor(){}run(e){e.endOcclusionQuery()}clone(){return new be}}class xe{constructor(){this.bundles=[]}run(e){e.executeBundles(this.bundles)}clone(){const e=new xe;return e.bundles=this.bundles,e}}class ye{constructor(e){this.numDrawCalls=0,this._device=e,this._list=new Array(10),this._listLength=0}addBundle(e){if(!this._currentItemIsBundle){const t=new xe;this._list[this._listLength++]=t,this._currentBundleList=t.bundles,this._currentItemIsBundle=!0}e&&this._currentBundleList.push(e)}_finishBundle(){this._currentItemIsBundle&&this._bundleEncoder&&(this._currentBundleList.push(this._bundleEncoder.finish()),this._bundleEncoder=void 0,this._currentItemIsBundle=!1)}addItem(e){this._finishBundle(),this._list[this._listLength++]=e,this._currentItemIsBundle=!1}getBundleEncoder(e,t,r){return this._currentItemIsBundle||(this.addBundle(),this._bundleEncoder=this._device.createRenderBundleEncoder({colorFormats:e,depthStencilFormat:t,sampleCount:C.GetSample(r)})),this._bundleEncoder}close(){this._finishBundle()}run(e){this.close();for(let t=0;t<this._listLength;++t)this._list[t].run(e)}reset(){this._listLength=0,this._currentItemIsBundle=!1,this.numDrawCalls=0}clone(){this.close();const e=new ye(this._device);e._list=new Array(this._listLength),e._listLength=this._listLength,e.numDrawCalls=this.numDrawCalls;for(let t=0;t<this._listLength;++t)e._list[t]=this._list[t].clone();return e}}class It{get querySet(){return this._querySet}constructor(e,t,r,n,i,a=!0,o){this._dstBuffers=[],this._engine=e,this._device=n,this._bufferManager=i,this._count=t,this._canUseMultipleBuffers=a,this._querySet=n.createQuerySet({label:o??"QuerySet",type:r,count:t}),this._queryBuffer=i.createRawBuffer(8*t,T.QueryResolve|T.CopySrc,void 0,"QueryBuffer"),a||this._dstBuffers.push(this._bufferManager.createRawBuffer(8*this._count,T.MapRead|T.CopyDst,void 0,"QueryBufferNoMultipleBuffers"))}_getBuffer(e,t){if(!this._canUseMultipleBuffers&&this._dstBuffers.length===0)return null;const r=this._device.createCommandEncoder();let n;return this._dstBuffers.length===0?n=this._bufferManager.createRawBuffer(8*this._count,T.MapRead|T.CopyDst,void 0,"QueryBufferAdditionalBuffer"):(n=this._dstBuffers[this._dstBuffers.length-1],this._dstBuffers.length--),r.resolveQuerySet(this._querySet,e,t,this._queryBuffer,0),r.copyBufferToBuffer(this._queryBuffer,0,n,0,8*t),this._device.queue.submit([r.finish()]),n}async readValues(e=0,t=1){const r=this._getBuffer(e,t);if(r===null)return null;const n=this._engine.uniqueId;return r.mapAsync(1).then(()=>{const i=new BigUint64Array(r.getMappedRange()).slice();return r.unmap(),this._dstBuffers[this._dstBuffers.length]=r,i},i=>{if(this._engine.isDisposed||this._engine.uniqueId!==n)return null;throw i})}async readValue(e=0){const t=this._getBuffer(e,1);if(t===null)return null;const r=this._engine.uniqueId;return t.mapAsync(1).then(()=>{const n=new BigUint64Array(t.getMappedRange()),i=Number(n[0]);return t.unmap(),this._dstBuffers[this._dstBuffers.length]=t,i},n=>{if(this._engine.isDisposed||this._engine.uniqueId!==r)return 0;throw n})}async readTwoValuesAndSubtract(e=0){const t=this._getBuffer(e,2);if(t===null)return null;const r=this._engine.uniqueId;return t.mapAsync(1).then(()=>{const n=new BigUint64Array(t.getMappedRange()),i=Number(n[1]-n[0]);return t.unmap(),this._dstBuffers[this._dstBuffers.length]=t,i},n=>{if(this._engine.isDisposed||this._engine.uniqueId!==r)return 0;throw n})}dispose(){this._querySet.destroy(),this._bufferManager.releaseBuffer(this._queryBuffer);for(let e=0;e<this._dstBuffers.length;++e)this._bufferManager.releaseBuffer(this._dstBuffers[e])}}class Pr{get gpuFrameTimeCounter(){return this._gpuFrameTimeCounter}constructor(e,t,r){this._enabled=!1,this._gpuFrameTimeCounter=new pe,this._measureDurationState=0,this._engine=e,this._device=t,this._bufferManager=r}get enable(){return this._enabled}set enable(e){if(this._enabled!==e)if(this._enabled=e,this._measureDurationState=0,e)try{this._measureDuration=new Fr(this._engine,this._device,this._bufferManager,2e3,"QuerySet_TimestampQuery")}catch(t){this._enabled=!1,y.Error(`Could not create a WebGPUDurationMeasure!
Error: `+t.message+`
Make sure timestamp query is supported and enabled in your browser.`);return}else this._measureDuration.dispose()}startFrame(e){this._enabled&&this._measureDurationState===0&&(this._measureDuration.start(e),this._measureDurationState=1)}endFrame(e){this._measureDurationState===1&&(this._measureDurationState=2,this._measureDuration.stop(e).then(t=>{t!==null&&t>=0&&(this._gpuFrameTimeCounter.fetchNewFrame(),this._gpuFrameTimeCounter.addCount(t,!0)),this._measureDurationState=0}))}startPass(e,t){this._enabled?this._measureDuration.startPass(e,t):e.timestampWrites=void 0}endPass(e,t){if(!this._enabled||!t)return;const r=this._engine.frameId;this._measureDuration.stopPass(e).then(n=>{t._addDuration(r,n!==null&&n>0?n:0)})}dispose(){this._measureDuration?.dispose()}}class Fr{constructor(e,t,r,n=2,i){this._count=n,this._querySet=new It(e,n,"timestamp",t,r,!0,i)}start(e){e.writeTimestamp?.(this._querySet.querySet,0)}async stop(e){return e.writeTimestamp?.(this._querySet.querySet,1),e.writeTimestamp?this._querySet.readTwoValuesAndSubtract(0):0}startPass(e,t){if(t+3>this._count)throw new Error("WebGPUDurationMeasure: index out of range ("+t+")");e.timestampWrites={querySet:this._querySet.querySet,beginningOfPassWriteIndex:t+2,endOfPassWriteIndex:t+3}}async stopPass(e){return this._querySet.readTwoValuesAndSubtract(e+2)}dispose(){this._querySet.dispose()}}class Lr{get querySet(){return this._querySet.querySet}get hasQueries(){return this._currentTotalIndices!==this._availableIndices.length}canBeginQuery(e){if(this._frameQuerySetIsDirty===this._engine.frameId||this._queryFrameId[e]===this._engine.frameId)return!1;const t=this._engine._getCurrentRenderPassWrapper().renderPassDescriptor.occlusionQuerySet!==void 0;return t&&(this._queryFrameId[e]=this._engine.frameId),t}constructor(e,t,r,n=50,i=100){this._availableIndices=[],this._frameQuerySetIsDirty=-1,this._queryFrameId=[],this._engine=e,this._device=t,this._bufferManager=r,this._frameLastBuffer=-1,this._currentTotalIndices=0,this._countIncrement=i,this._allocateNewIndices(n)}createQuery(){this._availableIndices.length===0&&this._allocateNewIndices();const e=this._availableIndices[this._availableIndices.length-1];return this._availableIndices.length--,e}deleteQuery(e){this._availableIndices[this._availableIndices.length]=e}isQueryResultAvailable(e){return this._retrieveQueryBuffer(),!!this._lastBuffer&&e<this._lastBuffer.length}getQueryResult(e){return Number(this._lastBuffer?.[e]??-1)}_retrieveQueryBuffer(){this._lastBuffer&&this._frameLastBuffer===this._engine.frameId||this._frameLastBuffer!==this._engine.frameId&&(this._frameLastBuffer=this._engine.frameId,this._querySet.readValues(0,this._currentTotalIndices).then(e=>{this._lastBuffer=e}))}_allocateNewIndices(e){e=e??this._countIncrement,this._delayQuerySetDispose();for(let t=0;t<e;++t)this._availableIndices.push(this._currentTotalIndices+t);this._currentTotalIndices+=e,this._querySet=new It(this._engine,this._currentTotalIndices,"occlusion",this._device,this._bufferManager,!1,"QuerySet_OcclusionQuery_count_"+this._currentTotalIndices),this._frameQuerySetIsDirty=this._engine.frameId}_delayQuerySetDispose(){const e=this._querySet;e&&setTimeout(()=>e.dispose,1e3)}dispose(){this._querySet?.dispose(),this._availableIndices.length=0}}class oe{get code(){return this._sourceCode}constructor(e,t=20){this.debug=!1,this._sourceCode=e,this._numMaxIterations=t,this._functionDescr=[],this.inlineToken="#define inline"}processCode(){this.debug&&y.Log(`Start inlining process (code size=${this._sourceCode.length})...`),this._collectFunctions(),this._processInlining(this._numMaxIterations),this.debug&&y.Log("End of inlining process.")}_collectFunctions(){let e=0;for(;e<this._sourceCode.length;){const t=this._sourceCode.indexOf(this.inlineToken,e);if(t<0)break;const r=this._sourceCode.indexOf("(",t+this.inlineToken.length);if(r<0){this.debug&&y.Warn(`Could not find the opening parenthesis after the token. startIndex=${e}`),e=t+this.inlineToken.length;continue}const n=oe._RegexpFindFunctionNameAndType.exec(this._sourceCode.substring(t+this.inlineToken.length,r));if(!n){this.debug&&y.Warn(`Could not extract the name/type of the function from: ${this._sourceCode.substring(t+this.inlineToken.length,r)}`),e=t+this.inlineToken.length;continue}const[i,a]=[n[3],n[4]],o=J("(",")",this._sourceCode,r);if(o<0){this.debug&&y.Warn(`Could not extract the parameters the function '${a}' (type=${i}). funcParamsStartIndex=${r}`),e=t+this.inlineToken.length;continue}const u=this._sourceCode.substring(r+1,o),h=dt(this._sourceCode,o+1);if(h===this._sourceCode.length){this.debug&&y.Warn(`Could not extract the body of the function '${a}' (type=${i}). funcParamsEndIndex=${o}`),e=t+this.inlineToken.length;continue}const c=J("{","}",this._sourceCode,h);if(c<0){this.debug&&y.Warn(`Could not extract the body of the function '${a}' (type=${i}). funcBodyStartIndex=${h}`),e=t+this.inlineToken.length;continue}const l=this._sourceCode.substring(h,c+1),d=le(u).split(","),g=[];for(let p=0;p<d.length;++p){const m=d[p].trim(),x=m.lastIndexOf(" ");x>=0&&g.push(m.substring(x+1))}i!=="void"&&g.push("return"),this._functionDescr.push({name:a,type:i,parameters:g,body:l,callIndex:0}),e=c+1;const f=t>0?this._sourceCode.substring(0,t):"",_=c+1<this._sourceCode.length-1?this._sourceCode.substring(c+1):"";this._sourceCode=f+_,e-=c+1-t}this.debug&&y.Log(`Collect functions: ${this._functionDescr.length} functions found. functionDescr=${this._functionDescr}`)}_processInlining(e=20){for(;e-->=0&&this._replaceFunctionCallsByCode(););return this.debug&&y.Log(`numMaxIterations is ${e} after inlining process`),e>=0}_replaceFunctionCallsByCode(){let e=!1;for(const t of this._functionDescr){const{name:r,type:n,parameters:i,body:a}=t;let o=0;for(;o<this._sourceCode.length;){const u=this._sourceCode.indexOf(r,o);if(u<0)break;if(u===0||ue(this._sourceCode.charAt(u-1))){o=u+r.length;continue}const h=dt(this._sourceCode,u+r.length);if(h===this._sourceCode.length||this._sourceCode.charAt(h)!=="("){o=u+r.length;continue}const c=J("(",")",this._sourceCode,h);if(c<0){this.debug&&y.Warn(`Could not extract the parameters of the function call. Function '${r}' (type=${n}). callParamsStartIndex=${h}`),o=u+r.length;continue}const l=this._sourceCode.substring(h+1,c),g=(b=>{const I=[];let w=0,v=0;for(;w<b.length;){if(b.charAt(w)==="("){const P=J("(",")",b,w);if(P<0)return null;w=P}else b.charAt(w)===","&&(I.push(b.substring(v,w)),v=w+1);w++}return v<w&&I.push(b.substring(v,w)),I})(le(l));if(g===null){this.debug&&y.Warn(`Invalid function call: can't extract the parameters of the function call. Function '${r}' (type=${n}). callParamsStartIndex=${h}, callParams=`+l),o=u+r.length;continue}const f=[];for(let b=0;b<g.length;++b){const I=g[b].trim();f.push(I)}const _=n!=="void"?r+"_"+t.callIndex++:null;if(_&&f.push(_+" ="),f.length!==i.length){this.debug&&y.Warn(`Invalid function call: not the same number of parameters for the call than the number expected by the function. Function '${r}' (type=${n}). function parameters=${i}, call parameters=${f}`),o=u+r.length;continue}o=c+1;const p=this._replaceNames(a,i,f);let m=u>0?this._sourceCode.substring(0,u):"";const x=c+1<this._sourceCode.length-1?this._sourceCode.substring(c+1):"";if(_){const b=Zt(this._sourceCode,u-1,`
`,"{");m=this._sourceCode.substring(0,b+1);const I=this._sourceCode.substring(b+1,u);this._sourceCode=m+n+" "+_+`;
`+p+`
`+I+_+x,this.debug&&y.Log(`Replace function call by code. Function '${r}' (type=${n}). injectDeclarationIndex=${b}, call parameters=${f}`)}else this._sourceCode=m+p+x,o+=p.length-(c+1-u),this.debug&&y.Log(`Replace function call by code. Function '${r}' (type=${n}). functionCallIndex=${u}, call parameters=${f}`);e=!0}}return e}_replaceNames(e,t,r){for(let n=0;n<t.length;++n){const i=new RegExp(er(t[n]),"g"),a=t[n].length,o=r[n];e=e.replace(i,(u,...h)=>{const c=h[0];return ue(e.charAt(c-1))||ue(e.charAt(c+a))?t[n]:o})}return e}}oe._RegexpFindFunctionNameAndType=/((\s+?)(\w+)\s+(\w+)\s*?)$/;class G{async initTwgsl(e){if(!G._Twgsl)return e=e||{},e={...G._TWgslDefaultOptions,...e},e.twgsl?(G._Twgsl=e.twgsl,Promise.resolve()):(e.jsPath&&e.wasmPath&&await q.LoadBabylonScriptAsync(e.jsPath),self.twgsl?(G._Twgsl=await self.twgsl(q.GetBabylonScriptURL(e.wasmPath)),Promise.resolve()):Promise.reject("twgsl is not available."))}convertSpirV2WGSL(e,t=!1){const r=G._Twgsl.convertSpirV2WGSL(e,G.DisableUniformityAnalysis||t);return G.ShowWGSLShaderCode&&(y.Log(r),y.Log("***********************************************")),G.DisableUniformityAnalysis||t?`diagnostic(off, derivative_uniformity);
`+r:r}}G._TWgslDefaultOptions={jsPath:`${q._DefaultCdnUrl}/twgsl/twgsl.js`,wasmPath:`${q._DefaultCdnUrl}/twgsl/twgsl.wasm`};G.ShowWGSLShaderCode=!1;G.DisableUniformityAnalysis=!1;G._Twgsl=null;class Mr{constructor(e,t,r){this._record=!1,this._play=!1,this._playBundleListIndex=0,this._allBundleLists=[],this._enabled=!1,this.showDebugLogs=!1,this._engine=e,this._mode=t,this._bundleList=r}get enabled(){return this._enabled}get play(){return this._play}get record(){return this._record}set enabled(e){this._log("enabled",`activate=${e}, mode=${this._mode}`),this._allBundleLists.length=0,this._record=this._enabled=e,this._play=!1,e&&(this._modeSaved=this._mode,this._mode=0)}get mode(){return this._mode}set mode(e){this._record?this._modeSaved=e:this._mode=e}endRenderPass(e){if(!this._record&&!this._play)return!1;let t=null;return this._record?(t=this._bundleList.clone(),this._allBundleLists.push(t),this._bundleList.reset(),this._log("endRenderPass",`bundleList recorded at position #${this._allBundleLists.length-1}`)):this._playBundleListIndex>=this._allBundleLists.length?this._log("endRenderPass",`empty or out-of-sync bundleList (_allBundleLists.length=${this._allBundleLists.length}, playBundleListIndex=${this._playBundleListIndex})`):(this._log("endRenderPass",`run bundleList #${this._playBundleListIndex}`),t=this._allBundleLists[this._playBundleListIndex++]),t&&(t.run(e),this._mode===1&&this._engine._reportDrawCall(t.numDrawCalls)),!0}endFrame(){this._record&&(this._record=!1,this._play=!0,this._mode=this._modeSaved,this._log("endFrame","bundles recorded, switching to play mode")),this._playBundleListIndex=0}reset(){this._log("reset","called"),this._record&&(this._mode=this._modeSaved),this.enabled=!1,this.enabled=!0}_log(e,t){this.showDebugLogs&&y.Log(`[Frame: ${this._engine.frameId}] WebGPUSnapshotRendering:${e} - ${t}`)}}const j=(()=>{const s=new Uint8Array(4),e=new Uint32Array(s.buffer);return!!((e[0]=1)&s[0])})();Object.defineProperty(S.prototype,"effectiveByteStride",{get:function(){return this._alignedBuffer&&this._alignedBuffer.byteStride||this.byteStride},enumerable:!0,configurable:!0});Object.defineProperty(S.prototype,"effectiveByteOffset",{get:function(){return this._alignedBuffer?0:this.byteOffset},enumerable:!0,configurable:!0});Object.defineProperty(S.prototype,"effectiveBuffer",{get:function(){return this._alignedBuffer&&this._alignedBuffer.getBuffer()||this._buffer.getBuffer()},enumerable:!0,configurable:!0});S.prototype._rebuild=function(){this._buffer?._rebuild(),this._alignedBuffer?._rebuild()};S.prototype.dispose=function(){this._ownsBuffer&&this._buffer.dispose(),this._alignedBuffer?.dispose(),this._alignedBuffer=void 0,this._isDisposed=!0};S.prototype.getWrapperBuffer=function(){return this._alignedBuffer||this._buffer};S.prototype._alignBuffer=function(){const s=this._buffer.getData();if(!this.engine._features.forceVertexBufferStrideAndOffsetMultiple4Bytes||this.byteStride%4===0&&this.byteOffset%4===0||!s)return;const e=S.GetTypeByteLength(this.type),t=this.byteStride+3&-4,r=t/e,n=this._maxVerticesCount,a=n*t/e;let o;if(Array.isArray(s)){const l=new Float32Array(s);o=new DataView(l.buffer,l.byteOffset,l.byteLength)}else s instanceof ArrayBuffer?o=new DataView(s,0,s.byteLength):o=new DataView(s.buffer,s.byteOffset,s.byteLength);let u;this.type===S.BYTE?u=new Int8Array(a):this.type===S.UNSIGNED_BYTE?u=new Uint8Array(a):this.type===S.SHORT?u=new Int16Array(a):this.type===S.UNSIGNED_SHORT?u=new Uint16Array(a):this.type===S.INT?u=new Int32Array(a):this.type===S.UNSIGNED_INT?u=new Uint32Array(a):u=new Float32Array(a);const h=this.getSize();let c=this.byteOffset;for(let l=0;l<n;++l){for(let d=0;d<h;++d)switch(this.type){case S.BYTE:u[l*r+d]=o.getInt8(c+d);break;case S.UNSIGNED_BYTE:u[l*r+d]=o.getUint8(c+d);break;case S.SHORT:u[l*r+d]=o.getInt16(c+d*2,j);break;case S.UNSIGNED_SHORT:u[l*r+d]=o.getUint16(c+d*2,j);break;case S.INT:u[l*r+d]=o.getInt32(c+d*4,j);break;case S.UNSIGNED_INT:u[l*r+d]=o.getUint32(c+d*4,j);break;case S.FLOAT:u[l*r+d]=o.getFloat32(c+d*4,j);break}c+=this.byteStride}this._alignedBuffer?.dispose(),this._alignedBuffer=new Ft(this.engine,u,!1,t,!1,this.getIsInstanced(),!0,this.instanceDivisor,(this._label??"VertexBuffer")+"_aligned")};class Er extends wt{constructor(e){super(e)}}A.prototype.setAlphaMode=function(s,e=!1){if(this._alphaMode===s&&(s===0&&!this._alphaState.alphaBlend||s!==0&&this._alphaState.alphaBlend)){if(!e){const t=s===0;this.depthCullingState.depthMask!==t&&(this.setDepthWrite(t),this._cacheRenderPipeline.setDepthWriteEnabled(t))}return}switch(s){case 0:this._alphaState.alphaBlend=!1;break;case 7:this._alphaState.setAlphaBlendFunctionParameters(1,771,1,1),this._alphaState.alphaBlend=!0;break;case 8:this._alphaState.setAlphaBlendFunctionParameters(1,771,1,771),this._alphaState.alphaBlend=!0;break;case 2:this._alphaState.setAlphaBlendFunctionParameters(770,771,1,1),this._alphaState.alphaBlend=!0;break;case 6:this._alphaState.setAlphaBlendFunctionParameters(1,1,0,1),this._alphaState.alphaBlend=!0;break;case 1:this._alphaState.setAlphaBlendFunctionParameters(770,1,0,1),this._alphaState.alphaBlend=!0;break;case 3:this._alphaState.setAlphaBlendFunctionParameters(0,769,1,1),this._alphaState.alphaBlend=!0;break;case 4:this._alphaState.setAlphaBlendFunctionParameters(774,0,1,1),this._alphaState.alphaBlend=!0;break;case 5:this._alphaState.setAlphaBlendFunctionParameters(770,769,1,1),this._alphaState.alphaBlend=!0;break;case 9:this._alphaState.setAlphaBlendFunctionParameters(32769,32770,32771,32772),this._alphaState.alphaBlend=!0;break;case 10:this._alphaState.setAlphaBlendFunctionParameters(1,769,1,771),this._alphaState.alphaBlend=!0;break;case 11:this._alphaState.setAlphaBlendFunctionParameters(1,1,1,1),this._alphaState.alphaBlend=!0;break;case 12:this._alphaState.setAlphaBlendFunctionParameters(772,1,0,0),this._alphaState.alphaBlend=!0;break;case 13:this._alphaState.setAlphaBlendFunctionParameters(775,769,773,771),this._alphaState.alphaBlend=!0;break;case 14:this._alphaState.setAlphaBlendFunctionParameters(1,771,1,771),this._alphaState.alphaBlend=!0;break;case 15:this._alphaState.setAlphaBlendFunctionParameters(1,1,1,0),this._alphaState.alphaBlend=!0;break;case 16:this._alphaState.setAlphaBlendFunctionParameters(775,769,0,1),this._alphaState.alphaBlend=!0;break;case 17:this._alphaState.setAlphaBlendFunctionParameters(770,771,1,771),this._alphaState.alphaBlend=!0;break}e||(this.setDepthWrite(s===0),this._cacheRenderPipeline.setDepthWriteEnabled(s===0)),this._alphaMode=s,this._cacheRenderPipeline.setAlphaBlendEnabled(this._alphaState.alphaBlend),this._cacheRenderPipeline.setAlphaBlendFactors(this._alphaState._blendFunctionParameters,this._alphaState._blendEquationParameters)};A.prototype.setAlphaEquation=function(s){k.prototype.setAlphaEquation.call(this,s),this._cacheRenderPipeline.setAlphaBlendFactors(this._alphaState._blendFunctionParameters,this._alphaState._blendEquationParameters)};A.prototype.createRawTexture=function(s,e,t,r,n,i,a,o=null,u=0,h=0,c=!1){const l=new W(this,3);return l.baseWidth=e,l.baseHeight=t,l.width=e,l.height=t,l.format=r,l.generateMipMaps=n,l.samplingMode=a,l.invertY=i,l._compression=o,l.type=u,l._creationFlags=h,l._useSRGBBuffer=c,this._doNotHandleContextLost||(l._bufferView=s),this._textureHelper.createGPUTextureForInternalTexture(l,e,t,void 0,h),this.updateRawTexture(l,s,r,i,o,u,c),this._internalTexturesCache.push(l),l};A.prototype.updateRawTexture=function(s,e,t,r,n=null,i=0,a=!1){if(s){if(this._doNotHandleContextLost||(s._bufferView=e,s.invertY=r,s._compression=n,s._useSRGBBuffer=a),e){const o=s._hardwareTexture;t===4&&(e=X(e,s.width,s.height,i));const h=new Uint8Array(e.buffer,e.byteOffset,e.byteLength);this._textureHelper.updateTexture(h,s,s.width,s.height,s.depth,o.format,0,0,r,!1,0,0),s.generateMipMaps&&this._generateMipmaps(s,this._uploadEncoder)}s.isReady=!0}};A.prototype.createRawCubeTexture=function(s,e,t,r,n,i,a,o=null){const u=new W(this,8);if(r===1&&!this._caps.textureFloatLinearFiltering?(n=!1,a=1,y.Warn("Float texture filtering is not supported. Mipmap generation and sampling mode are forced to false and TEXTURE_NEAREST_SAMPLINGMODE, respectively.")):r===2&&!this._caps.textureHalfFloatLinearFiltering?(n=!1,a=1,y.Warn("Half float texture filtering is not supported. Mipmap generation and sampling mode are forced to false and TEXTURE_NEAREST_SAMPLINGMODE, respectively.")):r===1&&!this._caps.textureFloatRender?(n=!1,y.Warn("Render to float textures is not supported. Mipmap generation forced to false.")):r===2&&!this._caps.colorBufferFloat&&(n=!1,y.Warn("Render to half float textures is not supported. Mipmap generation forced to false.")),u.isCube=!0,u._originalFormat=t,u.format=t===4?5:t,u.type=r,u.generateMipMaps=n,u.width=e,u.height=e,u.samplingMode=a,this._doNotHandleContextLost||(u._bufferViewArray=s),u.invertY=i,u._compression=o,u._cachedWrapU=0,u._cachedWrapV=0,this._textureHelper.createGPUTextureForInternalTexture(u),t===4){const h=u._hardwareTexture;h._originalFormatIsRGB=!0}return s&&this.updateRawCubeTexture(u,s,t,r,i,o),u.isReady=!0,u};A.prototype.updateRawCubeTexture=function(s,e,t,r,n,i=null){s._bufferViewArray=e,s.invertY=n,s._compression=i;const a=s._hardwareTexture,o=a._originalFormatIsRGB,u=[0,2,4,1,3,5],h=[];for(let c=0;c<e.length;++c){let l=e[u[c]];o&&(l=X(l,s.width,s.height,r)),h.push(new Uint8Array(l.buffer,l.byteOffset,l.byteLength))}this._textureHelper.updateCubeTextures(h,a.underlyingResource,s.width,s.height,a.format,n,!1,0,0),s.generateMipMaps&&this._generateMipmaps(s,this._uploadEncoder),s.isReady=!0};A.prototype.createRawCubeTextureFromUrl=function(s,e,t,r,n,i,a,o,u=null,h=null,c=3,l=!1){const d=this.createRawCubeTexture(null,t,r,n,!i,l,c,null);e?.addPendingData(d),d.url=s,d.isReady=!1,this._internalTexturesCache.push(d);const g=(_,p)=>{e?.removePendingData(d),h&&_&&h(_.status+" "+_.statusText,p)},f=_=>{const p=d.width,m=a(_);if(m){if(o){const x=r===4,b=o(m),I=d._hardwareTexture,w=[0,1,2,3,4,5];for(let v=0;v<b.length;v++){const P=p>>v,R=[];for(let M=0;M<6;M++){let E=b[v][w[M]];x&&(E=X(E,P,P,n)),R.push(new Uint8Array(E.buffer,E.byteOffset,E.byteLength))}this._textureHelper.updateCubeTextures(R,I.underlyingResource,P,P,I.format,l,!1,0,0)}}else this.updateRawCubeTexture(d,m,r,n,l);d.isReady=!0,e?.removePendingData(d),u&&u()}};return this._loadFile(s,_=>{f(_)},void 0,e?.offlineProvider,!0,g),d};A.prototype.createRawTexture3D=function(s,e,t,r,n,i,a,o,u=null,h=0,c=0){const d=new W(this,10);return d.baseWidth=e,d.baseHeight=t,d.baseDepth=r,d.width=e,d.height=t,d.depth=r,d.format=n,d.type=h,d.generateMipMaps=i,d.samplingMode=o,d.is3D=!0,d._creationFlags=c,this._doNotHandleContextLost||(d._bufferView=s),this._textureHelper.createGPUTextureForInternalTexture(d,e,t,void 0,c),this.updateRawTexture3D(d,s,n,a,u,h),this._internalTexturesCache.push(d),d};A.prototype.updateRawTexture3D=function(s,e,t,r,n=null,i=0){if(this._doNotHandleContextLost||(s._bufferView=e,s.format=t,s.invertY=r,s._compression=n),e){const a=s._hardwareTexture;t===4&&(e=X(e,s.width,s.height,i));const u=new Uint8Array(e.buffer,e.byteOffset,e.byteLength);this._textureHelper.updateTexture(u,s,s.width,s.height,s.depth,a.format,0,0,r,!1,0,0),s.generateMipMaps&&this._generateMipmaps(s,this._uploadEncoder)}s.isReady=!0};A.prototype.createRawTexture2DArray=function(s,e,t,r,n,i,a,o,u=null,h=0,c=0){const d=new W(this,11);return d.baseWidth=e,d.baseHeight=t,d.baseDepth=r,d.width=e,d.height=t,d.depth=r,d.format=n,d.type=h,d.generateMipMaps=i,d.samplingMode=o,d.is2DArray=!0,d._creationFlags=c,this._doNotHandleContextLost||(d._bufferView=s),this._textureHelper.createGPUTextureForInternalTexture(d,e,t,r,c),this.updateRawTexture2DArray(d,s,n,a,u,h),this._internalTexturesCache.push(d),d};A.prototype.updateRawTexture2DArray=function(s,e,t,r,n=null,i=0){if(this._doNotHandleContextLost||(s._bufferView=e,s.format=t,s.invertY=r,s._compression=n),e){const a=s._hardwareTexture;t===4&&(e=X(e,s.width,s.height,i));const u=new Uint8Array(e.buffer,e.byteOffset,e.byteLength);this._textureHelper.updateTexture(u,s,s.width,s.height,s.depth,a.format,0,0,r,!1,0,0),s.generateMipMaps&&this._generateMipmaps(s,this._uploadEncoder)}s.isReady=!0};function X(s,e,t,r){let n,i=1;r===1?n=new Float32Array(e*t*4):r===2?(n=new Uint16Array(e*t*4),i=15360):r===7?n=new Uint32Array(e*t*4):n=new Uint8Array(e*t*4);for(let a=0;a<e;a++)for(let o=0;o<t;o++){const u=(o*e+a)*3,h=(o*e+a)*4;n[h+0]=s[u+0],n[h+1]=s[u+1],n[h+2]=s[u+2],n[h+3]=i}return n}A.prototype._readTexturePixels=function(s,e,t,r=-1,n=0,i=null,a=!0,o=!1,u=0,h=0){const c=s._hardwareTexture;return a&&this.flushFramebuffer(),this._textureHelper.readPixels(c.underlyingResource,u,h,e,t,c.format,r,n,i,o)};A.prototype._readTexturePixelsSync=function(){throw"_readTexturePixelsSync is unsupported in WebGPU!"};A.prototype._createDepthStencilCubeTexture=function(s,e){const t=new W(this,e.generateStencil?12:14);t.isCube=!0,t.label=e.label;const r={bilinearFiltering:!1,comparisonFunction:0,generateStencil:!1,samples:1,depthTextureFormat:e.generateStencil?13:14,...e};t.format=r.depthTextureFormat,this._setupDepthStencilTexture(t,s,r.bilinearFiltering,r.comparisonFunction,r.samples),this._textureHelper.createGPUTextureForInternalTexture(t);const n=t._hardwareTexture;return t.type=C.GetTextureTypeFromFormat(n.format),this._internalTexturesCache.push(t),t};A.prototype.createCubeTexture=function(s,e,t,r,n=null,i=null,a,o=null,u=!1,h=0,c=0,l=null,d,g=!1,f=null){return this.createCubeTextureBase(s,e,t,!!r,n,i,a,o,u,h,c,l,null,(_,p)=>{const m=p,x=m[0].width,b=x;this._setCubeMapTextureParams(_,!r),_.format=a??-1;const I=this._textureHelper.createGPUTextureForInternalTexture(_,x,b);this._textureHelper.updateCubeTextures(m,I.underlyingResource,x,b,I.format,!1,!1,0,0),r||this._generateMipmaps(_,this._uploadEncoder),_.isReady=!0,_.onLoadedObservable.notifyObservers(_),_.onLoadedObservable.clear(),n&&n()},!!g,f)};A.prototype._setCubeMapTextureParams=function(s,e,t){s.samplingMode=e?3:2,s._cachedWrapU=0,s._cachedWrapV=0,t&&(s._maxLodLevel=t)};A.prototype.generateMipMapsForCubemap=function(s){s.generateMipMaps&&(s._hardwareTexture?.underlyingResource||this._textureHelper.createGPUTextureForInternalTexture(s),this._generateMipmaps(s))};class Gr extends Lt{constructor(e,t,r,n,i){super(e,t,r,n,i),n.enableGPUTimingMeasurements&&(this.gpuTimeInFrame=new yt)}}A.prototype._createHardwareRenderTargetWrapper=function(s,e,t){const r=new Gr(s,e,t,this);return this._renderTargetWrapperCache.push(r),r};A.prototype.createRenderTargetTexture=function(s,e){const t=this._createHardwareRenderTargetWrapper(!1,!1,s),r={};e!==void 0&&typeof e=="object"?(r.generateMipMaps=e.generateMipMaps,r.generateDepthBuffer=e.generateDepthBuffer===void 0?!0:e.generateDepthBuffer,r.generateStencilBuffer=r.generateDepthBuffer&&e.generateStencilBuffer,r.samplingMode=e.samplingMode===void 0?3:e.samplingMode,r.creationFlags=e.creationFlags??0,r.noColorAttachment=!!e.noColorAttachment,r.colorAttachment=e.colorAttachment,r.samples=e.samples,r.label=e.label,r.format=e.format,r.type=e.type):(r.generateMipMaps=e,r.generateDepthBuffer=!0,r.generateStencilBuffer=!1,r.samplingMode=3,r.creationFlags=0,r.noColorAttachment=!1);const n=r.colorAttachment||(r.noColorAttachment?null:this._createInternalTexture(s,r,!0,5));return t.label=r.label??"RenderTargetWrapper",t._samples=r.colorAttachment?.samples??r.samples??1,t._generateDepthBuffer=r.generateDepthBuffer,t._generateStencilBuffer=!!r.generateStencilBuffer,t.setTextures(n),(t._generateDepthBuffer||t._generateStencilBuffer)&&t.createDepthStencilTexture(0,!1,t._generateStencilBuffer,t.samples,r.generateStencilBuffer?13:14,r.label?r.label+"-DepthStencil":void 0),n&&!r.colorAttachment&&(e!==void 0&&typeof e=="object"&&e.createMipMaps&&!r.generateMipMaps&&(n.generateMipMaps=!0),this._textureHelper.createGPUTextureForInternalTexture(n,void 0,void 0,void 0,r.creationFlags),e!==void 0&&typeof e=="object"&&e.createMipMaps&&!r.generateMipMaps&&(n.generateMipMaps=!1)),t};A.prototype._createDepthStencilTexture=function(s,e,t){const r={bilinearFiltering:!1,comparisonFunction:0,generateStencil:!1,samples:1,depthTextureFormat:e.generateStencil?13:14,...e},n=Et(r.depthTextureFormat);t._depthStencilTextureWithStencil=n;const i=new W(this,n?12:14);return i.label=e.label,i.format=r.depthTextureFormat,i.type=Mt(i.format),this._setupDepthStencilTexture(i,s,r.bilinearFiltering,r.comparisonFunction,r.samples),this._textureHelper.createGPUTextureForInternalTexture(i),this._internalTexturesCache.push(i),i};A.prototype._setupDepthStencilTexture=function(s,e,t,r,n=1){const i=e.width??e,a=e.height??e,o=e.layers||0,u=e.depth||0;s.baseWidth=i,s.baseHeight=a,s.width=i,s.height=a,s.is2DArray=o>0,s.is3D=u>0,s.depth=o||u,s.isReady=!0,s.samples=n,s.generateMipMaps=!1,s.samplingMode=t?2:1,s.type=1,s._comparisonFunction=r,s._cachedWrapU=0,s._cachedWrapV=0};A.prototype.updateRenderTargetTextureSampleCount=function(s,e){return!s||!s.texture||s.samples===e||(e=Math.min(e,this.getCaps().maxMSAASamples),this._textureHelper.createMSAATexture(s.texture,e),s._depthStencilTexture&&(this._textureHelper.createMSAATexture(s._depthStencilTexture,e),s._depthStencilTexture.samples=e),s._samples=e,s.texture.samples=e),e};A.prototype.setDepthStencilTexture=function(s,e,t,r){!t||!t.depthStencilTexture?this._setTexture(s,null,void 0,void 0,r):this._setTexture(s,t,!1,!0,r)};A.prototype.createRenderTargetCubeTexture=function(s,e){const t=this._createHardwareRenderTargetWrapper(!1,!0,s),r={generateMipMaps:!0,generateDepthBuffer:!0,generateStencilBuffer:!1,type:0,samplingMode:3,format:5,samples:1,...e};r.generateStencilBuffer=r.generateDepthBuffer&&r.generateStencilBuffer,t.label=r.label??"RenderTargetWrapper",t._generateDepthBuffer=r.generateDepthBuffer,t._generateStencilBuffer=r.generateStencilBuffer;const n=new W(this,5);return n.width=s,n.height=s,n.depth=0,n.isReady=!0,n.isCube=!0,n.samples=r.samples,n.generateMipMaps=r.generateMipMaps,n.samplingMode=r.samplingMode,n.type=r.type,n.format=r.format,this._internalTexturesCache.push(n),t.setTextures(n),(t._generateDepthBuffer||t._generateStencilBuffer)&&t.createDepthStencilTexture(0,r.samplingMode===void 0||r.samplingMode===2||r.samplingMode===2||r.samplingMode===3||r.samplingMode===3||r.samplingMode===5||r.samplingMode===6||r.samplingMode===7||r.samplingMode===11,t._generateStencilBuffer,t.samples),e&&e.createMipMaps&&!r.generateMipMaps&&(n.generateMipMaps=!0),this._textureHelper.createGPUTextureForInternalTexture(n),e&&e.createMipMaps&&!r.generateMipMaps&&(n.generateMipMaps=!1),t};class Or{constructor(){this.occlusionInternalRetryCounter=0,this.isOcclusionQueryInProgress=!1,this.isOccluded=!1,this.occlusionRetryCount=-1,this.occlusionType=U.OCCLUSION_TYPE_NONE,this.occlusionQueryAlgorithmType=U.OCCLUSION_ALGORITHM_TYPE_CONSERVATIVE,this.forceRenderingWhenOccluded=!1}}k.prototype.createQuery=function(){return null};k.prototype.deleteQuery=function(s){return this};k.prototype.isQueryResultAvailable=function(s){return!1};k.prototype.getQueryResult=function(s){return 0};k.prototype.beginOcclusionQuery=function(s,e){return!1};k.prototype.endOcclusionQuery=function(s){return this};Object.defineProperty(U.prototype,"isOcclusionQueryInProgress",{get:function(){return this._occlusionDataStorage.isOcclusionQueryInProgress},set:function(s){this._occlusionDataStorage.isOcclusionQueryInProgress=s},enumerable:!1,configurable:!0});Object.defineProperty(U.prototype,"_occlusionDataStorage",{get:function(){return this.__occlusionDataStorage||(this.__occlusionDataStorage=new Or),this.__occlusionDataStorage},enumerable:!1,configurable:!0});Object.defineProperty(U.prototype,"isOccluded",{get:function(){return this._occlusionDataStorage.isOccluded},set:function(s){this._occlusionDataStorage.isOccluded=s},enumerable:!0,configurable:!0});Object.defineProperty(U.prototype,"occlusionQueryAlgorithmType",{get:function(){return this._occlusionDataStorage.occlusionQueryAlgorithmType},set:function(s){this._occlusionDataStorage.occlusionQueryAlgorithmType=s},enumerable:!0,configurable:!0});Object.defineProperty(U.prototype,"occlusionType",{get:function(){return this._occlusionDataStorage.occlusionType},set:function(s){this._occlusionDataStorage.occlusionType=s},enumerable:!0,configurable:!0});Object.defineProperty(U.prototype,"occlusionRetryCount",{get:function(){return this._occlusionDataStorage.occlusionRetryCount},set:function(s){this._occlusionDataStorage.occlusionRetryCount=s},enumerable:!0,configurable:!0});Object.defineProperty(U.prototype,"forceRenderingWhenOccluded",{get:function(){return this._occlusionDataStorage.forceRenderingWhenOccluded},set:function(s){this._occlusionDataStorage.forceRenderingWhenOccluded=s},enumerable:!0,configurable:!0});U.prototype._checkOcclusionQuery=function(){const s=this._occlusionDataStorage;if(s.occlusionType===U.OCCLUSION_TYPE_NONE)return s.isOccluded=!1,!1;const e=this.getEngine();if(!e.getCaps().supportOcclusionQuery||!e.isQueryResultAvailable)return s.isOccluded=!1,!1;if(this.isOcclusionQueryInProgress&&this._occlusionQuery!==null&&this._occlusionQuery!==void 0)if(e.isQueryResultAvailable(this._occlusionQuery)){const n=e.getQueryResult(this._occlusionQuery);s.isOcclusionQueryInProgress=!1,s.occlusionInternalRetryCounter=0,s.isOccluded=!(n>0)}else if(s.occlusionInternalRetryCounter++,s.occlusionRetryCount!==-1&&s.occlusionInternalRetryCounter>s.occlusionRetryCount)s.isOcclusionQueryInProgress=!1,s.occlusionInternalRetryCounter=0,s.isOccluded=s.occlusionType===U.OCCLUSION_TYPE_OPTIMISTIC?!1:s.isOccluded;else return s.occlusionType===U.OCCLUSION_TYPE_OPTIMISTIC?!1:s.isOccluded;const t=this.getScene();if(t.getBoundingBoxRenderer){const r=t.getBoundingBoxRenderer();this._occlusionQuery===null&&(this._occlusionQuery=e.createQuery()),this._occlusionQuery&&e.beginOcclusionQuery(s.occlusionQueryAlgorithmType,this._occlusionQuery)&&(r.renderOcclusionBoundingBox(this),e.endOcclusionQuery(s.occlusionQueryAlgorithmType),this._occlusionDataStorage.isOcclusionQueryInProgress=!0)}return s.isOccluded};A.prototype.getGPUFrameTimeCounter=function(){return this._timestampQuery.gpuFrameTimeCounter};A.prototype.captureGPUFrameTime=function(s){this._timestampQuery.enable=s&&!!this._caps.timerQuery};A.prototype.createQuery=function(){return this._occlusionQuery.createQuery()};A.prototype.deleteQuery=function(s){return this._occlusionQuery.deleteQuery(s),this};A.prototype.isQueryResultAvailable=function(s){return this._occlusionQuery.isQueryResultAvailable(s)};A.prototype.getQueryResult=function(s){return this._occlusionQuery.getQueryResult(s)};A.prototype.beginOcclusionQuery=function(s,e){if(this.compatibilityMode){if(this._occlusionQuery.canBeginQuery(e))return this._currentRenderPass?.beginOcclusionQuery(e),!0}else return this._bundleList.addItem(new me(e)),!0;return!1};A.prototype.endOcclusionQuery=function(){return this.compatibilityMode?this._currentRenderPass?.endOcclusionQuery():this._bundleList.addItem(new be),this};const mt={label:"TextureView_SwapChain_ResolveTarget",dimension:"2d",format:void 0,mipLevelCount:1,arrayLayerCount:1},bt={label:"TextureView_SwapChain",dimension:"2d",format:void 0,mipLevelCount:1,arrayLayerCount:1},Ur=new ce;class z extends A{get snapshotRenderingMode(){return this._snapshotRendering.mode}set snapshotRenderingMode(e){this._snapshotRendering.mode=e}snapshotRenderingReset(){this._snapshotRendering.reset()}get snapshotRendering(){return this._snapshotRendering.enabled}set snapshotRendering(e){this._snapshotRendering.enabled=e}get disableCacheSamplers(){return this._cacheSampler?this._cacheSampler.disabled:!1}set disableCacheSamplers(e){this._cacheSampler&&(this._cacheSampler.disabled=e)}get disableCacheRenderPipelines(){return this._cacheRenderPipeline?this._cacheRenderPipeline.disabled:!1}set disableCacheRenderPipelines(e){this._cacheRenderPipeline&&(this._cacheRenderPipeline.disabled=e)}get disableCacheBindGroups(){return this._cacheBindGroups?this._cacheBindGroups.disabled:!1}set disableCacheBindGroups(e){this._cacheBindGroups&&(this._cacheBindGroups.disabled=e)}areAllEffectsReady(){return!0}getFontOffset(e){return Gt(e)}static get IsSupportedAsync(){return navigator.gpu?navigator.gpu.requestAdapter().then(e=>!!e,()=>!1).catch(()=>!1):Promise.resolve(!1)}static get IsSupported(){return y.Warn("You must call IsSupportedAsync for WebGPU!"),!1}get supportsUniformBuffers(){return!0}get supportedExtensions(){return this._adapterSupportedExtensions}get enabledExtensions(){return this._deviceEnabledExtensions}get supportedLimits(){return this._adapterSupportedLimits}get currentLimits(){return this._deviceLimits}get description(){return this.name+this.version}get version(){return 1}getInfo(){return{vendor:this._adapterInfo.vendor||"unknown vendor",renderer:this._adapterInfo.architecture||"unknown renderer",version:this._adapterInfo.description||"unknown version"}}get compatibilityMode(){return this._compatibilityMode}set compatibilityMode(e){this._compatibilityMode=e}get currentSampleCount(){return this._currentRenderTarget?this._currentRenderTarget.samples:this._mainPassSampleCount}static CreateAsync(e,t={}){const r=new z(e,t);return new Promise(n=>{r.initAsync(t.glslangOptions,t.twgslOptions).then(()=>n(r))})}constructor(e,t={}){if(super(t.antialias??!0,t),this.uniqueId=-1,this._uploadEncoderDescriptor={label:"upload"},this._renderEncoderDescriptor={label:"render"},this._clearDepthValue=1,this._clearReverseDepthValue=0,this._clearStencilValue=0,this._defaultSampleCount=4,this._glslang=null,this._tintWASM=null,this._glslangAndTintAreFullyLoaded=!1,this._adapterInfo={vendor:"",architecture:"",device:"",description:""},this._compiledComputeEffects={},this._counters={numEnableEffects:0,numEnableDrawWrapper:0,numBundleCreationNonCompatMode:0,numBundleReuseNonCompatMode:0},this.countersLastFrame={numEnableEffects:0,numEnableDrawWrapper:0,numBundleCreationNonCompatMode:0,numBundleReuseNonCompatMode:0},this.numMaxUncapturedErrors=20,this.scenes=[],this._virtualScenes=new Array,this._commandBuffers=[null,null],this._mainRenderPassWrapper={renderPassDescriptor:null,colorAttachmentViewDescriptor:null,depthAttachmentViewDescriptor:null,colorAttachmentGPUTextures:[],depthTextureFormat:void 0},this._rttRenderPassWrapper={renderPassDescriptor:null,colorAttachmentViewDescriptor:null,depthAttachmentViewDescriptor:null,colorAttachmentGPUTextures:[],depthTextureFormat:void 0},this._pendingDebugCommands=[],this._currentOverrideVertexBuffers=null,this._currentIndexBuffer=null,this._colorWriteLocal=!0,this._forceEnableEffect=!1,this.isNDCHalfZRange=!0,this.hasOriginBottomLeft=!1,this._workingGlslangAndTintPromise=null,this._viewportsCurrent={x:0,y:0,w:0,h:0},this._scissorsCurrent={x:0,y:0,w:0,h:0},this._scissorCached={x:0,y:0,z:0,w:0},this._stencilRefsCurrent=-1,this._blendColorsCurrent=[null,null,null,null],this._performanceMonitor=new Ot,this._name="WebGPU",this._drawCalls=new pe,t.deviceDescriptor=t.deviceDescriptor||{},t.enableGPUDebugMarkers=t.enableGPUDebugMarkers??!1,y.Log(`Babylon.js v${k.Version} - ${this.description} engine`),!navigator.gpu){y.Error("WebGPU is not supported by your browser.");return}t.swapChainFormat=t.swapChainFormat||navigator.gpu.getPreferredCanvasFormat(),this._isWebGPU=!0,this._shaderPlatformName="WEBGPU",this._renderingCanvas=e,this._options=t,this._mainPassSampleCount=t.antialias?this._defaultSampleCount:1,navigator&&navigator.userAgent&&this._setupMobileChecks(),this._sharedInit(this._renderingCanvas),this._shaderProcessor=new tr,this._shaderProcessorWGSL=new ir}prepareGlslangAndTintAsync(){return this._workingGlslangAndTintPromise||(this._workingGlslangAndTintPromise=new Promise(e=>{this._initGlslang(this._glslangOptions??this._options?.glslangOptions).then(t=>{this._glslang=t,this._tintWASM=new G,this._tintWASM.initTwgsl(this._twgslOptions??this._options?.twgslOptions).then(()=>{this._glslangAndTintAreFullyLoaded=!0,e()})})})),this._workingGlslangAndTintPromise}initAsync(e,t){return this.uniqueId=z._InstanceId++,this._glslangOptions=e,this._twgslOptions=t,navigator.gpu.requestAdapter(this._options).then(r=>{if(r){this._adapter=r,this._adapterSupportedExtensions=[],this._adapter.features?.forEach(a=>this._adapterSupportedExtensions.push(a)),this._adapterSupportedLimits=this._adapter.limits,this._adapterInfo=this._adapter.info;const n=this._options.deviceDescriptor??{},i=n?.requiredFeatures??(this._options.enableAllFeatures?this._adapterSupportedExtensions:void 0);if(i){const a=i,o=[];for(const u of a)this._adapterSupportedExtensions.indexOf(u)!==-1&&o.push(u);n.requiredFeatures=o}if(this._options.setMaximumLimits&&!n.requiredLimits){n.requiredLimits={};for(const a in this._adapterSupportedLimits)a==="minSubgroupSize"||a==="maxSubgroupSize"||(n.requiredLimits[a]=this._adapterSupportedLimits[a])}return n.label=`BabylonWebGPUDevice${this.uniqueId}`,this._adapter.requestDevice(n)}else throw"Could not retrieve a WebGPU adapter (adapter is null)."}).then(r=>{this._device=r,this._deviceEnabledExtensions=[],this._device.features?.forEach(i=>this._deviceEnabledExtensions.push(i)),this._deviceLimits=r.limits;let n=-1;this._device.addEventListener("uncapturederror",i=>{++n<this.numMaxUncapturedErrors?y.Warn(`WebGPU uncaptured error (${n+1}): ${i.error} - ${i.error.message}`):n++===this.numMaxUncapturedErrors&&y.Warn(`WebGPU uncaptured error: too many warnings (${this.numMaxUncapturedErrors}), no more warnings will be reported to the console for this engine.`)}),this._doNotHandleContextLost||this._device.lost?.then(i=>{this._isDisposed||(this._contextWasLost=!0,y.Warn("WebGPU context lost. "+i),this.onContextLostObservable.notifyObservers(this),this._restoreEngineAfterContextLost(async()=>{const a=this.snapshotRenderingMode,o=this.snapshotRendering,u=this.disableCacheSamplers,h=this.disableCacheRenderPipelines,c=this.disableCacheBindGroups,l=this.enableGPUTimingMeasurements;await this.initAsync(this._glslangOptions??this._options?.glslangOptions,this._twgslOptions??this._options?.twgslOptions),this.snapshotRenderingMode=a,this.snapshotRendering=o,this.disableCacheSamplers=u,this.disableCacheRenderPipelines=h,this.disableCacheBindGroups=c,this.enableGPUTimingMeasurements=l,this._currentRenderPass=null}))})}).then(()=>{this._initializeLimits(),this._bufferManager=new se(this,this._device),this._textureHelper=new _r(this,this._device,this._bufferManager,this._deviceEnabledExtensions),this._cacheSampler=new Q(this._device),this._cacheBindGroups=new B(this._device,this._cacheSampler,this),this._timestampQuery=new Pr(this,this._device,this._bufferManager),this._occlusionQuery=this._device.createQuerySet?new Lr(this,this._device,this._bufferManager):void 0,this._bundleList=new ye(this._device),this._snapshotRendering=new Mr(this,this._snapshotRenderingMode,this._bundleList),this._ubInvertY=this._bufferManager.createBuffer(new Float32Array([-1,0]),T.Uniform|T.CopyDst,"UBInvertY"),this._ubDontInvertY=this._bufferManager.createBuffer(new Float32Array([1,0]),T.Uniform|T.CopyDst,"UBDontInvertY"),this.dbgVerboseLogsForFirstFrames&&this._count===void 0&&(this._count=0,y.Log(["%c frame #"+this._count+" - begin","background: #ffff00"])),this._uploadEncoder=this._device.createCommandEncoder(this._uploadEncoderDescriptor),this._renderEncoder=this._device.createCommandEncoder(this._renderEncoderDescriptor),this._emptyVertexBuffer=new S(this,[0],"",{stride:1,offset:0,size:1,label:"EmptyVertexBuffer"}),this._cacheRenderPipeline=new $(this._device,this._emptyVertexBuffer),this._depthCullingState=new Tr(this._cacheRenderPipeline),this._stencilStateComposer=new Cr(this._cacheRenderPipeline),this._stencilStateComposer.stencilGlobal=this._stencilState,this._depthCullingState.depthTest=!0,this._depthCullingState.depthFunc=515,this._depthCullingState.depthMask=!0,this._textureHelper.setCommandEncoder(this._uploadEncoder),this._clearQuad=new Dr(this._device,this,this._emptyVertexBuffer),this._defaultDrawContext=this.createDrawContext(),this._currentDrawContext=this._defaultDrawContext,this._defaultMaterialContext=this.createMaterialContext(),this._currentMaterialContext=this._defaultMaterialContext,this._initializeContextAndSwapChain(),this._initializeMainAttachments(),this.resize()}).catch(r=>{throw y.Error("A fatal error occurred during WebGPU creation/initialization."),r})}_initGlslang(e){return e=e||{},e={...z._GlslangDefaultOptions,...e},e.glslang?Promise.resolve(e.glslang):self.glslang?self.glslang(e.wasmPath):e.jsPath&&e.wasmPath?q.LoadBabylonScriptAsync(e.jsPath).then(()=>self.glslang(q.GetBabylonScriptURL(e.wasmPath))):Promise.reject("gslang is not available.")}_initializeLimits(){this._caps={maxTexturesImageUnits:this._deviceLimits.maxSampledTexturesPerShaderStage,maxVertexTextureImageUnits:this._deviceLimits.maxSampledTexturesPerShaderStage,maxCombinedTexturesImageUnits:this._deviceLimits.maxSampledTexturesPerShaderStage*2,maxTextureSize:this._deviceLimits.maxTextureDimension2D,maxCubemapTextureSize:this._deviceLimits.maxTextureDimension2D,maxRenderTextureSize:this._deviceLimits.maxTextureDimension2D,maxVertexAttribs:this._deviceLimits.maxVertexAttributes,maxDrawBuffers:8,maxVaryingVectors:this._deviceLimits.maxInterStageShaderVariables,maxFragmentUniformVectors:Math.floor(this._deviceLimits.maxUniformBufferBindingSize/4),maxVertexUniformVectors:Math.floor(this._deviceLimits.maxUniformBufferBindingSize/4),standardDerivatives:!0,astc:this._deviceEnabledExtensions.indexOf("texture-compression-astc")>=0?!0:void 0,s3tc:this._deviceEnabledExtensions.indexOf("texture-compression-bc")>=0?!0:void 0,pvrtc:null,etc1:null,etc2:this._deviceEnabledExtensions.indexOf("texture-compression-etc2")>=0?!0:void 0,bptc:this._deviceEnabledExtensions.indexOf("texture-compression-bc")>=0?!0:void 0,maxAnisotropy:16,uintIndices:!0,fragmentDepthSupported:!0,highPrecisionShaderSupported:!0,colorBufferFloat:!0,supportFloatTexturesResolve:!1,rg11b10ufColorRenderable:this._deviceEnabledExtensions.indexOf("rg11b10ufloat-renderable")>=0,textureFloat:!0,textureFloatLinearFiltering:this._deviceEnabledExtensions.indexOf("float32-filterable")>=0,textureFloatRender:!0,textureHalfFloat:!0,textureHalfFloatLinearFiltering:!0,textureHalfFloatRender:!0,textureLOD:!0,texelFetch:!0,drawBuffersExtension:!0,depthTextureExtension:!0,vertexArrayObject:!1,instancedArrays:!0,timerQuery:typeof BigUint64Array<"u"&&this._deviceEnabledExtensions.indexOf("timestamp-query")!==-1?!0:void 0,supportOcclusionQuery:typeof BigUint64Array<"u",canUseTimestampForTimerQuery:!0,multiview:!1,oculusMultiview:!1,parallelShaderCompile:void 0,blendMinMax:!0,maxMSAASamples:4,canUseGLInstanceID:!0,canUseGLVertexID:!0,supportComputeShaders:!0,supportSRGBBuffers:!0,supportTransformFeedbacks:!1,textureMaxLevel:!0,texture2DArrayMaxLayerCount:this._deviceLimits.maxTextureArrayLayers,disableMorphTargetTexture:!1,textureNorm16:!1},this._features={forceBitmapOverHTMLImageElement:!0,supportRenderAndCopyToLodForFloatTextures:!0,supportDepthStencilTexture:!0,supportShadowSamplers:!0,uniformBufferHardCheckMatrix:!1,allowTexturePrefiltering:!0,trackUbosInFrame:!0,checkUbosContentBeforeUpload:!0,supportCSM:!0,basisNeedsPOT:!1,support3DTextures:!0,needTypeSuffixInShaderConstants:!0,supportMSAA:!0,supportSSAO2:!0,supportIBLShadows:!0,supportExtendedTextureFormats:!0,supportSwitchCaseInShader:!0,supportSyncTextureRead:!1,needsInvertingBitmap:!1,useUBOBindingCache:!1,needShaderCodeInlining:!0,needToAlwaysBindUniformBuffers:!0,supportRenderPasses:!0,supportSpriteInstancing:!0,forceVertexBufferStrideAndOffsetMultiple4Bytes:!0,_checkNonFloatVertexBuffersDontRecreatePipelineContext:!0,_collectUbosUpdatedInFrame:!1}}_initializeContextAndSwapChain(){if(!this._renderingCanvas)throw"The rendering canvas has not been set!";this._context=this._renderingCanvas.getContext("webgpu"),this._configureContext(),this._colorFormat=this._options.swapChainFormat,this._mainRenderPassWrapper.colorAttachmentGPUTextures=[new te(this)],this._mainRenderPassWrapper.colorAttachmentGPUTextures[0].format=this._colorFormat,this._setColorFormat(this._mainRenderPassWrapper)}_initializeMainAttachments(){if(!this._bufferManager)return;this.flushFramebuffer(),this._mainTextureExtends={width:this.getRenderWidth(!0),height:this.getRenderHeight(!0),depthOrArrayLayers:1};const e=new Float32Array([this.getRenderHeight(!0)]);this._bufferManager.setSubData(this._ubInvertY,4,e),this._bufferManager.setSubData(this._ubDontInvertY,4,e);let t;if(this._options.antialias){const i={label:`Texture_MainColor_${this._mainTextureExtends.width}x${this._mainTextureExtends.height}_antialiasing`,size:this._mainTextureExtends,mipLevelCount:1,sampleCount:this._mainPassSampleCount,dimension:"2d",format:this._options.swapChainFormat,usage:16};this._mainTexture&&this._textureHelper.releaseTexture(this._mainTexture),this._mainTexture=this._device.createTexture(i),t=[{view:this._mainTexture.createView({label:"TextureView_MainColor_antialiasing",dimension:"2d",format:this._options.swapChainFormat,mipLevelCount:1,arrayLayerCount:1}),clearValue:new ce(0,0,0,1),loadOp:"clear",storeOp:"store"}]}else t=[{view:void 0,clearValue:new ce(0,0,0,1),loadOp:"clear",storeOp:"store"}];this._mainRenderPassWrapper.depthTextureFormat=this.isStencilEnable?"depth24plus-stencil8":"depth32float",this._setDepthTextureFormat(this._mainRenderPassWrapper),this._setColorFormat(this._mainRenderPassWrapper);const r={label:`Texture_MainDepthStencil_${this._mainTextureExtends.width}x${this._mainTextureExtends.height}`,size:this._mainTextureExtends,mipLevelCount:1,sampleCount:this._mainPassSampleCount,dimension:"2d",format:this._mainRenderPassWrapper.depthTextureFormat,usage:16};this._depthTexture&&this._textureHelper.releaseTexture(this._depthTexture),this._depthTexture=this._device.createTexture(r);const n={view:this._depthTexture.createView({label:`TextureView_MainDepthStencil_${this._mainTextureExtends.width}x${this._mainTextureExtends.height}`,dimension:"2d",format:this._depthTexture.format,mipLevelCount:1,arrayLayerCount:1}),depthClearValue:this._clearDepthValue,depthLoadOp:"clear",depthStoreOp:"store",stencilClearValue:this._clearStencilValue,stencilLoadOp:this.isStencilEnable?"clear":void 0,stencilStoreOp:this.isStencilEnable?"store":void 0};this._mainRenderPassWrapper.renderPassDescriptor={label:"MainRenderPass",colorAttachments:t,depthStencilAttachment:n}}_sharedInit(e){super._sharedInit(e),Ut(this,e,this._creationOptions)}_configureContext(){this._context.configure({device:this._device,format:this._options.swapChainFormat,usage:17,alphaMode:this.premultipliedAlpha?"premultiplied":"opaque"})}resizeImageBitmap(e,t,r){return Nt(this,e,t,r)}_createImageBitmapFromSource(e,t){return $t(this,e,t)}switchFullscreen(e){this.isFullscreen?this.exitFullscreen():this.enterFullscreen(e)}enterFullscreen(e){this.isFullscreen||(this._pointerLockRequested=e,this._renderingCanvas&&Vt(this._renderingCanvas))}exitFullscreen(){this.isFullscreen&&Wt()}enterPointerlock(){this._renderingCanvas&&kt(this._renderingCanvas)}exitPointerlock(){qt()}_rebuildBuffers(){super._rebuildBuffers();for(const e of this._storageBuffers)e.getBuffer().engineId!==this.uniqueId&&e._rebuild()}_restoreEngineAfterContextLost(e){$.ResetCache(),B.ResetCache();const t=n=>{for(const i of n){for(const a of i.meshes){const o=a.subMeshes;if(o)for(const u of o)u._drawWrappers=[]}for(const a of i.materials)a._materialContext?.reset()}};t(this.scenes),t(this._virtualScenes);const r=[];for(const n of this._uniformBuffers)n.name.indexOf("leftOver")<0&&r.push(n);this._uniformBuffers=r,super._restoreEngineAfterContextLost(e)}setSize(e,t,r=!1){return super.setSize(e,t,r)?(this.dbgVerboseLogsForFirstFrames&&(this._count===void 0&&(this._count=0),(!this._count||this._count<this.dbgVerboseLogsNumFrames)&&y.Log(["frame #"+this._count+" - setSize -",e,t])),this._initializeMainAttachments(),this.snapshotRendering&&this.snapshotRenderingReset(),!0):!1}_getShaderProcessor(e){return e===1?this._shaderProcessorWGSL:this._shaderProcessor}_getShaderProcessingContext(e,t){return new O(e,t)}_getCurrentRenderPass(){return this._currentRenderTarget&&!this._currentRenderPass?this._startRenderTargetRenderPass(this._currentRenderTarget,!1,null,!1,!1):this._currentRenderPass||this._startMainRenderPass(!1),this._currentRenderPass}_getCurrentRenderPassWrapper(){return this._currentRenderTarget?this._rttRenderPassWrapper:this._mainRenderPassWrapper}applyStates(){this._stencilStateComposer.apply(),this._cacheRenderPipeline.setAlphaBlendEnabled(this._alphaState.alphaBlend)}wipeCaches(e){this.preventCacheWipeBetweenFrames&&!e||(this._forceEnableEffect=!0,this._currentIndexBuffer=null,this._currentOverrideVertexBuffers=null,this._cacheRenderPipeline.setBuffers(null,null,null),e&&(this._stencilStateComposer.reset(),this._depthCullingState.reset(),this._depthCullingState.depthFunc=515,this._alphaState.reset(),this._alphaMode=1,this._alphaEquation=0,this._cacheRenderPipeline.setAlphaBlendFactors(this._alphaState._blendFunctionParameters,this._alphaState._blendEquationParameters),this._cacheRenderPipeline.setAlphaBlendEnabled(!1),this.setColorWrite(!0)),this._cachedVertexBuffers=null,this._cachedIndexBuffer=null,this._cachedEffectForVertexBuffers=null)}setColorWrite(e){this._colorWriteLocal=e,this._cacheRenderPipeline.setWriteMask(e?15:0)}getColorWrite(){return this._colorWriteLocal}_mustUpdateViewport(){const e=this._viewportCached.x,t=this._viewportCached.y,r=this._viewportCached.z,n=this._viewportCached.w,i=this._viewportsCurrent.x!==e||this._viewportsCurrent.y!==t||this._viewportsCurrent.w!==r||this._viewportsCurrent.h!==n;return i&&(this._viewportsCurrent.x=this._viewportCached.x,this._viewportsCurrent.y=this._viewportCached.y,this._viewportsCurrent.w=this._viewportCached.z,this._viewportsCurrent.h=this._viewportCached.w),i}_applyViewport(e){const t=Math.floor(this._viewportCached.x),r=Math.floor(this._viewportCached.z),n=Math.floor(this._viewportCached.w);let i=Math.floor(this._viewportCached.y);this._currentRenderTarget||(i=this.getRenderHeight(!0)-i-n),e?e.addItem(new fe(t,i,r,n)):this._getCurrentRenderPass().setViewport(t,i,r,n,0,1),this.dbgVerboseLogsForFirstFrames&&(this._count===void 0&&(this._count=0),(!this._count||this._count<this.dbgVerboseLogsNumFrames)&&y.Log(["frame #"+this._count+" - viewport applied - (",this._viewportCached.x,this._viewportCached.y,this._viewportCached.z,this._viewportCached.w,") current pass is main pass="+this._currentPassIsMainPass()]))}_viewport(e,t,r,n){this._viewportCached.x=e,this._viewportCached.y=t,this._viewportCached.z=r,this._viewportCached.w=n}_mustUpdateScissor(){const e=this._scissorCached.x,t=this._scissorCached.y,r=this._scissorCached.z,n=this._scissorCached.w,i=this._scissorsCurrent.x!==e||this._scissorsCurrent.y!==t||this._scissorsCurrent.w!==r||this._scissorsCurrent.h!==n;return i&&(this._scissorsCurrent.x=this._scissorCached.x,this._scissorsCurrent.y=this._scissorCached.y,this._scissorsCurrent.w=this._scissorCached.z,this._scissorsCurrent.h=this._scissorCached.w),i}_applyScissor(e){const t=this._currentRenderTarget?this._scissorCached.y:this.getRenderHeight()-this._scissorCached.w-this._scissorCached.y;e?e.addItem(new ge(this._scissorCached.x,t,this._scissorCached.z,this._scissorCached.w)):this._getCurrentRenderPass().setScissorRect(this._scissorCached.x,t,this._scissorCached.z,this._scissorCached.w),this.dbgVerboseLogsForFirstFrames&&(this._count===void 0&&(this._count=0),(!this._count||this._count<this.dbgVerboseLogsNumFrames)&&y.Log(["frame #"+this._count+" - scissor applied - (",this._scissorCached.x,this._scissorCached.y,this._scissorCached.z,this._scissorCached.w,") current pass is main pass="+this._currentPassIsMainPass()]))}_scissorIsActive(){return this._scissorCached.x!==0||this._scissorCached.y!==0||this._scissorCached.z!==0||this._scissorCached.w!==0}enableScissor(e,t,r,n){this._scissorCached.x=e,this._scissorCached.y=t,this._scissorCached.z=r,this._scissorCached.w=n}disableScissor(){this._scissorCached.x=this._scissorCached.y=this._scissorCached.z=this._scissorCached.w=0,this._scissorsCurrent.x=this._scissorsCurrent.y=this._scissorsCurrent.w=this._scissorsCurrent.h=0}_mustUpdateStencilRef(){const e=this._stencilStateComposer.funcRef!==this._stencilRefsCurrent;return e&&(this._stencilRefsCurrent=this._stencilStateComposer.funcRef),e}_applyStencilRef(e){e?e.addItem(new ne(this._stencilStateComposer.funcRef??0)):this._getCurrentRenderPass().setStencilReference(this._stencilStateComposer.funcRef??0)}_mustUpdateBlendColor(){const e=this._alphaState._blendConstants,t=e[0]!==this._blendColorsCurrent[0]||e[1]!==this._blendColorsCurrent[1]||e[2]!==this._blendColorsCurrent[2]||e[3]!==this._blendColorsCurrent[3];return t&&(this._blendColorsCurrent[0]=e[0],this._blendColorsCurrent[1]=e[1],this._blendColorsCurrent[2]=e[2],this._blendColorsCurrent[3]=e[3]),t}_applyBlendColor(e){e?e.addItem(new _e(this._alphaState._blendConstants.slice())):this._getCurrentRenderPass().setBlendConstant(this._alphaState._blendConstants)}_resetRenderPassStates(){this._viewportsCurrent.x=this._viewportsCurrent.y=this._viewportsCurrent.w=this._viewportsCurrent.h=0,this._scissorsCurrent.x=this._scissorsCurrent.y=this._scissorsCurrent.w=this._scissorsCurrent.h=0,this._stencilRefsCurrent=-1,this._blendColorsCurrent[0]=this._blendColorsCurrent[1]=this._blendColorsCurrent[2]=this._blendColorsCurrent[3]=null}clear(e,t,r,n=!1){e&&e.a===void 0&&(e.a=1);const i=this._scissorIsActive();this.dbgVerboseLogsForFirstFrames&&(this._count===void 0&&(this._count=0),(!this._count||this._count<this.dbgVerboseLogsNumFrames)&&y.Log(["frame #"+this._count+" - clear - backBuffer=",t," depth=",r," stencil=",n," scissor is active=",i])),this._currentRenderTarget?i?(this._currentRenderPass||this._startRenderTargetRenderPass(this._currentRenderTarget,!1,t?e:null,r,n),this._applyScissor(this.compatibilityMode?null:this._bundleList),this._clearFullQuad(t?e:null,r,n)):(this._currentRenderPass&&this._endCurrentRenderPass(),this._startRenderTargetRenderPass(this._currentRenderTarget,!0,t?e:null,r,n)):((!this._currentRenderPass||!i)&&this._startMainRenderPass(!i,t?e:null,r,n),i&&(this._applyScissor(this.compatibilityMode?null:this._bundleList),this._clearFullQuad(t?e:null,r,n)))}_clearFullQuad(e,t,r){const n=this.compatibilityMode?this._getCurrentRenderPass():null;this._clearQuad.setColorFormat(this._colorFormat),this._clearQuad.setDepthStencilFormat(this._depthTextureFormat),this._clearQuad.setMRTAttachments(this._cacheRenderPipeline.mrtAttachments??[],this._cacheRenderPipeline.mrtTextureArray??[],this._cacheRenderPipeline.mrtTextureCount),this.compatibilityMode?n.setStencilReference(this._clearStencilValue):this._bundleList.addItem(new ne(this._clearStencilValue));const i=this._clearQuad.clear(n,e,t,r,this.currentSampleCount);this.compatibilityMode?this._applyStencilRef(null):(this._bundleList.addBundle(i),this._applyStencilRef(this._bundleList),this._reportDrawCall())}createVertexBuffer(e,t,r){let n;return e instanceof Array?n=new Float32Array(e):e instanceof ArrayBuffer?n=new Uint8Array(e):n=e,this._bufferManager.createBuffer(n,T.Vertex|T.CopyDst,r)}createDynamicVertexBuffer(e,t){return this.createVertexBuffer(e,void 0,t)}createIndexBuffer(e,t,r){let n=!0,i;if(e instanceof Uint32Array||e instanceof Int32Array)i=e;else if(e instanceof Uint16Array)i=e,n=!1;else{for(let o=0;o<e.length;o++)if(e[o]>65535){i=new Uint32Array(e);break}i||(i=new Uint16Array(e),n=!1)}const a=this._bufferManager.createBuffer(i,T.Index|T.CopyDst,r);return a.is32Bits=n,a}updateDynamicIndexBuffer(e,t,r=0){const n=e;let i;e.is32Bits?i=t instanceof Uint32Array?t:new Uint32Array(t):i=t instanceof Uint16Array?t:new Uint16Array(t),this._bufferManager.setSubData(n,r,i)}updateDynamicVertexBuffer(e,t,r,n){const i=e;r===void 0&&(r=0);let a;n===void 0?(t instanceof Array?a=new Float32Array(t):t instanceof ArrayBuffer?a=new Uint8Array(t):a=t,n=a.byteLength):t instanceof Array?a=new Float32Array(t):t instanceof ArrayBuffer?a=new Uint8Array(t):a=t,this._bufferManager.setSubData(i,r,a,0,n)}_createBuffer(e,t,r){let n;e instanceof Array?n=new Float32Array(e):e instanceof ArrayBuffer?n=new Uint8Array(e):n=e;let i=0;return t&1&&(i|=T.CopySrc),t&2&&(i|=T.CopyDst),t&4&&(i|=T.Uniform),t&8&&(i|=T.Vertex),t&16&&(i|=T.Index),t&32&&(i|=T.Storage),t&64&&(i|=T.Indirect),this._bufferManager.createBuffer(n,i,r)}bindBuffersDirectly(){throw"Not implemented on WebGPU"}updateAndBindInstancesBuffer(){throw"Not implemented on WebGPU"}unbindInstanceAttributes(){}bindBuffers(e,t,r,n){this._currentIndexBuffer=t,this._currentOverrideVertexBuffers=n??null,this._cacheRenderPipeline.setBuffers(e,t,this._currentOverrideVertexBuffers)}_releaseBuffer(e){return this._bufferManager.releaseBuffer(e)}createUniformBuffer(e,t){let r;return e instanceof Array?r=new Float32Array(e):r=e,this._bufferManager.createBuffer(r,T.Uniform|T.CopyDst,t)}createDynamicUniformBuffer(e,t){return this.createUniformBuffer(e,t)}updateUniformBuffer(e,t,r,n){r===void 0&&(r=0);const i=e;let a;n===void 0?(t instanceof Float32Array?a=t:a=new Float32Array(t),n=a.byteLength):t instanceof Float32Array?a=t:a=new Float32Array(t),this._bufferManager.setSubData(i,r,a,0,n)}bindUniformBufferBase(e,t,r){this._currentDrawContext.setBuffer(r,e)}bindUniformBlock(){}createEffect(e,t,r,n,i,a,o,u,h,c=0,l){const d=typeof e=="string"?e:e.vertexToken||e.vertexSource||e.vertexElement||e.vertex,g=typeof e=="string"?e:e.fragmentToken||e.fragmentSource||e.fragmentElement||e.fragment,f=this._getGlobalDefines(),_=t.attributes!==void 0;let p=i??t.defines??"";f&&(p+=`
`+f);const m=d+"+"+g+"@"+p;if(this._compiledEffects[m]){const b=this._compiledEffects[m];return o&&b.isReady()&&o(b),b._refCount++,b}const x=new Qt(e,t,_?this:r,n,this,i,a,o,u,h,m,t.shaderLanguage??c,t.extraInitializationsAsync??l);return this._compiledEffects[m]=x,x}_compileRawShaderToSpirV(e,t){return this._glslang.compileGLSL(e,t)}_compileShaderToSpirV(e,t,r,n){return this._compileRawShaderToSpirV(n+(r?r+`
`:"")+e,t)}_getWGSLShader(e,t,r){return r?r="//"+r.split(`
`).join(`
//`)+`
`:r="",r+e}_createPipelineStageDescriptor(e,t,r,n,i){return this._tintWASM&&r===0&&(e=this._tintWASM.convertSpirV2WGSL(e,n),t=this._tintWASM.convertSpirV2WGSL(t,i)),{vertexStage:{module:this._device.createShaderModule({label:"vertex",code:e}),entryPoint:"main"},fragmentStage:{module:this._device.createShaderModule({label:"fragment",code:t}),entryPoint:"main"}}}_compileRawPipelineStageDescriptor(e,t,r){const n=e.indexOf("#define DISABLE_UNIFORMITY_ANALYSIS")>=0,i=t.indexOf("#define DISABLE_UNIFORMITY_ANALYSIS")>=0,a=r===0?this._compileRawShaderToSpirV(e,"vertex"):e,o=r===0?this._compileRawShaderToSpirV(t,"fragment"):t;return this._createPipelineStageDescriptor(a,o,r,n,i)}_compilePipelineStageDescriptor(e,t,r,n){this.onBeforeShaderCompilationObservable.notifyObservers(this);const i=e.indexOf("#define DISABLE_UNIFORMITY_ANALYSIS")>=0,a=t.indexOf("#define DISABLE_UNIFORMITY_ANALYSIS")>=0,o=`#version 450
`,u=n===0?this._compileShaderToSpirV(e,"vertex",r,o):this._getWGSLShader(e,"vertex",r),h=n===0?this._compileShaderToSpirV(t,"fragment",r,o):this._getWGSLShader(t,"fragment",r),c=this._createPipelineStageDescriptor(u,h,n,i,a);return this.onAfterShaderCompilationObservable.notifyObservers(this),c}createRawShaderProgram(){throw"Not available on WebGPU"}createShaderProgram(){throw"Not available on WebGPU"}inlineShaderCode(e){const t=new oe(e);return t.debug=!1,t.processCode(),t.code}createPipelineContext(e){return new Kt(e,this)}createMaterialContext(){return new ie}createDrawContext(){return new ae(this._bufferManager)}async _preparePipelineContext(e,t,r,n,i,a,o,u,h,c,l){const d=e,g=d.shaderProcessingContext.shaderLanguage;g===0&&!this._glslangAndTintAreFullyLoaded&&await this.prepareGlslangAndTintAsync(),this.dbgShowShaderCode&&(y.Log(["defines",u]),y.Log(t),y.Log(r),y.Log("***********************************************")),d.sources={fragment:r,vertex:t,rawVertex:i,rawFragment:a},n?d.stages=this._compileRawPipelineStageDescriptor(t,r,g):d.stages=this._compilePipelineStageDescriptor(t,r,u,g),l()}getAttributes(e,t){const r=new Array(t.length),n=e;for(let i=0;i<t.length;i++){const a=t[i],o=n.shaderProcessingContext.availableAttributes[a];o!==void 0&&(r[i]=o)}return r}enableEffect(e){if(e){if(!Ht(e))this._currentEffect=e,this._currentMaterialContext=this._defaultMaterialContext,this._currentDrawContext=this._defaultDrawContext,this._counters.numEnableEffects++,this.dbgLogIfNotDrawWrapper&&y.Warn(`enableEffect has been called with an Effect and not a Wrapper! effect.uniqueId=${e.uniqueId}, effect.name=${e.name}, effect.name.vertex=${typeof e.name=="string"?"":e.name.vertex}, effect.name.fragment=${typeof e.name=="string"?"":e.name.fragment}`,10);else if(!e.effect||e.effect===this._currentEffect&&e.materialContext===this._currentMaterialContext&&e.drawContext===this._currentDrawContext&&!this._forceEnableEffect){if(!e.effect&&this.dbgShowEmptyEnableEffectCalls)throw y.Log(["drawWrapper=",e]),"Invalid call to enableEffect: the effect property is empty!";return}else if(this._currentEffect=e.effect,this._currentMaterialContext=e.materialContext,this._currentDrawContext=e.drawContext,this._counters.numEnableDrawWrapper++,!this._currentMaterialContext)throw y.Log(["drawWrapper=",e]),"Invalid call to enableEffect: the materialContext property is empty!";this._stencilStateComposer.stencilMaterial=void 0,this._forceEnableEffect=!1,this._currentEffect.onBind&&this._currentEffect.onBind(this._currentEffect),this._currentEffect._onBindObservable&&this._currentEffect._onBindObservable.notifyObservers(this._currentEffect)}}_releaseEffect(e){this._compiledEffects[e._key]&&(delete this._compiledEffects[e._key],this._deletePipelineContext(e.getPipelineContext()))}releaseEffects(){for(const e in this._compiledEffects){const t=this._compiledEffects[e].getPipelineContext();this._deletePipelineContext(t)}this._compiledEffects={},this.onReleaseEffectsObservable.notifyObservers(this)}_deletePipelineContext(e){const t=e;t&&Yt(t)}get needPOTTextures(){return!1}_createHardwareTexture(){return new te(this)}_releaseTexture(e){const t=this._internalTexturesCache.indexOf(e);t!==-1&&this._internalTexturesCache.splice(t,1),this._textureHelper.releaseTexture(e)}_getRGBABufferInternalSizedFormat(){return 5}updateTextureComparisonFunction(e,t){e._comparisonFunction=t}_createInternalTexture(e,t,r=!0,n=0){const i={};t!==void 0&&typeof t=="object"?(i.generateMipMaps=t.generateMipMaps,i.createMipMaps=t.createMipMaps,i.type=t.type===void 0?0:t.type,i.samplingMode=t.samplingMode===void 0?3:t.samplingMode,i.format=t.format===void 0?5:t.format,i.samples=t.samples??1,i.creationFlags=t.creationFlags??0,i.useSRGBBuffer=t.useSRGBBuffer??!1,i.label=t.label):(i.generateMipMaps=t,i.type=0,i.samplingMode=3,i.format=5,i.samples=1,i.creationFlags=0,i.useSRGBBuffer=!1),(i.type===1&&!this._caps.textureFloatLinearFiltering||i.type===2&&!this._caps.textureHalfFloatLinearFiltering)&&(i.samplingMode=1),i.type===1&&!this._caps.textureFloat&&(i.type=0,y.Warn("Float textures are not supported. Type forced to TEXTURETYPE_UNSIGNED_BYTE"));const a=new W(this,n),o=e.width??e,u=e.height??e,h=e.depth??0,c=e.layers??0;if(a.baseWidth=o,a.baseHeight=u,a.width=o,a.height=u,a.depth=h||c,a.isReady=!0,a.samples=i.samples,a.generateMipMaps=!!i.generateMipMaps,a.samplingMode=i.samplingMode,a.type=i.type,a.format=i.format,a.is2DArray=c>0,a.is3D=h>0,a._cachedWrapU=0,a._cachedWrapV=0,a._useSRGBBuffer=i.useSRGBBuffer,a.label=i.label,this._internalTexturesCache.push(a),!r){const l=!i.generateMipMaps&&i.createMipMaps;l&&(a.generateMipMaps=!0),this._textureHelper.createGPUTextureForInternalTexture(a,o,u,c||1,i.creationFlags),l&&(a.generateMipMaps=!1)}return a}createTexture(e,t,r,n,i=3,a=null,o=null,u=null,h=null,c=null,l=null,d,g,f,_){return this._createTextureBase(e,t,r,n,i,a,o,(p,m,x,b,I,w,v,P)=>{const R=b;if(p.baseWidth=R.width,p.baseHeight=R.height,p.width=R.width,p.height=R.height,p.format=p.format!==-1?p.format:c??5,p.type=p.type!==-1?p.type:0,p._creationFlags=f??0,P(p.width,p.height,R,m,p,()=>{}),p._hardwareTexture?.underlyingResource)!w&&!v&&this._generateMipmaps(p,this._uploadEncoder);else{const M=this._textureHelper.createGPUTextureForInternalTexture(p,R.width,R.height,void 0,f);C.IsImageBitmap(R)&&(this._textureHelper.updateTexture(R,p,R.width,R.height,p.depth,M.format,0,0,I,!1,0,0),!w&&!v&&this._generateMipmaps(p,this._uploadEncoder))}x&&x.removePendingData(p),p.isReady=!0,p.onLoadedObservable.notifyObservers(p),p.onLoadedObservable.clear()},()=>!1,u,h,c,l,d,g,_)}wrapWebGPUTexture(e){const t=new te(this,e),r=new W(this,0,!0);return r._hardwareTexture=t,r.isReady=!0,r}wrapWebGLTexture(){throw new Error("wrapWebGLTexture is not supported, use wrapWebGPUTexture instead.")}_getUseSRGBBuffer(e,t){return e&&this._caps.supportSRGBBuffers}_unpackFlipY(e){}updateTextureSamplingMode(e,t,r=!1){r&&(t.generateMipMaps=!0,this._generateMipmaps(t)),t.samplingMode=e}updateTextureWrappingMode(e,t,r=null,n=null){t!==null&&(e._cachedWrapU=t),r!==null&&(e._cachedWrapV=r),(e.is2DArray||e.is3D)&&n!==null&&(e._cachedWrapR=n)}updateTextureDimensions(e,t,r,n=1){if(!e._hardwareTexture||e.width===t&&e.height===r&&e.depth===n)return;const i=e._hardwareTexture.textureAdditionalUsages;e._hardwareTexture.release(),this._textureHelper.createGPUTextureForInternalTexture(e,t,r,n,i)}_setInternalTexture(e,t,r){if(r=r??e,this._currentEffect){const i=this._currentEffect._pipelineContext.shaderProcessingContext.availableTextures[r];if(this._currentMaterialContext.setTexture(e,t),i&&i.autoBindSampler){const a=r+"Sampler";this._currentMaterialContext.setSampler(a,t)}}}createPrefilteredCubeTexture(e,t,r,n,i=null,a=null,o,u=null,h=!0){const c=l=>{if(!l){i&&i(null);return}const d=l.texture;h?l.info.sphericalPolynomial&&(d._sphericalPolynomial=l.info.sphericalPolynomial):d._sphericalPolynomial=new jt,d._source=9,i&&i(d)};return this.createCubeTexture(e,t,null,!1,c,a,o,u,h,r,n)}setTexture(e,t,r,n){this._setTexture(e,r,!1,!1,n,n)}setTextureArray(e,t,r,n){for(let i=0;i<r.length;i++)this._setTexture(-1,r[i],!0,!1,n+i.toString(),n)}_setTexture(e,t,r=!1,n=!1,i="",a){if(a=a??i,this._currentEffect){if(!t)return this._currentMaterialContext.setTexture(i,null),!1;if(t.video)t.update();else if(t.delayLoadState===4)return t.delayLoad(),!1;let o=null;if(n?o=t.depthStencilTexture:t.isReady()?o=t.getInternalTexture():t.isCube?o=this.emptyCubeTexture:t.is3D?o=this.emptyTexture3D:t.is2DArray?o=this.emptyTexture2DArray:o=this.emptyTexture,o&&!o.isMultiview){if(o.isCube&&o._cachedCoordinatesMode!==t.coordinatesMode){o._cachedCoordinatesMode=t.coordinatesMode;const u=t.coordinatesMode!==3&&t.coordinatesMode!==5?1:0;t.wrapU=u,t.wrapV=u}o._cachedWrapU=t.wrapU,o._cachedWrapV=t.wrapV,o.is3D&&(o._cachedWrapR=t.wrapR),this._setAnisotropicLevel(0,o,t.anisotropicFilteringLevel)}this._setInternalTexture(i,o,a)}else this.dbgVerboseLogsForFirstFrames&&(this._count===void 0&&(this._count=0),(!this._count||this._count<this.dbgVerboseLogsNumFrames)&&y.Log(["frame #"+this._count+" - _setTexture called with a null _currentEffect! texture=",t]));return!0}_setAnisotropicLevel(e,t,r){t._cachedAnisotropicFilteringLevel!==r&&(t._cachedAnisotropicFilteringLevel=Math.min(r,this._caps.maxAnisotropy))}_bindTexture(e,t,r){e!==void 0&&this._setInternalTexture(r,t)}generateMipmaps(e){this._generateMipmaps(e)}updateTextureData(e,t,r,n,i,a,o=0,u=0,h=!1){let c=e._hardwareTexture;e._hardwareTexture?.underlyingResource||(c=this._textureHelper.createGPUTextureForInternalTexture(e));const l=new Uint8Array(t.buffer,t.byteOffset,t.byteLength);this._textureHelper.updateTexture(l,e,i,a,e.depth,c.format,o,u,e.invertY,!1,r,n),h&&this._generateMipmaps(e)}_uploadCompressedDataToTextureDirectly(e,t,r,n,i,a=0,o=0){let u=e._hardwareTexture;e._hardwareTexture?.underlyingResource||(e.format=t,u=this._textureHelper.createGPUTextureForInternalTexture(e,r,n));const h=new Uint8Array(i.buffer,i.byteOffset,i.byteLength);this._textureHelper.updateTexture(h,e,r,n,e.depth,u.format,a,o,!1,!1,0,0)}_uploadDataToTextureDirectly(e,t,r=0,n=0,i,a=!1){const o=Math.round(Math.log(e.width)*Math.LOG2E),u=Math.round(Math.log(e.height)*Math.LOG2E),h=a?e.width:Math.pow(2,Math.max(o-n,0)),c=a?e.height:Math.pow(2,Math.max(u-n,0));let l=e._hardwareTexture;e._hardwareTexture?.underlyingResource||(l=this._textureHelper.createGPUTextureForInternalTexture(e,h,c));const d=new Uint8Array(t.buffer,t.byteOffset,t.byteLength);this._textureHelper.updateTexture(d,e,h,c,e.depth,l.format,r,n,e.invertY,!1,0,0)}_uploadArrayBufferViewToTexture(e,t,r=0,n=0){this._uploadDataToTextureDirectly(e,t,r,n)}_uploadImageToTexture(e,t,r=0,n=0){let i=e._hardwareTexture;if(e._hardwareTexture?.underlyingResource||(i=this._textureHelper.createGPUTextureForInternalTexture(e)),t instanceof HTMLImageElement)throw"WebGPU engine: HTMLImageElement not supported in _uploadImageToTexture!";const a=t,o=Math.ceil(e.width/(1<<n)),u=Math.ceil(e.height/(1<<n));this._textureHelper.updateTexture(a,e,o,u,e.depth,i.format,r,n,e.invertY,!1,0,0)}readPixels(e,t,r,n,i=!0,a=!0,o=null){const h=this._getCurrentRenderPassWrapper().colorAttachmentGPUTextures[0];if(!h)return Promise.resolve(new Uint8Array(0));const c=h.underlyingResource,l=h.format;return c?(a&&this.flushFramebuffer(),this._textureHelper.readPixels(c,e,t,r,n,l,void 0,void 0,o)):Promise.resolve(new Uint8Array(0))}_measureFps(){this._performanceMonitor.sampleFrame(),this._fps=this._performanceMonitor.averageFPS,this._deltaTime=this._performanceMonitor.instantaneousFrameTime||0}get performanceMonitor(){return this._performanceMonitor}beginFrame(){this._measureFps(),super.beginFrame()}endFrame(){if(this._endCurrentRenderPass(),this._snapshotRendering.endFrame(),this._timestampQuery.endFrame(this._renderEncoder),this._timestampIndex=0,this.flushFramebuffer(),this._textureHelper.destroyDeferredTextures(),this._bufferManager.destroyDeferredBuffers(),this._features._collectUbosUpdatedInFrame){if(this.dbgVerboseLogsForFirstFrames&&(this._count===void 0&&(this._count=0),!this._count||this._count<this.dbgVerboseLogsNumFrames)){const e=[];for(const t in ee._UpdatedUbosInFrame)e.push(t+":"+ee._UpdatedUbosInFrame[t]);y.Log(["frame #"+this._count+" - updated ubos -",e.join(", ")])}ee._UpdatedUbosInFrame={}}this.countersLastFrame.numEnableEffects=this._counters.numEnableEffects,this.countersLastFrame.numEnableDrawWrapper=this._counters.numEnableDrawWrapper,this.countersLastFrame.numBundleCreationNonCompatMode=this._counters.numBundleCreationNonCompatMode,this.countersLastFrame.numBundleReuseNonCompatMode=this._counters.numBundleReuseNonCompatMode,this._counters.numEnableEffects=0,this._counters.numEnableDrawWrapper=0,this._counters.numBundleCreationNonCompatMode=0,this._counters.numBundleReuseNonCompatMode=0,this._cacheRenderPipeline.endFrame(),this._cacheBindGroups.endFrame(),this._pendingDebugCommands.length=0,this.dbgVerboseLogsForFirstFrames&&(this._count===void 0&&(this._count=0),this._count<this.dbgVerboseLogsNumFrames&&y.Log(["%c frame #"+this._count+" - end","background: #ffff00"]),this._count<this.dbgVerboseLogsNumFrames&&(this._count++,this._count!==this.dbgVerboseLogsNumFrames&&y.Log(["%c frame #"+this._count+" - begin","background: #ffff00"]))),super.endFrame()}extractDriverInfo(){return""}flushFramebuffer(){this._endCurrentRenderPass(),this._commandBuffers[0]=this._uploadEncoder.finish(),this._commandBuffers[1]=this._renderEncoder.finish(),this._device.queue.submit(this._commandBuffers),this._uploadEncoder=this._device.createCommandEncoder(this._uploadEncoderDescriptor),this._renderEncoder=this._device.createCommandEncoder(this._renderEncoderDescriptor),this._timestampQuery.startFrame(this._uploadEncoder),this._textureHelper.setCommandEncoder(this._uploadEncoder),this._bundleList.reset()}_currentFrameBufferIsDefaultFrameBuffer(){return this._currentPassIsMainPass()}_startRenderTargetRenderPass(e,t,r,n,i){this._endCurrentRenderPass();const a=e,o=a._depthStencilTexture,u=o?._hardwareTexture,h=u?.underlyingResource,c=u?.getMSAATexture(0),l=h?.createView(this._rttRenderPassWrapper.depthAttachmentViewDescriptor),d=c?.createView(this._rttRenderPassWrapper.depthAttachmentViewDescriptor),g=u?C.HasStencilAspect(u.format):!1,f=[];this.useReverseDepthBuffer&&this.setDepthFunctionToGreaterOrEqual();const _=Ur;r&&(_.r=r.r*255,_.g=r.g*255,_.b=r.b*255,_.a=r.a*255);const p=t&&r,m=t&&n,x=t&&i;if(a._attachments&&a.isMulti){(!this._mrtAttachments||this._mrtAttachments.length===0)&&(this._mrtAttachments=a._defaultAttachments);for(let b=0;b<this._mrtAttachments.length;++b){const I=this._mrtAttachments[b],w=a.textures[b],v=w?._hardwareTexture,P=v?.underlyingResource;if(v&&P){const R=a.getBaseArrayLayer(b),M=v.getMSAATexture(R),E={...this._rttRenderPassWrapper.colorAttachmentViewDescriptor,dimension:w.is3D?"3d":"2d",format:v.format,baseArrayLayer:R},Ct={...this._rttRenderPassWrapper.colorAttachmentViewDescriptor,dimension:w.is3D?"3d":"2d",format:v.format,baseArrayLayer:0},Tt=w.type===7||w.type===5,Se=P.createView(E),we=M?.createView(Ct);f.push({view:we||Se,resolveTarget:M?Se:void 0,depthSlice:w.is3D?a.layerIndices?.[b]??0:void 0,clearValue:I!==0&&p?Tt?_:r:void 0,loadOp:I!==0&&p?"clear":"load",storeOp:"store"})}}this._cacheRenderPipeline.setMRT(a.textures,this._mrtAttachments.length),this._cacheRenderPipeline.setMRTAttachments(this._mrtAttachments)}else{const b=a.texture;if(b){const I=b._hardwareTexture,w=I.underlyingResource;let v;a.is3D&&(v=this._rttRenderPassWrapper.colorAttachmentViewDescriptor.baseArrayLayer,this._rttRenderPassWrapper.colorAttachmentViewDescriptor.baseArrayLayer=0);const P=I.getMSAATexture(0),R=w.createView(this._rttRenderPassWrapper.colorAttachmentViewDescriptor),M=P?.createView(this._rttRenderPassWrapper.colorAttachmentViewDescriptor),E=b.type===7||b.type===5;f.push({view:M||R,resolveTarget:P?R:void 0,depthSlice:v,clearValue:p?E?_:r:void 0,loadOp:p?"clear":"load",storeOp:"store"})}else f.push(null)}if(this._debugPushGroup?.("render target pass"+(e.label?" ("+e.label+")":""),0),this._rttRenderPassWrapper.renderPassDescriptor={label:(e.label??"RTT")+" - RenderPass",colorAttachments:f,depthStencilAttachment:o&&h?{view:d||l,depthClearValue:m?this.useReverseDepthBuffer?this._clearReverseDepthValue:this._clearDepthValue:void 0,depthLoadOp:m?"clear":"load",depthStoreOp:"store",stencilClearValue:a._depthStencilTextureWithStencil&&x?this._clearStencilValue:void 0,stencilLoadOp:g?a._depthStencilTextureWithStencil&&x?"clear":"load":void 0,stencilStoreOp:g?"store":void 0}:void 0,occlusionQuerySet:this._occlusionQuery?.hasQueries?this._occlusionQuery.querySet:void 0},this._timestampQuery.startPass(this._rttRenderPassWrapper.renderPassDescriptor,this._timestampIndex),this._currentRenderPass=this._renderEncoder.beginRenderPass(this._rttRenderPassWrapper.renderPassDescriptor),this.dbgVerboseLogsForFirstFrames&&(this._count===void 0&&(this._count=0),!this._count||this._count<this.dbgVerboseLogsNumFrames)){const b=a.texture;y.Log(["frame #"+this._count+" - render target begin pass - rtt name="+e.label+", internalTexture.uniqueId="+b.uniqueId+", width="+b.width+", height="+b.height+", setClearStates="+t,"renderPassDescriptor=",this._rttRenderPassWrapper.renderPassDescriptor])}this._debugFlushPendingCommands?.(),this._resetRenderPassStates(),(!u||!C.HasStencilAspect(u.format))&&(this._stencilStateComposer.enabled=!1)}_startMainRenderPass(e,t,r,n){this._endCurrentRenderPass(),this.useReverseDepthBuffer&&this.setDepthFunctionToGreaterOrEqual();const i=e&&t,a=e&&r,o=e&&n;this._mainRenderPassWrapper.renderPassDescriptor.colorAttachments[0].clearValue=i?t:void 0,this._mainRenderPassWrapper.renderPassDescriptor.colorAttachments[0].loadOp=i?"clear":"load",this._mainRenderPassWrapper.renderPassDescriptor.depthStencilAttachment.depthClearValue=a?this.useReverseDepthBuffer?this._clearReverseDepthValue:this._clearDepthValue:void 0,this._mainRenderPassWrapper.renderPassDescriptor.depthStencilAttachment.depthLoadOp=a?"clear":"load",this._mainRenderPassWrapper.renderPassDescriptor.depthStencilAttachment.stencilClearValue=o?this._clearStencilValue:void 0,this._mainRenderPassWrapper.renderPassDescriptor.depthStencilAttachment.stencilLoadOp=this.isStencilEnable?o?"clear":"load":void 0,this._mainRenderPassWrapper.renderPassDescriptor.occlusionQuerySet=this._occlusionQuery?.hasQueries?this._occlusionQuery.querySet:void 0;const u=this._context.getCurrentTexture();this._mainRenderPassWrapper.colorAttachmentGPUTextures[0].set(u),this._options.antialias?(mt.format=u.format,this._mainRenderPassWrapper.renderPassDescriptor.colorAttachments[0].resolveTarget=u.createView(mt)):(bt.format=u.format,this._mainRenderPassWrapper.renderPassDescriptor.colorAttachments[0].view=u.createView(bt)),this.dbgVerboseLogsForFirstFrames&&(this._count===void 0&&(this._count=0),(!this._count||this._count<this.dbgVerboseLogsNumFrames)&&y.Log(["frame #"+this._count+" - main begin pass - texture width="+this._mainTextureExtends.width," height="+this._mainTextureExtends.height+", setClearStates="+e,"renderPassDescriptor=",this._mainRenderPassWrapper.renderPassDescriptor])),this._debugPushGroup?.("main pass",0),this._timestampQuery.startPass(this._mainRenderPassWrapper.renderPassDescriptor,this._timestampIndex),this._currentRenderPass=this._renderEncoder.beginRenderPass(this._mainRenderPassWrapper.renderPassDescriptor),this._setDepthTextureFormat(this._mainRenderPassWrapper),this._setColorFormat(this._mainRenderPassWrapper),this._debugFlushPendingCommands?.(),this._resetRenderPassStates(),this._isStencilEnable||(this._stencilStateComposer.enabled=!1)}bindFramebuffer(e,t=0,r,n,i,a=0,o=0){const u=e.texture?._hardwareTexture;this._currentRenderTarget?this.unBindFramebuffer(this._currentRenderTarget):this._endCurrentRenderPass(),this._currentRenderTarget=e;const h=this._currentRenderTarget._depthStencilTexture;this._rttRenderPassWrapper.colorAttachmentGPUTextures[0]=u,this._rttRenderPassWrapper.depthTextureFormat=h?C.GetWebGPUTextureFormat(-1,h.format):void 0,this._setDepthTextureFormat(this._rttRenderPassWrapper),this._setColorFormat(this._rttRenderPassWrapper),this._rttRenderPassWrapper.colorAttachmentViewDescriptor={format:this._colorFormat,dimension:e.is3D?"3d":"2d",mipLevelCount:1,baseArrayLayer:e.isCube?o*6+t:o,baseMipLevel:a,arrayLayerCount:1,aspect:"all"},this._rttRenderPassWrapper.depthAttachmentViewDescriptor={format:this._depthTextureFormat,dimension:h&&h.is3D?"3d":"2d",mipLevelCount:1,baseArrayLayer:h?h.isCube?o*6+t:o:0,baseMipLevel:0,arrayLayerCount:1,aspect:"all"},this.dbgVerboseLogsForFirstFrames&&(this._count===void 0&&(this._count=0),(!this._count||this._count<this.dbgVerboseLogsNumFrames)&&y.Log(["frame #"+this._count+" - bindFramebuffer - rtt name="+e.label+", internalTexture.uniqueId="+e.texture?.uniqueId+", face="+t+", lodLevel="+a+", layer="+o,"colorAttachmentViewDescriptor=",this._rttRenderPassWrapper.colorAttachmentViewDescriptor,"depthAttachmentViewDescriptor=",this._rttRenderPassWrapper.depthAttachmentViewDescriptor])),this._cachedViewport&&!i?this.setViewport(this._cachedViewport,r,n):(r||(r=e.width,a&&(r=r/Math.pow(2,a))),n||(n=e.height,a&&(n=n/Math.pow(2,a))),this._viewport(0,0,r,n)),this.wipeCaches()}unBindFramebuffer(e,t=!1,r){const n=this._currentRenderTarget;this._currentRenderTarget=null,r&&r(),this._currentRenderTarget=n,this._endCurrentRenderPass(),t||(e.isMulti?this.generateMipMapsMultiFramebuffer(e):this.generateMipMapsFramebuffer(e)),this._currentRenderTarget=null,this.dbgVerboseLogsForFirstFrames&&(this._count===void 0&&(this._count=0),(!this._count||this._count<this.dbgVerboseLogsNumFrames)&&y.Log("frame #"+this._count+" - unBindFramebuffer - rtt name="+e.label+", internalTexture.uniqueId=",e.texture?.uniqueId)),this._mrtAttachments=[],this._cacheRenderPipeline.setMRT([]),this._cacheRenderPipeline.setMRTAttachments(this._mrtAttachments)}generateMipMapsFramebuffer(e){!e.isMulti&&e.texture?.generateMipMaps&&!e.isCube&&this._generateMipmaps(e.texture)}resolveFramebuffer(e){throw new Error("resolveFramebuffer is not yet implemented in WebGPU!")}restoreDefaultFramebuffer(){this._currentRenderTarget?this.unBindFramebuffer(this._currentRenderTarget):this._currentRenderPass||this._startMainRenderPass(!1),this._cachedViewport&&this.setViewport(this._cachedViewport),this.wipeCaches()}_setColorFormat(e){const t=e.colorAttachmentGPUTextures[0]?.format??null;this._cacheRenderPipeline.setColorFormat(t),this._colorFormat!==t&&(this._colorFormat=t)}_setDepthTextureFormat(e){this._cacheRenderPipeline.setDepthStencilFormat(e.depthTextureFormat),this._depthTextureFormat!==e.depthTextureFormat&&(this._depthTextureFormat=e.depthTextureFormat)}setDitheringState(){}setRasterizerState(){}_executeWhenRenderingStateIsCompiled(e,t){t()}bindSamplers(){}_getUnpackAlignement(){return 1}_bindTextureDirectly(){return!1}setStateCullFaceType(e,t=!1){const r=this.cullBackFaces??e??!0?1:2;(this._depthCullingState.cullFace!==r||t)&&(this._depthCullingState.cullFace=r)}setState(e,t=0,r,n=!1,i,a,o=0){(this._depthCullingState.cull!==e||r)&&(this._depthCullingState.cull=e),this.setStateCullFaceType(i,r),this.setZOffset(t),this.setZOffsetUnits(o);const u=n?this._currentRenderTarget?1:2:this._currentRenderTarget?2:1;(this._depthCullingState.frontFace!==u||r)&&(this._depthCullingState.frontFace=u),this._stencilStateComposer.stencilMaterial=a}_applyRenderPassChanges(e){const t=this._stencilStateComposer.enabled?this._mustUpdateStencilRef():!1,r=this._alphaState.alphaBlend?this._mustUpdateBlendColor():!1;this._mustUpdateViewport()&&this._applyViewport(e),this._mustUpdateScissor()&&this._applyScissor(e),t&&this._applyStencilRef(e),r&&this._applyBlendColor(e)}_draw(e,t,r,n,i){const a=this._getCurrentRenderPass(),o=this._bundleList;this.applyStates();const u=this._currentEffect._pipelineContext;if(this.bindUniformBufferBase(this._currentRenderTarget?this._ubInvertY:this._ubDontInvertY,0,L.InternalsUBOName),u.uniformBuffer&&(u.uniformBuffer.update(),this.bindUniformBufferBase(u.uniformBuffer.getBuffer(),0,L.LeftOvertUBOName)),this._snapshotRendering.play){this._reportDrawCall();return}!this.compatibilityMode&&(this._currentDrawContext.isDirty(this._currentMaterialContext.updateId)||this._currentMaterialContext.isDirty||this._currentMaterialContext.forceBindGroupCreation)&&(this._currentDrawContext.fastBundle=void 0);const h=!this.compatibilityMode&&this._currentDrawContext.fastBundle;let c=a;if(h||this._snapshotRendering.record){if(this._applyRenderPassChanges(o),!this._snapshotRendering.record){this._counters.numBundleReuseNonCompatMode++,this._currentDrawContext.indirectDrawBuffer&&this._currentDrawContext.setIndirectData(n,i||1,r),o.addBundle(this._currentDrawContext.fastBundle),this._reportDrawCall();return}c=o.getBundleEncoder(this._cacheRenderPipeline.colorFormats,this._depthTextureFormat,this.currentSampleCount),o.numDrawCalls++}let l=0;if(this._currentMaterialContext.hasFloatOrDepthTextures){let p=1;for(let m=0;m<u.shaderProcessingContext.textureNames.length;++m){const x=u.shaderProcessingContext.textureNames[m],b=this._currentMaterialContext.textures[x]?.texture,I=b&&b.format>=13&&b.format<=18;(b?.type===1&&!this._caps.textureFloatLinearFiltering||I)&&(l|=p),p=p<<1}}this._currentMaterialContext.textureState=l;const d=this._cacheRenderPipeline.getRenderPipeline(t,this._currentEffect,this.currentSampleCount,l),g=this._cacheBindGroups.getBindGroups(u,this._currentDrawContext,this._currentMaterialContext);this._snapshotRendering.record||(this._applyRenderPassChanges(this.compatibilityMode?null:o),this.compatibilityMode||(this._counters.numBundleCreationNonCompatMode++,c=this._device.createRenderBundleEncoder({colorFormats:this._cacheRenderPipeline.colorFormats,depthStencilFormat:this._depthTextureFormat,sampleCount:C.GetSample(this.currentSampleCount)}))),c.setPipeline(d),this._currentIndexBuffer&&c.setIndexBuffer(this._currentIndexBuffer.underlyingResource,this._currentIndexBuffer.is32Bits?"uint32":"uint16",0);const f=this._cacheRenderPipeline.vertexBuffers;for(let p=0;p<f.length;p++){const m=f[p],x=m.effectiveBuffer;x&&c.setVertexBuffer(p,x.underlyingResource,m._validOffsetRange?0:m.byteOffset)}for(let p=0;p<g.length;p++)c.setBindGroup(p,g[p]);const _=!this.compatibilityMode&&!this._snapshotRendering.record;_&&this._currentDrawContext.indirectDrawBuffer?(this._currentDrawContext.setIndirectData(n,i||1,r),e===0?c.drawIndexedIndirect(this._currentDrawContext.indirectDrawBuffer,0):c.drawIndirect(this._currentDrawContext.indirectDrawBuffer,0)):e===0?c.drawIndexed(n,i||1,r,0,0):c.draw(n,i||1,r,0),_&&(this._currentDrawContext.fastBundle=c.finish(),o.addBundle(this._currentDrawContext.fastBundle)),this._reportDrawCall()}drawElementsType(e,t,r,n=1){this._draw(0,e,t,r,n)}drawArraysType(e,t,r,n=1){this._currentIndexBuffer=null,this._draw(1,e,t,r,n)}dispose(){this._isDisposed=!0,this.hideLoadingUI(),this._timestampQuery.dispose(),this._mainTexture?.destroy(),this._depthTexture?.destroy(),this._textureHelper.destroyDeferredTextures(),this._bufferManager.destroyDeferredBuffers(),this._device.destroy(),zt(this,this._renderingCanvas),super.dispose()}getRenderWidth(e=!1){return!e&&this._currentRenderTarget?this._currentRenderTarget.width:this._renderingCanvas?.width??0}getRenderHeight(e=!1){return!e&&this._currentRenderTarget?this._currentRenderTarget.height:this._renderingCanvas?.height??0}getError(){return 0}createExternalTexture(e){return new Er(e)}setExternalTexture(e,t){if(!t){this._currentMaterialContext.setTexture(e,null);return}this._setInternalTexture(e,t)}setTextureSampler(e,t){this._currentMaterialContext?.setSampler(e,t)}createStorageBuffer(e,t,r){return this._createBuffer(e,t|32,r)}updateStorageBuffer(e,t,r,n){const i=e;r===void 0&&(r=0);let a;n===void 0?(t instanceof Array?a=new Float32Array(t):t instanceof ArrayBuffer?a=new Uint8Array(t):a=t,n=a.byteLength):t instanceof Array?a=new Float32Array(t):t instanceof ArrayBuffer?a=new Uint8Array(t):a=t,this._bufferManager.setSubData(i,r,a,0,n)}_readFromGPUBuffer(e,t,r,n){return new Promise((i,a)=>{const o=()=>{e.mapAsync(1,0,t).then(()=>{const u=e.getMappedRange(0,t);let h=r;if(h===void 0)h=new Uint8Array(t),h.set(new Uint8Array(u));else{const c=h.constructor;h=new c(h.buffer),h.set(new c(u))}e.unmap(),this._bufferManager.releaseBuffer(e),i(h)},u=>{this.isDisposed?i(new Uint8Array):a(u)})};n?(this.flushFramebuffer(),o()):this.onEndFrameObservable.addOnce(()=>{o()})})}readFromStorageBuffer(e,t,r,n,i){r=r||e.capacity;const a=this._bufferManager.createRawBuffer(r,T.MapRead|T.CopyDst,void 0,"TempReadFromStorageBuffer");return this._renderEncoder.copyBufferToBuffer(e.underlyingResource,t??0,a,0,r),this._readFromGPUBuffer(a,r,n,i)}readFromMultipleStorageBuffers(e,t,r,n,i){r=r||e[0].capacity;const a=this._bufferManager.createRawBuffer(r*e.length,T.MapRead|T.CopyDst,void 0,"TempReadFromMultipleStorageBuffers");for(let o=0;o<e.length;o++)this._renderEncoder.copyBufferToBuffer(e[o].underlyingResource,t??0,a,o*r,r);return this._readFromGPUBuffer(a,r*e.length,n,i)}setStorageBuffer(e,t){this._currentDrawContext?.setBuffer(e,t?.getBuffer()??null)}}z._GlslangDefaultOptions={jsPath:`${q._DefaultCdnUrl}/glslang/glslang.js`,wasmPath:`${q._DefaultCdnUrl}/glslang/glslang.wasm`};z._InstanceId=0;export{z as WebGPUEngine};
//# sourceMappingURL=webgpuEngine-BbC0XIIF.js.map
