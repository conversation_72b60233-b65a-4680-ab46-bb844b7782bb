const{SvelteComponent:l,append:v,attr:e,detach:c,init:h,insert:p,noop:r,safe_not_equal:d,svg_element:o}=window.__gradio__svelte__internal;function w(s){let t,n;return{c(){t=o("svg"),n=o("path"),e(n,"fill","currentColor"),e(n,"d","M4 2H2v26a2 2 0 0 0 2 2h26v-2H4v-3h22v-8H4v-4h14V5H4Zm20 17v4H4v-4ZM16 7v4H4V7Z"),e(t,"xmlns","http://www.w3.org/2000/svg"),e(t,"xmlns:xlink","http://www.w3.org/1999/xlink"),e(t,"aria-hidden","true"),e(t,"role","img"),e(t,"class","iconify iconify--carbon"),e(t,"width","100%"),e(t,"height","100%"),e(t,"preserveAspectRatio","xMidYMid meet"),e(t,"viewBox","0 0 32 32")},m(i,a){p(i,t,a),v(t,n)},p:r,i:r,o:r,d(i){i&&c(t)}}}class g extends l{constructor(t){super(),h(this,t,null,w,d,{})}}export{g as L};
//# sourceMappingURL=LineChart-CKh1Fdep.js.map
