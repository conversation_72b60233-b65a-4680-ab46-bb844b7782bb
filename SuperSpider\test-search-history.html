<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索历史记录测试</title>
    <link rel="stylesheet" href="frontend/static/css/style.css">
    <link rel="stylesheet" href="frontend/static/css/downloads.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>搜索历史记录功能测试</h1>
        
        <div class="test-section">
            <h2>测试按钮</h2>
            <button class="test-button" onclick="testShowModal()">显示搜索历史模态框</button>
            <button class="test-button" onclick="testAPICall()">测试API调用</button>
            <button class="test-button" onclick="testLoginStatus()">检查登录状态</button>
            <button class="test-button" onclick="clearDebugInfo()">清空调试信息</button>
        </div>

        <div class="debug-section">
            <h2>调试信息</h2>
            <div id="debug-info" class="debug-info">等待测试...</div>
        </div>
    </div>

    <!-- 搜索历史记录模态框 -->
    <div class="modal" id="search-history-modal">
        <div class="modal-overlay"></div>
        <div class="modal-container modal-large">
            <div class="modal-header">
                <h2>搜索历史记录</h2>
                <div class="modal-header-actions">
                    <button id="export-search-records-btn" class="btn btn-secondary" title="导出搜索记录">
                        <i class="fas fa-file-export"></i> 导出
                    </button>
                    <button class="close-modal"><i class="fas fa-times"></i></button>
                </div>
            </div>
            <div class="modal-body">
                <div class="search-history-container">
                    <!-- 搜索统计 -->
                    <div class="search-stats" id="search-stats">
                        <div class="stat-item">
                            <div class="stat-value">0</div>
                            <div class="stat-label">总搜索数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">0</div>
                            <div class="stat-label">最近7天</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">0</div>
                            <div class="stat-label">收藏</div>
                        </div>
                    </div>

                    <!-- 搜索列表 -->
                    <div class="search-records-list-container">
                        <!-- 加载中 -->
                        <div id="search-records-loading" class="loading-container" style="display: none;">
                            <div class="loading-spinner"></div>
                            <p>加载中...</p>
                        </div>

                        <!-- 空状态 -->
                        <div id="search-records-empty" class="empty-container" style="display: none;">
                            <i class="fas fa-inbox"></i>
                            <p>暂无搜索记录</p>
                        </div>

                        <!-- 搜索列表 -->
                        <div id="search-records-list"></div>

                        <!-- 分页 -->
                        <div id="search-records-pagination"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="frontend/static/js/search-history.js"></script>
    <script>
        // 模拟登录状态
        window.isLoggedIn = true;
        window.currentUser = {
            id: 1,
            username: 'testuser'
        };

        function addDebugInfo(message) {
            const debugDiv = document.getElementById('debug-info');
            const timestamp = new Date().toLocaleTimeString();
            debugDiv.textContent += `[${timestamp}] ${message}\n`;
            debugDiv.scrollTop = debugDiv.scrollHeight;
        }

        function clearDebugInfo() {
            document.getElementById('debug-info').textContent = '调试信息已清空\n';
        }

        function testShowModal() {
            addDebugInfo('测试显示搜索历史模态框...');
            try {
                showSearchHistoryModal();
                addDebugInfo('✓ 模态框显示成功');
            } catch (error) {
                addDebugInfo('✗ 模态框显示失败: ' + error.message);
            }
        }

        function testAPICall() {
            addDebugInfo('测试API调用...');
            fetch('/api/search/history?page=1&per_page=10')
                .then(response => {
                    addDebugInfo(`API响应状态: ${response.status}`);
                    return response.json();
                })
                .then(data => {
                    addDebugInfo('API响应数据: ' + JSON.stringify(data, null, 2));
                })
                .catch(error => {
                    addDebugInfo('✗ API调用失败: ' + error.message);
                });
        }

        function testLoginStatus() {
            addDebugInfo('检查登录状态...');
            addDebugInfo(`登录状态: ${window.isLoggedIn}`);
            addDebugInfo(`当前用户: ${JSON.stringify(window.currentUser)}`);
        }

        // 重写console.log来显示在页面上
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addDebugInfo('Console: ' + args.join(' '));
        };

        const originalConsoleError = console.error;
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addDebugInfo('Error: ' + args.join(' '));
        };

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            addDebugInfo('页面加载完成，开始初始化...');
            try {
                initSearchHistoryFeatures();
                addDebugInfo('✓ 搜索历史功能初始化成功');
            } catch (error) {
                addDebugInfo('✗ 初始化失败: ' + error.message);
            }
        });
    </script>
</body>
</html>
