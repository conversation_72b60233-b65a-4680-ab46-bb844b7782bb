import{ar as d,an as c,ao as h}from"./index-Cb4A4-Xi.js";import{GLTFLoader as m}from"./glTFLoader-D-NF4fEj.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./bone-CgNwSK5F.js";import"./rawTexture-PjZ4PTsN.js";import"./assetContainer-CIn78ZXO.js";import"./objectModelMapping-D3Nr8hfO.js";const t="MSFT_minecraftMesh";class u{constructor(r){this.name=t,this._loader=r,this.enabled=this._loader.isExtensionUsed(t)}dispose(){this._loader=null}loadMaterialPropertiesAsync(r,i,e){return m.LoadExtraAsync(r,i,this.name,(o,n)=>{if(n){if(!(e instanceof d))throw new Error(`${o}: Material type not supported`);const p=this._loader.loadMaterialPropertiesAsync(r,i,e);return e.needAlphaBlending()&&(e.forceDepthWrite=!0,e.separateCullingPass=!0),e.backFaceCulling=e.forceDepthWrite,e.twoSidedLighting=!0,p}return null})}}c(t);h(t,!0,s=>new u(s));export{u as MSFT_minecraftMesh};
//# sourceMappingURL=MSFT_minecraftMesh-Ca_c1AUC.js.map
