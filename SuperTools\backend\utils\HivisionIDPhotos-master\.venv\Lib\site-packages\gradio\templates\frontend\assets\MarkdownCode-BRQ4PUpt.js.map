{"version": 3, "mappings": ";mIAGA,IAAIA,EAAgB,SAAuBC,EAAWC,EAAMC,EAAY,CAOtE,QAJIC,EAAQD,EACRE,EAAa,EACbC,EAAcL,EAAU,OAErBG,EAAQF,EAAK,QAAQ,CAC1B,IAAIK,EAAYL,EAAKE,CAAK,EAE1B,GAAIC,GAAc,GAAKH,EAAK,MAAME,EAAOA,EAAQE,CAAW,IAAML,EAChE,OAAOG,EACEG,IAAc,KACvBH,IACSG,IAAc,IACvBF,IACSE,IAAc,KACvBF,IAGFD,GACD,CAED,MAAO,EACT,EAEII,EAAc,SAAqBC,EAAQ,CAC7C,OAAOA,EAAO,QAAQ,wBAAyB,MAAM,CACvD,EAEIC,EAAW,YAEXC,EAAoB,SAA2BT,EAAMU,EAAY,CAKnE,QAJIR,EACAS,EAAO,GACPC,EAAY,IAAI,OAAO,IAAMF,EAAW,IAAIG,GAAKP,EAAYO,EAAE,IAAI,CAAC,EAAE,KAAK,GAAG,EAAI,GAAG,EAGvFX,EAAQF,EAAK,OAAOY,CAAS,EAEzBV,IAAU,IAHH,CAOPA,EAAQ,IACVS,EAAK,KAAK,CACR,KAAM,OACN,KAAMX,EAAK,MAAM,EAAGE,CAAK,CACjC,CAAO,EACDF,EAAOA,EAAK,MAAME,CAAK,GAIzB,IAAIY,EAAIJ,EAAW,UAAUK,GAASf,EAAK,WAAWe,EAAM,IAAI,CAAC,EAGjE,GAFAb,EAAQJ,EAAcY,EAAWI,CAAC,EAAE,MAAOd,EAAMU,EAAWI,CAAC,EAAE,KAAK,MAAM,EAEtEZ,IAAU,GACZ,MAGF,IAAIc,EAAUhB,EAAK,MAAM,EAAGE,EAAQQ,EAAWI,CAAC,EAAE,MAAM,MAAM,EAC1DG,EAAOT,EAAS,KAAKQ,CAAO,EAAIA,EAAUhB,EAAK,MAAMU,EAAWI,CAAC,EAAE,KAAK,OAAQZ,CAAK,EACzFS,EAAK,KAAK,CACR,KAAM,OACN,KAAMM,EACN,QAAAD,EACA,QAASN,EAAWI,CAAC,EAAE,OAC7B,CAAK,EACDd,EAAOA,EAAK,MAAME,EAAQQ,EAAWI,CAAC,EAAE,MAAM,MAAM,CACrD,CAED,OAAId,IAAS,IACXW,EAAK,KAAK,CACR,KAAM,OACN,KAAMX,CACZ,CAAK,EAGIW,CACT,EAOIO,EAAmB,SAA0BlB,EAAMmB,EAAa,CAClE,IAAIR,EAAOF,EAAkBT,EAAMmB,EAAY,UAAU,EAEzD,GAAIR,EAAK,SAAW,GAAKA,EAAK,CAAC,EAAE,OAAS,OAIxC,OAAO,KAKT,QAFIS,EAAW,SAAS,yBAEfN,EAAI,EAAGA,EAAIH,EAAK,OAAQG,IAC/B,GAAIH,EAAKG,CAAC,EAAE,OAAS,OACnBM,EAAS,YAAY,SAAS,eAAeT,EAAKG,CAAC,EAAE,IAAI,CAAC,MACrD,CACL,IAAIO,EAAO,SAAS,cAAc,MAAM,EACpCJ,EAAON,EAAKG,CAAC,EAAE,KAGnBK,EAAY,YAAcR,EAAKG,CAAC,EAAE,QAElC,GAAI,CACEK,EAAY,aACdF,EAAOE,EAAY,WAAWF,CAAI,GAGpCK,EAAM,OAAOL,EAAMI,EAAMF,CAAW,CACrC,OAAQI,EAAG,CACV,GAAI,EAAEA,aAAaD,EAAM,YACvB,MAAMC,EAGRJ,EAAY,cAAc,uCAAyCR,EAAKG,CAAC,EAAE,KAAO,UAAWS,CAAC,EAC9FH,EAAS,YAAY,SAAS,eAAeT,EAAKG,CAAC,EAAE,OAAO,CAAC,EAC7D,QACD,CAEDM,EAAS,YAAYC,CAAI,CAC1B,CAGH,OAAOD,CACT,EAEII,EAAa,SAASA,EAAWC,EAAMN,EAAa,CACtD,QAASL,EAAI,EAAGA,EAAIW,EAAK,WAAW,OAAQX,IAAK,CAC/C,IAAIY,EAAYD,EAAK,WAAWX,CAAC,EAEjC,GAAIY,EAAU,WAAa,EAAG,CAS5B,QAJIC,EAAoBD,EAAU,YAC9BE,EAAUF,EAAU,YACpBG,EAAY,EAETD,GAAWA,EAAQ,WAAa,KAAK,WAC1CD,GAAqBC,EAAQ,YAC7BA,EAAUA,EAAQ,YAClBC,IAGF,IAAIC,EAAOZ,EAAiBS,EAAmBR,CAAW,EAE1D,GAAIW,EAAM,CAER,QAASC,EAAI,EAAGA,EAAIF,EAAWE,IAC7BL,EAAU,YAAY,SAGxBZ,GAAKgB,EAAK,WAAW,OAAS,EAC9BL,EAAK,aAAaK,EAAMJ,CAAS,CACzC,MAGQZ,GAAKe,CAEb,MAAeH,EAAU,WAAa,GAC/B,UAAY,CAEX,IAAIM,EAAY,IAAMN,EAAU,UAAY,IACxCO,EAAed,EAAY,YAAY,QAAQO,EAAU,SAAS,YAAW,CAAE,IAAM,IAAMP,EAAY,eAAe,MAAMN,GAAKmB,EAAU,QAAQ,IAAMnB,EAAI,GAAG,IAAM,EAAE,EAExKoB,GACFT,EAAWE,EAAWP,CAAW,CAE3C,GAGG,CACH,EAEIe,EAAsB,SAA6BT,EAAMU,EAAS,CACpE,GAAI,CAACV,EACH,MAAM,IAAI,MAAM,+BAA+B,EAGjD,IAAIN,EAAc,GAElB,QAASiB,KAAUD,EACbA,EAAQ,eAAeC,CAAM,IAC/BjB,EAAYiB,CAAM,EAAID,EAAQC,CAAM,GAKxCjB,EAAY,WAAaA,EAAY,YAAc,CAAC,CAClD,KAAM,KACN,MAAO,KACP,QAAS,EACb,EAAK,CACD,KAAM,MACN,MAAO,MACP,QAAS,EACV,EAID,CACE,KAAM,oBACN,MAAO,kBACP,QAAS,EACb,EAAK,CACD,KAAM,iBACN,MAAO,eACP,QAAS,EACb,EAAK,CACD,KAAM,mBACN,MAAO,iBACP,QAAS,EACb,EAAK,CACD,KAAM,kBACN,MAAO,gBACP,QAAS,EACb,EAAK,CACD,KAAM,cACN,MAAO,YACP,QAAS,EACb,EAAK,CACD,KAAM,MACN,MAAO,MACP,QAAS,EACb,CAAG,EACDA,EAAY,YAAcA,EAAY,aAAe,CAAC,SAAU,WAAY,QAAS,WAAY,MAAO,OAAQ,QAAQ,EACxHA,EAAY,eAAiBA,EAAY,gBAAkB,GAC3DA,EAAY,cAAgBA,EAAY,eAAiB,QAAQ,MAGjEA,EAAY,OAASA,EAAY,QAAU,GAC3CK,EAAWC,EAAMN,CAAW,CAC9B,EC/OA,MAAMkB,EAAkB,CAACC,EAAqBC,IAA0B,CACnE,IACI,OAAC,CAACD,GAAQ,IAAI,IAAIA,CAAI,EAAE,SAAW,IAAI,IAAIC,CAAI,EAAE,YAC7C,CACJ,QACR,CACD,EAEgB,SAAAC,EAASC,EAAgBF,EAAsB,CACxD,MAAAG,EAAW,IAAIC,EACfC,EAAO,IAAI,YAAY,gBAAgBH,EAAQ,WAAW,EAChE,OAAAI,EAAWD,EAAK,KAAM,IAAMA,GAAS,CAChCA,aAAgB,aAAe,WAAYA,GAC1CP,EAAgBO,EAAK,aAAa,MAAM,EAAGL,CAAI,IAClDK,EAAK,aAAa,SAAU,QAAQ,EACpCA,EAAK,aAAa,MAAO,qBAAqB,EAEhD,CACA,EAEMF,EAAS,SAASE,CAAI,EAAE,KAAK,SACrC,CAEA,SAASC,EACRD,EACAE,EACAC,EACO,CAENH,IAC8BA,EAAK,WAAaE,GAC9C,OAAOA,GAAS,aAElBC,EAASH,CAAI,EAER,MAAAI,EAAWJ,GAAM,YAAc,GACrC,QAAS9B,EAAI,EAAGA,EAAIkC,EAAS,OAAQlC,IAEpC+B,EAAWG,EAASlC,CAAC,EAAGgC,EAAMC,CAAQ,CAExC,CCzCO,MAAME,EAAmB,CAC/B,MACA,WACA,IACA,OACA,UACA,UACA,SACA,OACA,UACA,QACA,QACA,IACA,OACA,WACA,MACA,MACA,MACA,aACA,OACA,KACA,SACA,SACA,UACA,SACA,OACA,OACA,MACA,WACA,OACA,WACA,KACA,MACA,UACA,MACA,SACA,MACA,MACA,KACA,KACA,KACA,QACA,WACA,aACA,SACA,OACA,SACA,OACA,QACA,WACA,KACA,KACA,KACA,KACA,KACA,KACA,OACA,SACA,SACA,KACA,OACA,IACA,SACA,MACA,QACA,MACA,MACA,QACA,SACA,KACA,OACA,OACA,MACA,OACA,OACA,OACA,QACA,MACA,WACA,WACA,SACA,KACA,WACA,SACA,SACA,IACA,QACA,UACA,MACA,WACA,IACA,KACA,KACA,OACA,IACA,OACA,SACA,SACA,UACA,SACA,QACA,SACA,OACA,SACA,SACA,QACA,MACA,UACA,MACA,MACA,QACA,QACA,KACA,WACA,WACA,QACA,KACA,QACA,OACA,QACA,KACA,QACA,KACA,IACA,KACA,MACA,QACA,KACD,EAGaC,EAAU,CAEtB,IACA,OACA,MACA,SAGA,OACA,SACA,UACA,OACA,WACA,UACA,OACA,QAGA,OACA,QACA,WAGA,iBACA,iBACA,OACA,UACA,WACA,OACA,SAGA,UACA,gBACA,sBACA,cACA,mBACA,oBACA,oBACA,iBACA,UACA,eACA,WACA,qBACA,eACA,cACA,UACA,UACA,UACA,UACA,iBACA,eACA,cACA,UACA,SAGA,UACA,mBACA,gBACA,QACA,MAGA,OACA,SACA,gBACA,OACA,QACA,WACA,QACD,EAEaC,EAAyB,CACrC,GAAGF,EACH,GAAGC,EAAQ,OAAQE,GAAQ,CAACH,EAAiB,SAASG,CAAG,CAAC,CAC3D,yKC/MU,aAAAC,GAAA,KAAAC,aAAkC,iJA0JeC,EAAe,YAA1EC,GAEMC,EAAApC,EAAAqC,CAAA,cADEH,EAAI,wCAAJA,EAAI,8CAD+CA,EAAe,iDA1HhEI,EAAapD,EAAA,QACdA,EAAO,QAAQ,sBAAuB,MAAM,yBAxBzC,QAAAqD,EAAU,IAAAC,EACV,SAAAC,CAAA,EAAAD,GACA,cAAAE,EAAgB,IAAAF,EAChB,kBAAAG,EAAA,IAAAH,GAKA,gBAAAI,EAAkB,IAAAJ,GAClB,YAAAK,EAAc,IAAAL,GACd,aAAAM,EAAe,IAAAN,EACf,MAAAtB,CAAA,EAAAsB,GACA,WAAAO,EAAiC,IAAAP,GACjC,WAAAQ,EAAwB,UAAAR,EAC/BS,EACAC,QAEEC,EAASC,EAAA,CACd,aAAAN,EACA,YAAAD,EACA,iBAAkBF,GAAA,KAOV,SAAAU,EACRC,EACAC,EAAA,IAEIA,IAAiB,UAEdC,EAAW,6CACVF,EAAQ,QAAQE,EAAW,CAAAC,EAAOC,EAASC,IAC5C7B,EAAuB,SAAS4B,EAAQ,eAGtCD,EAFCA,EAAM,QAAQ,KAAM,MAAM,EAAE,QAAQ,KAAM,MAAM,GAMtD,SAAM,QAAQF,CAAY,GACvB,MAAAK,EAAaL,EAAa,IAAKxB,IAAA,CACpC,KAAU,gBAAYA,CAAG,iBAAkB,IAAI,EAC/C,MAAW,iBAAaA,CAAG,KAAM,IAAI,SAGlC8B,EAASP,EAEb,OAAAM,EAAW,QAASE,GAAA,CACnBD,EAASA,EAAO,QAAQC,EAAQ,KAAOL,GACtCA,EAAM,QAAQ,KAAM,MAAM,EAAE,QAAQ,KAAM,MAAM,GAEjDI,EAASA,EAAO,QAAQC,EAAQ,MAAQL,GACvCA,EAAM,QAAQ,KAAM,MAAM,EAAE,QAAQ,KAAM,MAAM,KAG3CI,EAED,OAAAP,WAGCS,EAAgBC,EAAA,KACpBC,EAAcD,EACd,GAAApB,EAAA,CACG,MAAAsB,EAAA,GACNvB,EAAiB,SAASjE,EAAWG,IAAA,OAC9BsF,EAAgB7B,EAAa5D,EAAU,IAAI,EAC3C0F,EAAiB9B,EAAa5D,EAAU,KAAK,EAC7C2F,EAAA,IAAY,OACd,GAAAF,CAAa,eAAeC,CAAc,GAC7C,KAEDH,EAAcA,EAAY,QAAQI,EAAA,CAAQZ,EAAOa,MAChDJ,EAAY,KAAKT,CAAK,EACG,kBAAAS,EAAY,OAAS,CAAC,UAIjDD,EAAcd,EAAO,MAAMc,CAAW,EAEtCA,EAAcA,EAAY,QACzB,2BACC,CAAAR,EAAOa,IAAOJ,EAAY,SAASI,EAAI,EAAE,IAIxC,OAAAvB,IACHkB,EAAcZ,EAAWY,EAAalB,CAAU,GAG7CL,GAAiBvB,IACpB8C,EAAc9C,EAAS8C,EAAa/C,CAAI,GAElC+C,iBASOM,EAAYP,EAAA,CActB,GAbArB,EAAiB,OAAS,GAAKqB,GACRrB,EAAiB,KACzCjE,GACAsF,EAAM,SAAStF,EAAU,IAAI,GAAKsF,EAAM,SAAStF,EAAU,KAAK,IAGjE8F,EAAuBvB,EAAA,CACtB,WAAYN,EACZ,aAAc,KAKbM,EAAA,OACGwB,EAAcxB,EAAG,iBAAiB,UAAU,EAC9C,GAAAwB,EAAY,OAAS,GAClB,MAAAxC,GAAA,EACE,cAASyC,sBAAyB,4BAAS,OAAAC,KAAA,oDAEnDD,EAAQ,YACP,YAAa,GACb,MAAO1B,IAAe,OAAS,OAAS,UACxC,cAAe,qBAEV0B,EAAQ,KACb,MAAO,MAAM,KAAKD,CAAW,EAAE,IAAKlD,GAASA,CAAmB,MAMpES,GAAA,UACKiB,GAAM,SAAS,KAAK,SAASA,CAAE,EAC5B,MAAAsB,EAAY9B,CAAO,EAEzB,QAAQ,MAAM,2BAA2B,6CAKbQ,EAAE2B,wcA/CzBnC,GAAWA,EAAQ,OACzBoC,EAAA,EAAA3B,EAAOa,EAAgBtB,CAAO,OAE9BS,EAAO", "names": ["findEndOfMath", "delimiter", "text", "startIndex", "index", "braceLevel", "delimLength", "character", "escapeRegex", "string", "amsRegex", "splitAtDelimiters", "delimiters", "data", "regexLeft", "x", "i", "delim", "rawData", "math", "renderMathInText", "optionsCopy", "fragment", "span", "katex", "e", "renderElem", "elem", "childNode", "textContentConcat", "sibling", "nSiblings", "frag", "j", "className", "shouldRender", "renderMathInElement", "options", "option", "is_external_url", "link", "root", "sanitize", "source", "<PERSON><PERSON>na", "<PERSON><PERSON><PERSON>", "node", "walk_nodes", "test", "callback", "children", "standardHtmlTags", "svgTags", "standardHtmlAndSvgTags", "tag", "afterUpdate", "tick", "ctx", "insert", "target", "anchor", "escapeRegExp", "chatbot", "$$props", "message", "sanitize_html", "latex_delimiters", "render_markdown", "line_breaks", "header_links", "allow_tags", "theme_mode", "el", "html", "marked", "create_marked", "escapeTags", "content", "tagsToEscape", "tagRegex", "match", "tagName", "endChar", "tagPattern", "result", "pattern", "process_message", "value", "parsedValue", "latexBlocks", "leftDelimiter", "rightDelimiter", "regex", "p1", "render_html", "render_math_in_element", "mermaidDivs", "mermaid", "n", "$$value", "$$invalidate"], "ignoreList": [0], "sources": ["../../../../node_modules/.pnpm/katex@0.16.10/node_modules/katex/dist/contrib/auto-render.mjs", "../../../../js/sanitize/browser.ts", "../../../../js/markdown-code/html-tags.ts", "../../../../js/markdown-code/MarkdownCode.svelte"], "sourcesContent": ["import katex from '../katex.mjs';\n\n/* eslint no-constant-condition:0 */\nvar findEndOfMath = function findEndOfMath(delimiter, text, startIndex) {\n  // Adapted from\n  // https://github.com/Khan/perseus/blob/master/src/perseus-markdown.jsx\n  var index = startIndex;\n  var braceLevel = 0;\n  var delimLength = delimiter.length;\n\n  while (index < text.length) {\n    var character = text[index];\n\n    if (braceLevel <= 0 && text.slice(index, index + delimLength) === delimiter) {\n      return index;\n    } else if (character === \"\\\\\") {\n      index++;\n    } else if (character === \"{\") {\n      braceLevel++;\n    } else if (character === \"}\") {\n      braceLevel--;\n    }\n\n    index++;\n  }\n\n  return -1;\n};\n\nvar escapeRegex = function escapeRegex(string) {\n  return string.replace(/[-/\\\\^$*+?.()|[\\]{}]/g, \"\\\\$&\");\n};\n\nvar amsRegex = /^\\\\begin{/;\n\nvar splitAtDelimiters = function splitAtDelimiters(text, delimiters) {\n  var index;\n  var data = [];\n  var regexLeft = new RegExp(\"(\" + delimiters.map(x => escapeRegex(x.left)).join(\"|\") + \")\");\n\n  while (true) {\n    index = text.search(regexLeft);\n\n    if (index === -1) {\n      break;\n    }\n\n    if (index > 0) {\n      data.push({\n        type: \"text\",\n        data: text.slice(0, index)\n      });\n      text = text.slice(index); // now text starts with delimiter\n    } // ... so this always succeeds:\n\n\n    var i = delimiters.findIndex(delim => text.startsWith(delim.left));\n    index = findEndOfMath(delimiters[i].right, text, delimiters[i].left.length);\n\n    if (index === -1) {\n      break;\n    }\n\n    var rawData = text.slice(0, index + delimiters[i].right.length);\n    var math = amsRegex.test(rawData) ? rawData : text.slice(delimiters[i].left.length, index);\n    data.push({\n      type: \"math\",\n      data: math,\n      rawData,\n      display: delimiters[i].display\n    });\n    text = text.slice(index + delimiters[i].right.length);\n  }\n\n  if (text !== \"\") {\n    data.push({\n      type: \"text\",\n      data: text\n    });\n  }\n\n  return data;\n};\n\n/* eslint no-console:0 */\n/* Note: optionsCopy is mutated by this method. If it is ever exposed in the\n * API, we should copy it before mutating.\n */\n\nvar renderMathInText = function renderMathInText(text, optionsCopy) {\n  var data = splitAtDelimiters(text, optionsCopy.delimiters);\n\n  if (data.length === 1 && data[0].type === 'text') {\n    // There is no formula in the text.\n    // Let's return null which means there is no need to replace\n    // the current text node with a new one.\n    return null;\n  }\n\n  var fragment = document.createDocumentFragment();\n\n  for (var i = 0; i < data.length; i++) {\n    if (data[i].type === \"text\") {\n      fragment.appendChild(document.createTextNode(data[i].data));\n    } else {\n      var span = document.createElement(\"span\");\n      var math = data[i].data; // Override any display mode defined in the settings with that\n      // defined by the text itself\n\n      optionsCopy.displayMode = data[i].display;\n\n      try {\n        if (optionsCopy.preProcess) {\n          math = optionsCopy.preProcess(math);\n        }\n\n        katex.render(math, span, optionsCopy);\n      } catch (e) {\n        if (!(e instanceof katex.ParseError)) {\n          throw e;\n        }\n\n        optionsCopy.errorCallback(\"KaTeX auto-render: Failed to parse `\" + data[i].data + \"` with \", e);\n        fragment.appendChild(document.createTextNode(data[i].rawData));\n        continue;\n      }\n\n      fragment.appendChild(span);\n    }\n  }\n\n  return fragment;\n};\n\nvar renderElem = function renderElem(elem, optionsCopy) {\n  for (var i = 0; i < elem.childNodes.length; i++) {\n    var childNode = elem.childNodes[i];\n\n    if (childNode.nodeType === 3) {\n      // Text node\n      // Concatenate all sibling text nodes.\n      // Webkit browsers split very large text nodes into smaller ones,\n      // so the delimiters may be split across different nodes.\n      var textContentConcat = childNode.textContent;\n      var sibling = childNode.nextSibling;\n      var nSiblings = 0;\n\n      while (sibling && sibling.nodeType === Node.TEXT_NODE) {\n        textContentConcat += sibling.textContent;\n        sibling = sibling.nextSibling;\n        nSiblings++;\n      }\n\n      var frag = renderMathInText(textContentConcat, optionsCopy);\n\n      if (frag) {\n        // Remove extra text nodes\n        for (var j = 0; j < nSiblings; j++) {\n          childNode.nextSibling.remove();\n        }\n\n        i += frag.childNodes.length - 1;\n        elem.replaceChild(frag, childNode);\n      } else {\n        // If the concatenated text does not contain math\n        // the siblings will not either\n        i += nSiblings;\n      }\n    } else if (childNode.nodeType === 1) {\n      (function () {\n        // Element node\n        var className = ' ' + childNode.className + ' ';\n        var shouldRender = optionsCopy.ignoredTags.indexOf(childNode.nodeName.toLowerCase()) === -1 && optionsCopy.ignoredClasses.every(x => className.indexOf(' ' + x + ' ') === -1);\n\n        if (shouldRender) {\n          renderElem(childNode, optionsCopy);\n        }\n      })();\n    } // Otherwise, it's something else, and ignore it.\n\n  }\n};\n\nvar renderMathInElement = function renderMathInElement(elem, options) {\n  if (!elem) {\n    throw new Error(\"No element provided to render\");\n  }\n\n  var optionsCopy = {}; // Object.assign(optionsCopy, option)\n\n  for (var option in options) {\n    if (options.hasOwnProperty(option)) {\n      optionsCopy[option] = options[option];\n    }\n  } // default options\n\n\n  optionsCopy.delimiters = optionsCopy.delimiters || [{\n    left: \"$$\",\n    right: \"$$\",\n    display: true\n  }, {\n    left: \"\\\\(\",\n    right: \"\\\\)\",\n    display: false\n  }, // LaTeX uses $…$, but it ruins the display of normal `$` in text:\n  // {left: \"$\", right: \"$\", display: false},\n  // $ must come after $$\n  // Render AMS environments even if outside $$…$$ delimiters.\n  {\n    left: \"\\\\begin{equation}\",\n    right: \"\\\\end{equation}\",\n    display: true\n  }, {\n    left: \"\\\\begin{align}\",\n    right: \"\\\\end{align}\",\n    display: true\n  }, {\n    left: \"\\\\begin{alignat}\",\n    right: \"\\\\end{alignat}\",\n    display: true\n  }, {\n    left: \"\\\\begin{gather}\",\n    right: \"\\\\end{gather}\",\n    display: true\n  }, {\n    left: \"\\\\begin{CD}\",\n    right: \"\\\\end{CD}\",\n    display: true\n  }, {\n    left: \"\\\\[\",\n    right: \"\\\\]\",\n    display: true\n  }];\n  optionsCopy.ignoredTags = optionsCopy.ignoredTags || [\"script\", \"noscript\", \"style\", \"textarea\", \"pre\", \"code\", \"option\"];\n  optionsCopy.ignoredClasses = optionsCopy.ignoredClasses || [];\n  optionsCopy.errorCallback = optionsCopy.errorCallback || console.error; // Enable sharing of global macros defined via `\\gdef` between different\n  // math elements within a single call to `renderMathInElement`.\n\n  optionsCopy.macros = optionsCopy.macros || {};\n  renderElem(elem, optionsCopy);\n};\n\nexport { renderMathInElement as default };\n", "import Amuchina from \"amuchina\";\n\nconst is_external_url = (link: string | null, root: string): boolean => {\n\ttry {\n\t\treturn !!link && new URL(link).origin !== new URL(root).origin;\n\t} catch (e) {\n\t\treturn false;\n\t}\n};\n\nexport function sanitize(source: string, root: string): string {\n\tconst amuchina = new Amuchina();\n\tconst node = new DOMParser().parseFromString(source, \"text/html\");\n\twalk_nodes(node.body, \"A\", (node) => {\n\t\tif (node instanceof HTMLElement && \"target\" in node) {\n\t\t\tif (is_external_url(node.getAttribute(\"href\"), root)) {\n\t\t\t\tnode.setAttribute(\"target\", \"_blank\");\n\t\t\t\tnode.setAttribute(\"rel\", \"noopener noreferrer\");\n\t\t\t}\n\t\t}\n\t});\n\n\treturn amuchina.sanitize(node).body.innerHTML;\n}\n\nfunction walk_nodes(\n\tnode: Node | null | HTMLElement,\n\ttest: string | ((node: Node | HTMLElement) => boolean),\n\tcallback: (node: Node | HTMLElement) => void\n): void {\n\tif (\n\t\tnode &&\n\t\t((typeof test === \"string\" && node.nodeName === test) ||\n\t\t\t(typeof test === \"function\" && test(node)))\n\t) {\n\t\tcallback(node);\n\t}\n\tconst children = node?.childNodes || [];\n\tfor (let i = 0; i < children.length; i++) {\n\t\t// @ts-ignore\n\t\twalk_nodes(children[i], test, callback);\n\t}\n}\n", "// https://www.w3schools.com/tags/\nexport const standardHtmlTags = [\n\t\"!--\",\n\t\"!doctype\",\n\t\"a\",\n\t\"abbr\",\n\t\"acronym\",\n\t\"address\",\n\t\"applet\",\n\t\"area\",\n\t\"article\",\n\t\"aside\",\n\t\"audio\",\n\t\"b\",\n\t\"base\",\n\t\"basefont\",\n\t\"bdi\",\n\t\"bdo\",\n\t\"big\",\n\t\"blockquote\",\n\t\"body\",\n\t\"br\",\n\t\"button\",\n\t\"canvas\",\n\t\"caption\",\n\t\"center\",\n\t\"cite\",\n\t\"code\",\n\t\"col\",\n\t\"colgroup\",\n\t\"data\",\n\t\"datalist\",\n\t\"dd\",\n\t\"del\",\n\t\"details\",\n\t\"dfn\",\n\t\"dialog\",\n\t\"dir\",\n\t\"div\",\n\t\"dl\",\n\t\"dt\",\n\t\"em\",\n\t\"embed\",\n\t\"fieldset\",\n\t\"figcaption\",\n\t\"figure\",\n\t\"font\",\n\t\"footer\",\n\t\"form\",\n\t\"frame\",\n\t\"frameset\",\n\t\"h1\",\n\t\"h2\",\n\t\"h3\",\n\t\"h4\",\n\t\"h5\",\n\t\"h6\",\n\t\"head\",\n\t\"header\",\n\t\"hgroup\",\n\t\"hr\",\n\t\"html\",\n\t\"i\",\n\t\"iframe\",\n\t\"img\",\n\t\"input\",\n\t\"ins\",\n\t\"kbd\",\n\t\"label\",\n\t\"legend\",\n\t\"li\",\n\t\"link\",\n\t\"main\",\n\t\"map\",\n\t\"mark\",\n\t\"menu\",\n\t\"meta\",\n\t\"meter\",\n\t\"nav\",\n\t\"noframes\",\n\t\"noscript\",\n\t\"object\",\n\t\"ol\",\n\t\"optgroup\",\n\t\"option\",\n\t\"output\",\n\t\"p\",\n\t\"param\",\n\t\"picture\",\n\t\"pre\",\n\t\"progress\",\n\t\"q\",\n\t\"rp\",\n\t\"rt\",\n\t\"ruby\",\n\t\"s\",\n\t\"samp\",\n\t\"script\",\n\t\"search\",\n\t\"section\",\n\t\"select\",\n\t\"small\",\n\t\"source\",\n\t\"span\",\n\t\"strike\",\n\t\"strong\",\n\t\"style\",\n\t\"sub\",\n\t\"summary\",\n\t\"sup\",\n\t\"svg\",\n\t\"table\",\n\t\"tbody\",\n\t\"td\",\n\t\"template\",\n\t\"textarea\",\n\t\"tfoot\",\n\t\"th\",\n\t\"thead\",\n\t\"time\",\n\t\"title\",\n\t\"tr\",\n\t\"track\",\n\t\"tt\",\n\t\"u\",\n\t\"ul\",\n\t\"var\",\n\t\"video\",\n\t\"wbr\"\n];\n\n// SVG tags\nexport const svgTags = [\n\t// Base structural elements\n\t\"g\",\n\t\"defs\",\n\t\"use\",\n\t\"symbol\",\n\n\t// Shape elements\n\t\"rect\",\n\t\"circle\",\n\t\"ellipse\",\n\t\"line\",\n\t\"polyline\",\n\t\"polygon\",\n\t\"path\",\n\t\"image\",\n\n\t// Text elements\n\t\"text\",\n\t\"tspan\",\n\t\"textPath\",\n\n\t// Gradient and effects\n\t\"linearGradient\",\n\t\"radialGradient\",\n\t\"stop\",\n\t\"pattern\",\n\t\"clipPath\",\n\t\"mask\",\n\t\"filter\",\n\n\t// Filter effects\n\t\"feBlend\",\n\t\"feColorMatrix\",\n\t\"feComponentTransfer\",\n\t\"feComposite\",\n\t\"feConvolveMatrix\",\n\t\"feDiffuseLighting\",\n\t\"feDisplacementMap\",\n\t\"feGaussianBlur\",\n\t\"feMerge\",\n\t\"feMorphology\",\n\t\"feOffset\",\n\t\"feSpecularLighting\",\n\t\"feTurbulence\",\n\t\"feMergeNode\",\n\t\"feFuncR\",\n\t\"feFuncG\",\n\t\"feFuncB\",\n\t\"feFuncA\",\n\t\"feDistantLight\",\n\t\"fePointLight\",\n\t\"feSpotLight\",\n\t\"feFlood\",\n\t\"feTile\",\n\n\t// Animation elements\n\t\"animate\",\n\t\"animateTransform\",\n\t\"animateMotion\",\n\t\"mpath\",\n\t\"set\",\n\n\t// Interactive and other elements\n\t\"view\",\n\t\"cursor\",\n\t\"foreignObject\",\n\t\"desc\",\n\t\"title\",\n\t\"metadata\",\n\t\"switch\"\n];\n\nexport const standardHtmlAndSvgTags = [\n\t...standardHtmlTags,\n\t...svgTags.filter((tag) => !standardHtmlTags.includes(tag))\n];\n", "<script lang=\"ts\">\n\timport { afterUpdate, tick, onMount } from \"svelte\";\n\timport render_math_in_element from \"katex/contrib/auto-render\";\n\timport \"katex/dist/katex.min.css\";\n\timport { create_marked } from \"./utils\";\n\timport { sanitize } from \"@gradio/sanitize\";\n\timport \"./prism.css\";\n\timport { standardHtmlAndSvgTags } from \"./html-tags\";\n\timport type { ThemeMode } from \"@gradio/core\";\n\n\texport let chatbot = true;\n\texport let message: string;\n\texport let sanitize_html = true;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[] = [];\n\texport let render_markdown = true;\n\texport let line_breaks = true;\n\texport let header_links = false;\n\texport let root: string;\n\texport let allow_tags: string[] | boolean = false;\n\texport let theme_mode: ThemeMode = \"system\";\n\tlet el: HTMLSpanElement;\n\tlet html: string;\n\n\tconst marked = create_marked({\n\t\theader_links,\n\t\tline_breaks,\n\t\tlatex_delimiters: latex_delimiters || []\n\t});\n\n\tfunction escapeRegExp(string: string): string {\n\t\treturn string.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n\t}\n\n\tfunction escapeTags(\n\t\tcontent: string,\n\t\ttagsToEscape: string[] | boolean\n\t): string {\n\t\tif (tagsToEscape === true) {\n\t\t\t// https://www.w3schools.com/tags/\n\t\t\tconst tagRegex = /<\\/?([a-zA-Z][a-zA-Z0-9-]*)([\\s>])/g;\n\t\t\treturn content.replace(tagRegex, (match, tagName, endChar) => {\n\t\t\t\tif (!standardHtmlAndSvgTags.includes(tagName.toLowerCase())) {\n\t\t\t\t\treturn match.replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\");\n\t\t\t\t}\n\t\t\t\treturn match;\n\t\t\t});\n\t\t}\n\n\t\tif (Array.isArray(tagsToEscape)) {\n\t\t\tconst tagPattern = tagsToEscape.map((tag) => ({\n\t\t\t\topen: new RegExp(`<(${tag})(\\\\s+[^>]*)?>`, \"gi\"),\n\t\t\t\tclose: new RegExp(`</(${tag})>`, \"gi\")\n\t\t\t}));\n\n\t\t\tlet result = content;\n\n\t\t\ttagPattern.forEach((pattern) => {\n\t\t\t\tresult = result.replace(pattern.open, (match) =>\n\t\t\t\t\tmatch.replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\")\n\t\t\t\t);\n\t\t\t\tresult = result.replace(pattern.close, (match) =>\n\t\t\t\t\tmatch.replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\")\n\t\t\t\t);\n\t\t\t});\n\t\t\treturn result;\n\t\t}\n\t\treturn content;\n\t}\n\n\tfunction process_message(value: string): string {\n\t\tlet parsedValue = value;\n\t\tif (render_markdown) {\n\t\t\tconst latexBlocks: string[] = [];\n\t\t\tlatex_delimiters.forEach((delimiter, index) => {\n\t\t\t\tconst leftDelimiter = escapeRegExp(delimiter.left);\n\t\t\t\tconst rightDelimiter = escapeRegExp(delimiter.right);\n\t\t\t\tconst regex = new RegExp(\n\t\t\t\t\t`${leftDelimiter}([\\\\s\\\\S]+?)${rightDelimiter}`,\n\t\t\t\t\t\"g\"\n\t\t\t\t);\n\t\t\t\tparsedValue = parsedValue.replace(regex, (match, p1) => {\n\t\t\t\t\tlatexBlocks.push(match);\n\t\t\t\t\treturn `%%%LATEX_BLOCK_${latexBlocks.length - 1}%%%`;\n\t\t\t\t});\n\t\t\t});\n\n\t\t\tparsedValue = marked.parse(parsedValue) as string;\n\n\t\t\tparsedValue = parsedValue.replace(\n\t\t\t\t/%%%LATEX_BLOCK_(\\d+)%%%/g,\n\t\t\t\t(match, p1) => latexBlocks[parseInt(p1, 10)]\n\t\t\t);\n\t\t}\n\n\t\tif (allow_tags) {\n\t\t\tparsedValue = escapeTags(parsedValue, allow_tags);\n\t\t}\n\n\t\tif (sanitize_html && sanitize) {\n\t\t\tparsedValue = sanitize(parsedValue, root);\n\t\t}\n\t\treturn parsedValue;\n\t}\n\n\t$: if (message && message.trim()) {\n\t\thtml = process_message(message);\n\t} else {\n\t\thtml = \"\";\n\t}\n\n\tasync function render_html(value: string): Promise<void> {\n\t\tif (latex_delimiters.length > 0 && value) {\n\t\t\tconst containsDelimiter = latex_delimiters.some(\n\t\t\t\t(delimiter) =>\n\t\t\t\t\tvalue.includes(delimiter.left) && value.includes(delimiter.right)\n\t\t\t);\n\t\t\tif (containsDelimiter) {\n\t\t\t\trender_math_in_element(el, {\n\t\t\t\t\tdelimiters: latex_delimiters,\n\t\t\t\t\tthrowOnError: false\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tif (el) {\n\t\t\tconst mermaidDivs = el.querySelectorAll(\".mermaid\");\n\t\t\tif (mermaidDivs.length > 0) {\n\t\t\t\tawait tick();\n\t\t\t\tconst { default: mermaid } = await import(\"mermaid\");\n\n\t\t\t\tmermaid.initialize({\n\t\t\t\t\tstartOnLoad: false,\n\t\t\t\t\ttheme: theme_mode === \"dark\" ? \"dark\" : \"default\",\n\t\t\t\t\tsecurityLevel: \"antiscript\"\n\t\t\t\t});\n\t\t\t\tawait mermaid.run({\n\t\t\t\t\tnodes: Array.from(mermaidDivs).map((node) => node as HTMLElement)\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n\n\tafterUpdate(async () => {\n\t\tif (el && document.body.contains(el)) {\n\t\t\tawait render_html(message);\n\t\t} else {\n\t\t\tconsole.error(\"Element is not in the DOM\");\n\t\t}\n\t});\n</script>\n\n<span class:chatbot bind:this={el} class=\"md\" class:prose={render_markdown}>\n\t{@html html}\n</span>\n\n<style>\n\tspan :global(div[class*=\"code_wrap\"]) {\n\t\tposition: relative;\n\t}\n\n\t/* KaTeX */\n\tspan :global(span.katex) {\n\t\tfont-size: var(--text-lg);\n\t\tdirection: ltr;\n\t}\n\n\tspan :global(div[class*=\"code_wrap\"] > button) {\n\t\tz-index: 1;\n\t\tcursor: pointer;\n\t\tborder-bottom-left-radius: var(--radius-sm);\n\t\tpadding: var(--spacing-md);\n\t\twidth: 25px;\n\t\theight: 25px;\n\t\tposition: absolute;\n\t\tright: 0;\n\t}\n\n\tspan :global(.check) {\n\t\topacity: 0;\n\t\tz-index: var(--layer-top);\n\t\ttransition: opacity 0.2s;\n\t\tbackground: var(--code-background-fill);\n\t\tcolor: var(--body-text-color);\n\t\tposition: absolute;\n\t\ttop: var(--size-1-5);\n\t\tleft: var(--size-1-5);\n\t}\n\n\tspan :global(p:not(:first-child)) {\n\t\tmargin-top: var(--spacing-xxl);\n\t}\n\n\tspan :global(.md-header-anchor) {\n\t\t/* position: absolute; */\n\t\tmargin-left: -25px;\n\t\tpadding-right: 8px;\n\t\tline-height: 1;\n\t\tcolor: var(--body-text-color-subdued);\n\t\topacity: 0;\n\t}\n\n\tspan :global(h1:hover .md-header-anchor),\n\tspan :global(h2:hover .md-header-anchor),\n\tspan :global(h3:hover .md-header-anchor),\n\tspan :global(h4:hover .md-header-anchor),\n\tspan :global(h5:hover .md-header-anchor),\n\tspan :global(h6:hover .md-header-anchor) {\n\t\topacity: 1;\n\t}\n\n\tspan.md :global(.md-header-anchor > svg) {\n\t\tcolor: var(--body-text-color-subdued);\n\t}\n\n\tspan :global(table) {\n\t\tword-break: break-word;\n\t}\n</style>\n"], "file": "assets/MarkdownCode-BRQ4PUpt.js"}