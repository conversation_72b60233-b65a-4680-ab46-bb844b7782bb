{"version": 3, "file": "KHR_xmp_json_ld-Bp9cpoVc.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/KHR_xmp_json_ld.js"], "sourcesContent": ["import { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nconst NAME = \"KHR_xmp_json_ld\";\n/**\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_xmp_json_ld/README.md)\n * @since 5.0.0\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class KHR_xmp_json_ld {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        /**\n         * Defines a number that determines the order the extensions are applied.\n         */\n        this.order = 100;\n        this._loader = loader;\n        this.enabled = this._loader.isExtensionUsed(NAME);\n    }\n    /** @internal */\n    dispose() {\n        this._loader = null;\n    }\n    /**\n     * Called after the loader state changes to LOADING.\n     */\n    onLoading() {\n        if (this._loader.rootBabylonMesh === null) {\n            return;\n        }\n        const xmp_gltf = this._loader.gltf.extensions?.KHR_xmp_json_ld;\n        const xmp_node = this._loader.gltf.asset?.extensions?.KHR_xmp_json_ld;\n        if (xmp_gltf && xmp_node) {\n            const packet = +xmp_node.packet;\n            if (xmp_gltf.packets && packet < xmp_gltf.packets.length) {\n                this._loader.rootBabylonMesh.metadata = this._loader.rootBabylonMesh.metadata || {};\n                this._loader.rootBabylonMesh.metadata.xmp = xmp_gltf.packets[packet];\n            }\n        }\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new KHR_xmp_json_ld(loader));\n//# sourceMappingURL=KHR_xmp_json_ld.js.map"], "names": ["NAME", "KHR_xmp_json_ld", "loader", "xmp_gltf", "xmp_node", "packet", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "wGACA,MAAMA,EAAO,kBAMN,MAAMC,CAAgB,CAIzB,YAAYC,EAAQ,CAIhB,KAAK,KAAOF,EAIZ,KAAK,MAAQ,IACb,KAAK,QAAUE,EACf,KAAK,QAAU,KAAK,QAAQ,gBAAgBF,CAAI,CACnD,CAED,SAAU,CACN,KAAK,QAAU,IAClB,CAID,WAAY,CACR,GAAI,KAAK,QAAQ,kBAAoB,KACjC,OAEJ,MAAMG,EAAW,KAAK,QAAQ,KAAK,YAAY,gBACzCC,EAAW,KAAK,QAAQ,KAAK,OAAO,YAAY,gBACtD,GAAID,GAAYC,EAAU,CACtB,MAAMC,EAAS,CAACD,EAAS,OACrBD,EAAS,SAAWE,EAASF,EAAS,QAAQ,SAC9C,KAAK,QAAQ,gBAAgB,SAAW,KAAK,QAAQ,gBAAgB,UAAY,GACjF,KAAK,QAAQ,gBAAgB,SAAS,IAAMA,EAAS,QAAQE,CAAM,EAE1E,CACJ,CACL,CACAC,EAAwBN,CAAI,EAC5BO,EAAsBP,EAAM,GAAOE,GAAW,IAAID,EAAgBC,CAAM,CAAC", "x_google_ignoreList": [0]}