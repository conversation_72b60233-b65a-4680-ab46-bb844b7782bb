#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库迁移脚本
将 superspider 数据库重命名为 supertools
"""

import os
import sys
import subprocess
import argparse
import getpass
from pathlib import Path

def run_mysql_command(command, user, password, host='localhost', port=3306):
    """执行MySQL命令"""
    try:
        cmd = [
            'mysql',
            f'-h{host}',
            f'-P{port}',
            f'-u{user}',
            f'-p{password}',
            '-e',
            command
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, e.stderr

def backup_database(old_db, user, password, host='localhost', port=3306):
    """备份数据库"""
    backup_file = f"{old_db}_backup.sql"
    
    try:
        cmd = [
            'mysqldump',
            f'-h{host}',
            f'-P{port}',
            f'-u{user}',
            f'-p{password}',
            old_db
        ]
        
        with open(backup_file, 'w', encoding='utf-8') as f:
            result = subprocess.run(cmd, stdout=f, stderr=subprocess.PIPE, text=True, check=True)
        
        print(f"✅ 数据库备份成功: {backup_file}")
        return True, backup_file
    except subprocess.CalledProcessError as e:
        print(f"❌ 数据库备份失败: {e.stderr}")
        return False, None

def restore_database(new_db, backup_file, user, password, host='localhost', port=3306):
    """恢复数据库"""
    try:
        cmd = [
            'mysql',
            f'-h{host}',
            f'-P{port}',
            f'-u{user}',
            f'-p{password}',
            new_db
        ]
        
        with open(backup_file, 'r', encoding='utf-8') as f:
            result = subprocess.run(cmd, stdin=f, stderr=subprocess.PIPE, text=True, check=True)
        
        print(f"✅ 数据库恢复成功: {new_db}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 数据库恢复失败: {e.stderr}")
        return False

def migrate_database(old_db, new_db, user, password, host='localhost', port=3306):
    """迁移数据库"""
    print(f"开始迁移数据库: {old_db} → {new_db}")
    
    # 1. 检查旧数据库是否存在
    success, output = run_mysql_command(f"SHOW DATABASES LIKE '{old_db}';", user, password, host, port)
    if not success:
        print(f"❌ 无法连接到MySQL: {output}")
        return False
    
    if not output.strip():
        print(f"❌ 数据库 {old_db} 不存在")
        return False
    
    print(f"✅ 找到数据库: {old_db}")
    
    # 2. 检查新数据库是否已存在
    success, output = run_mysql_command(f"SHOW DATABASES LIKE '{new_db}';", user, password, host, port)
    if output.strip():
        print(f"⚠️  数据库 {new_db} 已存在")
        confirm = input("是否要覆盖现有数据库？(y/N): ")
        if confirm.lower() != 'y':
            print("❌ 迁移已取消")
            return False
        
        # 删除现有数据库
        success, output = run_mysql_command(f"DROP DATABASE {new_db};", user, password, host, port)
        if not success:
            print(f"❌ 删除现有数据库失败: {output}")
            return False
    
    # 3. 创建新数据库
    create_cmd = f"CREATE DATABASE {new_db} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    success, output = run_mysql_command(create_cmd, user, password, host, port)
    if not success:
        print(f"❌ 创建新数据库失败: {output}")
        return False
    
    print(f"✅ 创建新数据库: {new_db}")
    
    # 4. 备份旧数据库
    success, backup_file = backup_database(old_db, user, password, host, port)
    if not success:
        return False
    
    # 5. 恢复数据到新数据库
    success = restore_database(new_db, backup_file, user, password, host, port)
    if not success:
        return False
    
    # 6. 询问是否删除旧数据库
    print(f"\n数据迁移完成！")
    confirm = input(f"是否删除旧数据库 {old_db}？(y/N): ")
    if confirm.lower() == 'y':
        success, output = run_mysql_command(f"DROP DATABASE {old_db};", user, password, host, port)
        if success:
            print(f"✅ 删除旧数据库: {old_db}")
        else:
            print(f"❌ 删除旧数据库失败: {output}")
    
    # 7. 清理备份文件
    try:
        os.remove(backup_file)
        print(f"✅ 清理备份文件: {backup_file}")
    except:
        print(f"⚠️  请手动删除备份文件: {backup_file}")
    
    print(f"\n🎉 数据库迁移完成: {old_db} → {new_db}")
    return True

def main():
    parser = argparse.ArgumentParser(description='数据库迁移工具')
    parser.add_argument('--host', default='localhost', help='MySQL主机地址')
    parser.add_argument('--port', type=int, default=3306, help='MySQL端口')
    parser.add_argument('--user', default='root', help='MySQL用户名')
    parser.add_argument('--old-db', default='superspider', help='旧数据库名称')
    parser.add_argument('--new-db', default='supertools', help='新数据库名称')
    
    args = parser.parse_args()
    
    # 获取密码
    password = getpass.getpass(f"请输入MySQL用户 {args.user} 的密码: ")
    
    # 执行迁移
    success = migrate_database(
        args.old_db, 
        args.new_db, 
        args.user, 
        password, 
        args.host, 
        args.port
    )
    
    if success:
        print("\n✅ 迁移完成！请重启应用以使用新数据库。")
        sys.exit(0)
    else:
        print("\n❌ 迁移失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
