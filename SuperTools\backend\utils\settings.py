#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SuperTools 配置文件
包含各模块配置项和参数
"""

import os
from pathlib import Path

# 项目根目录
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# 数据目录
DATA_DIR = os.path.join(BASE_DIR, "data")

# 确保数据目录存在
if not os.path.exists(DATA_DIR):
    os.makedirs(DATA_DIR)

# 邮件配置
QQ_EMAIL = os.environ.get("QQ_EMAIL", "<EMAIL>")  # QQ邮箱地址，从环境变量获取
QQ_AUTH_CODE = os.environ.get("QQ_AUTH_CODE", "mkdkeafmoigvbced")  # QQ邮箱授权码，从环境变量获取

# 网站配置

# CSDN配置
CSDN_COOKIE = os.environ.get("CSDN_COOKIE", "fid=20_66632587437-*************-717303; UserName=weixin_46066085; UserInfo=20af1afd822e4c289582ec795b159782; UserToken=20af1afd822e4c289582ec795b159782; UserNick=Appple%E4%B8%B6; AU=50C; UN=weixin_46066085; BT=*************; p_uid=U010000; historyList-new=%5B%5D; dc_sid=8bb247162011e9e1c4df8ccd399116b3; Hm_lvt_6bcd52f51e9b3dce32bec4a3997715ac=**********,**********,**********,**********; HMACCOUNT=916859EF894D2E51; c_dl_prid=1745986098361_275239; c_dl_rid=1747308124662_624961; c_dl_fref=https://blog.csdn.net/qq_50119948/article/details/*********; c_dl_fpage=/download/qq_44237441/********; c_dl_um=-; uuid_tt_dd=10_36844384450-*************-399563; c_segment=11; csdn_newcert_weixin_46066085=1; ssxmod_itna=eqRxRDuiYDwxyDfxBPGI2ClfDCiAYDkiKff4u8RDBkG4iNDnD8x7YDvIIM7IQC+0Guwaxrmo4+iLBefbLYKBj8ubKLSO74GLDmKDyiApueDx1q0rD74irDDxD3DbSdDSDWKD9D0+kSBuqtDm4GWCqGfDDoDYR=nDitD4qDB+EdDKqGgCkeK+wxD0whrODx9YDDhQC2UzlUdLqhP57DyD0UQxBLYRcuocpHKSkrOEp0aiqGybKGuRMtV/SbDCoUVnwiszRG3IYdR+APbBGG33cEQU7WtKAxQzGhiG0Gi7AkQSxsDG8DK0NqxD; ssxmod_itna2=eqRxRDuiYDwxyDfxBPGI2ClfDCiAYDkiKff4u8D8TP2qGXvxtGa/zAdBfz=AOAPSamsnQ0gP48D82yDhDHtG9wHh7+qbNI3clf4yCYZcQviRTs/83iXj=vmEXYQ+H9dBp3=70/nwkcIieWWoq6v=4P4rbOTsKex8D128M0nDMriQH6etf2r=ThDNPiq7fxINslbw6rGI=33N6PeLXORdDgTP0/KQ6ITaDNf6P3n0YWQiG03vf9vzHKjRc4lDqWHnprQ0ri=GYT6HFu0D+M1O6ojN4RfNXuTwHEFjrC3uruLEZIqrnN57422lxhKyWIv0WRl4hg3wl7az/w0zufnF835LWR1KACGTWI4SiTtGF8lH439DzHqMRlYErImdsDhGW2Q0QTFw+DtiGdortFEr4PjRmr+yMlH90rPlnq0gkQiq1+EGA=Qbqo4rSpTlWXWR5IIWfr5eTKXbebu2+DrfGTtIQArAeI5muEeK1mq8h7=8RECkWQifRIoUnE+HqUWzpRWWQ7w7+OeIDdmZ5b8mW8wy/AEWxDKLtD7=DYKqeD==; tfstk=gaZE4E9xbMIUJTcK-liy_bPfbqoKD05ftuGSE82odXchy7NyafPJAuw7Ez-zHfdQRyt54zzin3AH-e0ijxcOpYG7RbWzGfE7Rb_d4Tyo6vDoqvxgIxkSZ7yRpYcuZ7l7RNsbpJn-qsNPciwpD3AgikokNRmKFYvsbZSbpJpihGb9ji9yjR3qxbVo-V0ipfAkt7DosOkoFBYkx7XNQYMex30n-RmiExmoZ7mlQhctE0D3Z0cWFbcXbxuheWxzWdSrz2l0Kf-4Blk3geen_FT_bymIi3lwq3qZL8urmA-VPj4xOqG0sGtE4RDz_qaFgHPzE-Uo7uRhdSVgz7DTW_8rgz2Qklg6Ee2a4Xu0xVJRh-2KE70zWs-jLDaZukzdeC23GXzmv89VORu47qHiSL8nArwb2qqhYIhskYPZly52YSjrxQHgGhr88QYr-AHZGOWNbpK4w8V9Bz8Jy2UtQj6dpUL--AHZGOWwyU3KDAlfp9C..; c_first_ref=www.bing.com; c_first_page=https%3A//www.csdn.net/; c_ab_test=1; __gads=ID=fb115f4a5336c144:T=**********:RT=1748347451:S=ALNI_MbkQHzQqx26vt8BbZ8j4nMFVqCD_Q; __gpi=UID=00000e85bc65c320:T=**********:RT=1748347451:S=ALNI_MacTjEP1mUXXuK565rtyrenbgEfjw; __eoi=ID=fbbe7b50e75171eb:T=1742033395:RT=1748347451:S=AA-AfjbhVe2mYjUs1nMAyMqkMRu4; FCNEC=%5B%5B%22AKsRol9f8pcrWEk5WVA1HAGz-67tb9NwODDVxbWsR0k0k9MtEPSm_pkxPh3J48WNanOO4J6bRhMC9bCWVJZM5l0sa_Jjy64e8Fwgu_gztfTH3qd7c5sRaJ6OV8xiYOMtbhJX5Fs0qKE5SoBxQlN7DBcuhqdhaV68mg%3D%3D%22%5D%5D; _clsk=11lk2w1%7C1748348346279%7C2%7C0%7Cv.clarity.ms%2Fcollect; dc_session_id=10_1748414850851.244335; c_dsid=11_1748414851211.261370; c-sidebar-collapse=0; Hm_ct_6bcd52f51e9b3dce32bec4a3997715ac=6525*1*10_36844384450-*************-399563!5744*1*weixin_46066085; creativeSetApiNew=%7B%22toolbarImg%22%3A%22https%3A//img-home.csdnimg.cn/images/20231011044944.png%22%2C%22publishSuccessImg%22%3A%22https%3A//img-home.csdnimg.cn/images/20240229024608.png%22%2C%22articleNum%22%3A0%2C%22type%22%3A0%2C%22oldUser%22%3Afalse%2C%22useSeven%22%3Atrue%2C%22oldFullVersion%22%3Afalse%2C%22userName%22%3A%22weixin_46066085%22%7D; log_Id_click=2; c_pref=https%3A//www.bing.com/; c_ref=https%3A//www.csdn.net/; c_page_id=default; log_Id_pv=2; Hm_lpvt_6bcd52f51e9b3dce32bec4a3997715ac=1748414863; dc_tos=swyljj; log_Id_view=80; _clck=191xv3g%7C2%7Cfwa%7C0%7C1649")

# 快手配置
KUAI_SHOU_COOKIE = os.environ.get("KUAI_SHOU_COOKIE", "kpf=PC_WEB; clientid=3; did=web_c7e141a1d4af8608c78b88e6b4f2af50; didv=1743259440000; userId=1689701533; kuaishou.server.webday7_st=ChprdWFpc2hvdS5zZXJ2ZXIud2ViZGF5Ny5zdBKwAZPOO2SUvfBuV1A0EWTsV1KOyORvOYpHuzPnm0q1LjT0Vh4InYkk8SiYsQwkKm0UmvSGwrFxdSSW6b1-dt4WzsR_v6pON4nX_HvitJPRI0Xw1xaB1C5G51NMvJKH3GaFX076L8A1Zj916GbULECZp2uq243_kzkSAJ7sAogxpkgckyG9LEpkzbZ2zoIkk1fIRdK5b4NGGCQkK6ZMmtrT9dQlcsfb-LYWjgMYn6JextE1GhL4pmhhbJYE_IbaSGnqsJiJvG8iILS-K9TESH2cq_-b2PtDX2ZE3bk_4uioDGb0vVxfUQB4KAUwAQ; kuaishou.server.webday7_ph=99c90fac85ab74d0ea1b586eafbbd46f789c; kpn=KUAISHOU_VISION")

# 抖音配置
DOU_YIN_COOKIE = os.environ.get("DOU_YIN_COOKIE", "UIFID_TEMP=c4683e1a43ffa6bc6852097c712d14b81f04bc9b5ca6d30214b0e66b4e385280513a2e86ac1ed9852a4b9e45d1fbb0aba08450c97bb1efc8236813b9fbb0ff2dd7e9c798a50e197eb52f1ed567852bad; live_use_vvc=%22false%22; UIFID=c4683e1a43ffa6bc6852097c712d14b81f04bc9b5ca6d30214b0e66b4e385280992184e1931287336fbe546dfa7f504ad3a1e167b4f9a52eb8cc2c59a34025efa884984ccf8caa229eb6c01a737cdcc3bba85b936ad10bb6256140a9eab5d7f78f743afcd8e0560fbdd97a1e433d014f16c1243596e4d20431f710274c97088427b7db011ba8b28ca7c3e4650d932b6e38a101e88f3c53817e840c11b8e3836a; SelfTabRedDotControl=%5B%5D; store-region=cn-bj; store-region-src=uid; bd_ticket_guard_client_web_domain=2; d_ticket=70a2bbd061d666479e2527ccbeb6a2864b518; passport_assist_user=CkABSOpS7c545NBZANpa8ZbjHD2Vljn-VvEZNC6XXn7_ZEfWxbBvA6WehenePhOg2ahTFDe4wbi_V5FIk1pEMUxlGkoKPAAAAAAAAAAAAABOwqCXje-2tspekCsn5B1mvjl-m6vOb9UB4Qy-B-HVXadHZEgQ9w848ZvVIfqFJePIzRC6jOwNGImv1lQgASIBA7G7Fp8%3D; n_mh=34VyT4hgXvoQHOGHBJalgiDlyjekVr-rAay5AHdEsro; uid_tt=c58d01fefa2bf8f975b37c6cdac3dcd5; uid_tt_ss=c58d01fefa2bf8f975b37c6cdac3dcd5; sid_tt=486c7ad4d3be0614198adb30b86c4c95; sessionid=486c7ad4d3be0614198adb30b86c4c95; sessionid_ss=486c7ad4d3be0614198adb30b86c4c95; is_staff_user=false; login_time=1742056119741; my_rd=2; ttwid=1%7CUOmYUMbDsKlVD9GykQYczvkXKJr_HD6TsHvpuEQjnH8%7C1744272512%7C60bf28c5001950fc048cc23fcfebcf2d582ceadcd60fb5ae86b55c4ab80d230f; theme=%22light%22; passport_csrf_token=133a1bf558d86d50f8a6b5893923e258; passport_csrf_token_default=133a1bf558d86d50f8a6b5893923e258; sid_guard=486c7ad4d3be0614198adb30b86c4c95%7C1747144611%7C5184000%7CSat%2C+12-Jul-2025+13%3A56%3A51+GMT; sid_ucp_v1=1.0.0-KGQxMTcxODAxZTcwMmY4OGRhYWFkNmUxZTY3MTRmN2U2M2M0Nzk1MmMKIAid08DT0_RHEKOfjcEGGO8xIAwwsdDO-gU4B0D0B0gEGgJscSIgNDg2YzdhZDRkM2JlMDYxNDE5OGFkYjMwYjg2YzRjOTU; ssid_ucp_v1=1.0.0-KGQxMTcxODAxZTcwMmY4OGRhYWFkNmUxZTY3MTRmN2U2M2M0Nzk1MmMKIAid08DT0_RHEKOfjcEGGO8xIAwwsdDO-gU4B0D0B0gEGgJscSIgNDg2YzdhZDRkM2JlMDYxNDE5OGFkYjMwYjg2YzRjOTU; SearchMultiColumnLandingAbVer=2; SEARCH_RESULT_LIST_TYPE=%22multi%22; __security_mc_1_s_sdk_crypt_sdk=b51c1835-4153-a4bc; __security_mc_1_s_sdk_cert_key=a42bbd59-4964-9478; __security_mc_1_s_sdk_sign_data_key_web_protect=8e67fecc-4bd8-ab0b; _bd_ticket_crypt_cookie=c97cb32c9c04daa013825d4565758aa7; webcast_local_quality=hd; webcast_leading_last_show_time=1747570586171; webcast_leading_total_show_times=1; stream_player_status_params=%22%7B%5C%22is_auto_play%5C%22%3A0%2C%5C%22is_full_screen%5C%22%3A0%2C%5C%22is_full_webscreen%5C%22%3A0%2C%5C%22is_mute%5C%22%3A1%2C%5C%22is_speed%5C%22%3A1%2C%5C%22is_visible%5C%22%3A1%7D%22; FOLLOW_LIVE_POINT_INFO=%22MS4wLjABAAAAPs9N746JHXBgpYPepn51uSco2BXCvG3ktoO9cPVWxuw%2F1747584000000%2F0%2F0%2F1747579344707%22; __live_version__=%221.1.3.2158%22; live_can_add_dy_2_desktop=%221%22; download_guide=%220%2F%2F1%22; volume_info=%7B%22isUserMute%22%3Afalse%2C%22isMute%22%3Atrue%2C%22volume%22%3A0.406%7D; publish_badge_show_info=%220%2C0%2C0%2C1747901676804%22; biz_trace_id=69c354cd; odin_tt=6cae4221040f514a9ba38502607064575274b5ef43f7d17c6d0bbbe68e29a12eb03e59acba6bdc44bf217c7a48d27ab5e7dc4d472459b998dcee1c3c9f0caa09; FOLLOW_NUMBER_YELLOW_POINT_INFO=%22MS4wLjABAAAAPs9N746JHXBgpYPepn51uSco2BXCvG3ktoO9cPVWxuw%2F1748102400000%2F0%2F1748067686400%2F0%22; strategyABtestKey=%************.51%22; bd_ticket_guard_client_data=eyJiZC10aWNrZXQtZ3VhcmQtdmVyc2lvbiI6MiwiYmQtdGlja2V0LWd1YXJkLWl0ZXJhdGlvbi12ZXJzaW9uIjoxLCJiZC10aWNrZXQtZ3VhcmQtcmVlLXB1YmxpYy1rZXkiOiJCRSs5c0YxZHQ4Z1M1bGEreTl4K25GZHN2aVA1Sko5YS9HNXFUNkRLNkxRQkx4cW1jdldxZWtabmdqNWFuV2xoS1R2SzdHcktGZWNMV1dKV3k1dmxpdU09IiwiYmQtdGlja2V0LWd1YXJkLXdlYi12ZXJzaW9uIjoyfQ%3D%3D; IsDouyinActive=true; home_can_add_dy_2_desktop=%220%22; stream_recommend_feed_params=%22%7B%5C%22cookie_enabled%5C%22%3Atrue%2C%5C%22screen_width%5C%22%3A1536%2C%5C%22screen_height%5C%22%3A864%2C%5C%22browser_online%5C%22%3Atrue%2C%5C%22cpu_core_num%5C%22%3A8%2C%5C%22device_memory%5C%22%3A8%2C%5C%22downlink%5C%22%3A0.85%2C%5C%22effective_type%5C%22%3A%5C%223g%5C%22%2C%5C%22round_trip_time%5C%22%3A300%7D%22")

# 哔哩哔哩配置
BILIBILI_COOKIE = os.environ.get("BILIBILI_COOKIE", "buvid3=E48B3426-0FF9-B90C-B196-CE8650032B2265458infoc; b_nut=1720348565; _uuid=10CA8105102-627B-1B1C-773C-4B1029DFCD9E965684infoc; buvid_fp=99f7625440f0b3ccf47636726705d31b; enable_web_push=DISABLE; buvid4=025FB72E-9B26-A5D8-3BD4-A51D0E66AD9A67050-024070710-xaYXSHzkgQ5jkFxbX%2FtCl69g3PIWqS2q4LSKuhRX7kSjrQDDJ95hl3wgIbPcMXKf; rpdid=0zbfvZTxJv|IMBzbkZS|1ji|3w1Sqpha; CURRENT_QUALITY=80; header_theme_version=CLOSE; LIVE_BUVID=AUTO8517213868862813; PVID=1; enable_feed_channel=ENABLE; DedeUserID=220696176; DedeUserID__ckMd5=5933741a1323befc; home_feed_column=5; browser_resolution=1528-746; CURRENT_FNVAL=4048; bp_t_offset_220696176=1065031583079071744; b_lsid=3C91D93E_19701950075; share_source_origin=COPY; bsource=share_source_copy_link; bili_ticket=eyJhbGciOiJIUzI1NiIsImtpZCI6InMwMyIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NDgzMzc0MzIsImlhdCI6MTc0ODA3ODE3MiwicGx0IjotMX0.aV0pKwfC3dbiqcGP0R5QMVCF1SA0258XUmKevN3rUKc; bili_ticket_expires=1748337372; SESSDATA=8d57347c%2C1763630489%2C7c251%2A52CjDBz2MYNshHs9NkST2rJwVCRJiY2O5fJ7HOk75MyyzQ98WN5Fw_1ixyAdQ5Ny3JZDISVlhvSVRDX0JySXZBUC1XN1M1X1BZaC1MWGhHVzJiazV2MDJjTUJVcU1DQW9NMUZ4eDhzbXhibjZ5eFRCejRmZnRIcWdySmYwSWZNQUxjZG5wUVJVa3VRIIEC; bili_jct=eb6d8780fa278621464b27a822fb1076; sid=79lgkxrc")

# 浏览器配置
CHROME_DRIVER_PATH = os.environ.get("CHROME_DRIVER_PATH", "")  # ChromeDriver路径，从环境变量获取
HEADLESS_MODE = True  # 是否使用无头模式

# API配置
API_TIMEOUT = 30  # API超时时间（秒）
API_MAX_RETRIES = 3  # API最大重试次数

# 日志配置
LOG_LEVEL = os.environ.get("LOG_LEVEL", "INFO")
LOG_DIR = os.path.join(BASE_DIR, "logs")

# 确保日志目录存在
if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)