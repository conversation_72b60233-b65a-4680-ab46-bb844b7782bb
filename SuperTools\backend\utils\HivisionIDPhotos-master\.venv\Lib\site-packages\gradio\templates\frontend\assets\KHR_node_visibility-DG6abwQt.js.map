{"version": 3, "file": "KHR_node_visibility-DG6abwQt.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/KHR_node_visibility.js"], "sourcesContent": ["import { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nimport { AddObjectAccessorToKey } from \"./objectModelMapping.js\";\nconst NAME = \"KHR_node_visibility\";\n// object model extension for visibility\nAddObjectAccessorToKey(\"/nodes/{}/extensions/KHR_node_visibility/visible\", {\n    get: (node) => {\n        const tn = node._babylonTransformNode;\n        if (tn && tn.isVisible !== undefined) {\n            return tn.isVisible;\n        }\n        return true;\n    },\n    set: (value, node) => {\n        node._primitiveBabylonMeshes?.forEach((mesh) => {\n            mesh.inheritVisibility = true;\n        });\n        if (node._babylonTransformNode) {\n            node._babylonTransformNode.isVisible = value;\n        }\n        node._primitiveBabylonMeshes?.forEach((mesh) => {\n            mesh.isVisible = value;\n        });\n    },\n    getTarget: (node) => node._babylonTransformNode,\n    getPropertyName: [() => \"isVisible\"],\n    type: \"boolean\",\n});\n/**\n * Loader extension for KHR_node_visibility\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class KHR_node_visibility {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        this._loader = loader;\n        this.enabled = loader.isExtensionUsed(NAME);\n    }\n    async onReady() {\n        this._loader.gltf.nodes?.forEach((node) => {\n            node._primitiveBabylonMeshes?.forEach((mesh) => {\n                mesh.inheritVisibility = true;\n            });\n            // When the JSON Pointer is used we need to change both the transform node and the primitive meshes to the new value.\n            if (node.extensions?.KHR_node_visibility) {\n                if (node.extensions?.KHR_node_visibility.visible === false) {\n                    if (node._babylonTransformNode) {\n                        node._babylonTransformNode.isVisible = false;\n                    }\n                    node._primitiveBabylonMeshes?.forEach((mesh) => {\n                        mesh.isVisible = false;\n                    });\n                }\n            }\n        });\n    }\n    dispose() {\n        this._loader = null;\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new KHR_node_visibility(loader));\n//# sourceMappingURL=KHR_node_visibility.js.map"], "names": ["NAME", "AddObjectAccessorToKey", "node", "tn", "value", "mesh", "KHR_node_visibility", "loader", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "6JAEA,MAAMA,EAAO,sBAEbC,EAAuB,mDAAoD,CACvE,IAAMC,GAAS,CACX,MAAMC,EAAKD,EAAK,sBAChB,OAAIC,GAAMA,EAAG,YAAc,OAChBA,EAAG,UAEP,EACV,EACD,IAAK,CAACC,EAAOF,IAAS,CAClBA,EAAK,yBAAyB,QAASG,GAAS,CAC5CA,EAAK,kBAAoB,EACrC,CAAS,EACGH,EAAK,wBACLA,EAAK,sBAAsB,UAAYE,GAE3CF,EAAK,yBAAyB,QAASG,GAAS,CAC5CA,EAAK,UAAYD,CAC7B,CAAS,CACJ,EACD,UAAYF,GAASA,EAAK,sBAC1B,gBAAiB,CAAC,IAAM,WAAW,EACnC,KAAM,SACV,CAAC,EAKM,MAAMI,CAAoB,CAI7B,YAAYC,EAAQ,CAIhB,KAAK,KAAOP,EACZ,KAAK,QAAUO,EACf,KAAK,QAAUA,EAAO,gBAAgBP,CAAI,CAC7C,CACD,MAAM,SAAU,CACZ,KAAK,QAAQ,KAAK,OAAO,QAASE,GAAS,CACvCA,EAAK,yBAAyB,QAASG,GAAS,CAC5CA,EAAK,kBAAoB,EACzC,CAAa,EAEGH,EAAK,YAAY,qBACbA,EAAK,YAAY,oBAAoB,UAAY,KAC7CA,EAAK,wBACLA,EAAK,sBAAsB,UAAY,IAE3CA,EAAK,yBAAyB,QAASG,GAAS,CAC5CA,EAAK,UAAY,EACzC,CAAqB,EAGrB,CAAS,CACJ,CACD,SAAU,CACN,KAAK,QAAU,IAClB,CACL,CACAG,EAAwBR,CAAI,EAC5BS,EAAsBT,EAAM,GAAOO,GAAW,IAAID,EAAoBC,CAAM,CAAC", "x_google_ignoreList": [0]}