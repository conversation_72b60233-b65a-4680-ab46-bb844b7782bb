__all__: list[str] = []

# Enumerations
StsOk: int
STS_OK: int
StsBackTrace: int
STS_BACK_TRACE: int
StsError: int
STS_ERROR: int
StsInternal: int
STS_INTERNAL: int
StsNoMem: int
STS_NO_MEM: int
StsBadArg: int
STS_BAD_ARG: int
StsBadFunc: int
STS_BAD_FUNC: int
StsNoConv: int
STS_NO_CONV: int
StsAutoTrace: int
STS_AUTO_TRACE: int
HeaderIsNull: int
HEADER_IS_NULL: int
BadImageSize: int
BAD_IMAGE_SIZE: int
BadOffset: int
BAD_OFFSET: int
BadDataPtr: int
BAD_DATA_PTR: int
BadStep: int
BAD_STEP: int
BadModelOrChSeq: int
BAD_MODEL_OR_CH_SEQ: int
BadNumChannels: int
BAD_NUM_CHANNELS: int
BadNumChannel1U: int
BAD_NUM_CHANNEL1U: int
BadDepth: int
BAD_DEPTH: int
BadAlphaChannel: int
BAD_ALPHA_CHANNEL: int
BadOrder: int
BAD_ORDER: int
BadOrigin: int
BAD_ORIGIN: int
BadAlign: int
BAD_ALIGN: int
BadCallBack: int
BAD_CALL_BACK: int
BadTileSize: int
BAD_TILE_SIZE: int
BadCOI: int
BAD_COI: int
BadROISize: int
BAD_ROISIZE: int
MaskIsTiled: int
MASK_IS_TILED: int
StsNullPtr: int
STS_NULL_PTR: int
StsVecLengthErr: int
STS_VEC_LENGTH_ERR: int
StsFilterStructContentErr: int
STS_FILTER_STRUCT_CONTENT_ERR: int
StsKernelStructContentErr: int
STS_KERNEL_STRUCT_CONTENT_ERR: int
StsFilterOffsetErr: int
STS_FILTER_OFFSET_ERR: int
StsBadSize: int
STS_BAD_SIZE: int
StsDivByZero: int
STS_DIV_BY_ZERO: int
StsInplaceNotSupported: int
STS_INPLACE_NOT_SUPPORTED: int
StsObjectNotFound: int
STS_OBJECT_NOT_FOUND: int
StsUnmatchedFormats: int
STS_UNMATCHED_FORMATS: int
StsBadFlag: int
STS_BAD_FLAG: int
StsBadPoint: int
STS_BAD_POINT: int
StsBadMask: int
STS_BAD_MASK: int
StsUnmatchedSizes: int
STS_UNMATCHED_SIZES: int
StsUnsupportedFormat: int
STS_UNSUPPORTED_FORMAT: int
StsOutOfRange: int
STS_OUT_OF_RANGE: int
StsParseError: int
STS_PARSE_ERROR: int
StsNotImplemented: int
STS_NOT_IMPLEMENTED: int
StsBadMemBlock: int
STS_BAD_MEM_BLOCK: int
StsAssert: int
STS_ASSERT: int
GpuNotSupported: int
GPU_NOT_SUPPORTED: int
GpuApiCallError: int
GPU_API_CALL_ERROR: int
OpenGlNotSupported: int
OPEN_GL_NOT_SUPPORTED: int
OpenGlApiCallError: int
OPEN_GL_API_CALL_ERROR: int
OpenCLApiCallError: int
OPEN_CLAPI_CALL_ERROR: int
OpenCLDoubleNotSupported: int
OPEN_CLDOUBLE_NOT_SUPPORTED: int
OpenCLInitError: int
OPEN_CLINIT_ERROR: int
OpenCLNoAMDBlasFft: int
OPEN_CLNO_AMDBLAS_FFT: int
Code = int
"""One of [StsOk, STS_OK, StsBackTrace, STS_BACK_TRACE, StsError, STS_ERROR, StsInternal, STS_INTERNAL, StsNoMem, STS_NO_MEM, StsBadArg, STS_BAD_ARG, StsBadFunc, STS_BAD_FUNC, StsNoConv, STS_NO_CONV, StsAutoTrace, STS_AUTO_TRACE, HeaderIsNull, HEADER_IS_NULL, BadImageSize, BAD_IMAGE_SIZE, BadOffset, BAD_OFFSET, BadDataPtr, BAD_DATA_PTR, BadStep, BAD_STEP, BadModelOrChSeq, BAD_MODEL_OR_CH_SEQ, BadNumChannels, BAD_NUM_CHANNELS, BadNumChannel1U, BAD_NUM_CHANNEL1U, BadDepth, BAD_DEPTH, BadAlphaChannel, BAD_ALPHA_CHANNEL, BadOrder, BAD_ORDER, BadOrigin, BAD_ORIGIN, BadAlign, BAD_ALIGN, BadCallBack, BAD_CALL_BACK, BadTileSize, BAD_TILE_SIZE, BadCOI, BAD_COI, BadROISize, BAD_ROISIZE, MaskIsTiled, MASK_IS_TILED, StsNullPtr, STS_NULL_PTR, StsVecLengthErr, STS_VEC_LENGTH_ERR, StsFilterStructContentErr, STS_FILTER_STRUCT_CONTENT_ERR, StsKernelStructContentErr, STS_KERNEL_STRUCT_CONTENT_ERR, StsFilterOffsetErr, STS_FILTER_OFFSET_ERR, StsBadSize, STS_BAD_SIZE, StsDivByZero, STS_DIV_BY_ZERO, StsInplaceNotSupported, STS_INPLACE_NOT_SUPPORTED, StsObjectNotFound, STS_OBJECT_NOT_FOUND, StsUnmatchedFormats, STS_UNMATCHED_FORMATS, StsBadFlag, STS_BAD_FLAG, StsBadPoint, STS_BAD_POINT, StsBadMask, STS_BAD_MASK, StsUnmatchedSizes, STS_UNMATCHED_SIZES, StsUnsupportedFormat, STS_UNSUPPORTED_FORMAT, StsOutOfRange, STS_OUT_OF_RANGE, StsParseError, STS_PARSE_ERROR, StsNotImplemented, STS_NOT_IMPLEMENTED, StsBadMemBlock, STS_BAD_MEM_BLOCK, StsAssert, STS_ASSERT, GpuNotSupported, GPU_NOT_SUPPORTED, GpuApiCallError, GPU_API_CALL_ERROR, OpenGlNotSupported, OPEN_GL_NOT_SUPPORTED, OpenGlApiCallError, OPEN_GL_API_CALL_ERROR, OpenCLApiCallError, OPEN_CLAPI_CALL_ERROR, OpenCLDoubleNotSupported, OPEN_CLDOUBLE_NOT_SUPPORTED, OpenCLInitError, OPEN_CLINIT_ERROR, OpenCLNoAMDBlasFft, OPEN_CLNO_AMDBLAS_FFT]"""



