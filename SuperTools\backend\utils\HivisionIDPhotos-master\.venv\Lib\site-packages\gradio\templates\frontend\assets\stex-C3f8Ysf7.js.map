{"version": 3, "file": "stex-C3f8Ysf7.js", "sources": ["../../../../node_modules/.pnpm/@codemirror+legacy-modes@6.4.3/node_modules/@codemirror/legacy-modes/mode/stex.js"], "sourcesContent": ["function mkStex(mathMode) {\n  function pushCommand(state, command) {\n    state.cmdState.push(command);\n  }\n\n  function peekCommand(state) {\n    if (state.cmdState.length > 0) {\n      return state.cmdState[state.cmdState.length - 1];\n    } else {\n      return null;\n    }\n  }\n\n  function popCommand(state) {\n    var plug = state.cmdState.pop();\n    if (plug) {\n      plug.closeBracket();\n    }\n  }\n\n  // returns the non-default plugin closest to the end of the list\n  function getMostPowerful(state) {\n    var context = state.cmdState;\n    for (var i = context.length - 1; i >= 0; i--) {\n      var plug = context[i];\n      if (plug.name == \"DEFAULT\") {\n        continue;\n      }\n      return plug;\n    }\n    return { styleIdentifier: function() { return null; } };\n  }\n\n  function addPluginPattern(pluginName, cmdStyle, styles) {\n    return function () {\n      this.name = pluginName;\n      this.bracketNo = 0;\n      this.style = cmdStyle;\n      this.styles = styles;\n      this.argument = null;   // \\begin and \\end have arguments that follow. These are stored in the plugin\n\n      this.styleIdentifier = function() {\n        return this.styles[this.bracketNo - 1] || null;\n      };\n      this.openBracket = function() {\n        this.bracketNo++;\n        return \"bracket\";\n      };\n      this.closeBracket = function() {};\n    };\n  }\n\n  var plugins = {};\n\n  plugins[\"importmodule\"] = addPluginPattern(\"importmodule\", \"tag\", [\"string\", \"builtin\"]);\n  plugins[\"documentclass\"] = addPluginPattern(\"documentclass\", \"tag\", [\"\", \"atom\"]);\n  plugins[\"usepackage\"] = addPluginPattern(\"usepackage\", \"tag\", [\"atom\"]);\n  plugins[\"begin\"] = addPluginPattern(\"begin\", \"tag\", [\"atom\"]);\n  plugins[\"end\"] = addPluginPattern(\"end\", \"tag\", [\"atom\"]);\n\n  plugins[\"label\"    ] = addPluginPattern(\"label\"    , \"tag\", [\"atom\"]);\n  plugins[\"ref\"      ] = addPluginPattern(\"ref\"      , \"tag\", [\"atom\"]);\n  plugins[\"eqref\"    ] = addPluginPattern(\"eqref\"    , \"tag\", [\"atom\"]);\n  plugins[\"cite\"     ] = addPluginPattern(\"cite\"     , \"tag\", [\"atom\"]);\n  plugins[\"bibitem\"  ] = addPluginPattern(\"bibitem\"  , \"tag\", [\"atom\"]);\n  plugins[\"Bibitem\"  ] = addPluginPattern(\"Bibitem\"  , \"tag\", [\"atom\"]);\n  plugins[\"RBibitem\" ] = addPluginPattern(\"RBibitem\" , \"tag\", [\"atom\"]);\n\n  plugins[\"DEFAULT\"] = function () {\n    this.name = \"DEFAULT\";\n    this.style = \"tag\";\n\n    this.styleIdentifier = this.openBracket = this.closeBracket = function() {};\n  };\n\n  function setState(state, f) {\n    state.f = f;\n  }\n\n  // called when in a normal (no environment) context\n  function normal(source, state) {\n    var plug;\n    // Do we look like '\\command' ?  If so, attempt to apply the plugin 'command'\n    if (source.match(/^\\\\[a-zA-Z@\\xc0-\\u1fff\\u2060-\\uffff]+/)) {\n      var cmdName = source.current().slice(1);\n      plug = plugins.hasOwnProperty(cmdName) ? plugins[cmdName] : plugins[\"DEFAULT\"];\n      plug = new plug();\n      pushCommand(state, plug);\n      setState(state, beginParams);\n      return plug.style;\n    }\n\n    // escape characters\n    if (source.match(/^\\\\[$&%#{}_]/)) {\n      return \"tag\";\n    }\n\n    // white space control characters\n    if (source.match(/^\\\\[,;!\\/\\\\]/)) {\n      return \"tag\";\n    }\n\n    // find if we're starting various math modes\n    if (source.match(\"\\\\[\")) {\n      setState(state, function(source, state){ return inMathMode(source, state, \"\\\\]\"); });\n      return \"keyword\";\n    }\n    if (source.match(\"\\\\(\")) {\n      setState(state, function(source, state){ return inMathMode(source, state, \"\\\\)\"); });\n      return \"keyword\";\n    }\n    if (source.match(\"$$\")) {\n      setState(state, function(source, state){ return inMathMode(source, state, \"$$\"); });\n      return \"keyword\";\n    }\n    if (source.match(\"$\")) {\n      setState(state, function(source, state){ return inMathMode(source, state, \"$\"); });\n      return \"keyword\";\n    }\n\n    var ch = source.next();\n    if (ch == \"%\") {\n      source.skipToEnd();\n      return \"comment\";\n    } else if (ch == '}' || ch == ']') {\n      plug = peekCommand(state);\n      if (plug) {\n        plug.closeBracket(ch);\n        setState(state, beginParams);\n      } else {\n        return \"error\";\n      }\n      return \"bracket\";\n    } else if (ch == '{' || ch == '[') {\n      plug = plugins[\"DEFAULT\"];\n      plug = new plug();\n      pushCommand(state, plug);\n      return \"bracket\";\n    } else if (/\\d/.test(ch)) {\n      source.eatWhile(/[\\w.%]/);\n      return \"atom\";\n    } else {\n      source.eatWhile(/[\\w\\-_]/);\n      plug = getMostPowerful(state);\n      if (plug.name == 'begin') {\n        plug.argument = source.current();\n      }\n      return plug.styleIdentifier();\n    }\n  }\n\n  function inMathMode(source, state, endModeSeq) {\n    if (source.eatSpace()) {\n      return null;\n    }\n    if (endModeSeq && source.match(endModeSeq)) {\n      setState(state, normal);\n      return \"keyword\";\n    }\n    if (source.match(/^\\\\[a-zA-Z@]+/)) {\n      return \"tag\";\n    }\n    if (source.match(/^[a-zA-Z]+/)) {\n      return \"variableName.special\";\n    }\n    // escape characters\n    if (source.match(/^\\\\[$&%#{}_]/)) {\n      return \"tag\";\n    }\n    // white space control characters\n    if (source.match(/^\\\\[,;!\\/]/)) {\n      return \"tag\";\n    }\n    // special math-mode characters\n    if (source.match(/^[\\^_&]/)) {\n      return \"tag\";\n    }\n    // non-special characters\n    if (source.match(/^[+\\-<>|=,\\/@!*:;'\"`~#?]/)) {\n      return null;\n    }\n    if (source.match(/^(\\d+\\.\\d*|\\d*\\.\\d+|\\d+)/)) {\n      return \"number\";\n    }\n    var ch = source.next();\n    if (ch == \"{\" || ch == \"}\" || ch == \"[\" || ch == \"]\" || ch == \"(\" || ch == \")\") {\n      return \"bracket\";\n    }\n\n    if (ch == \"%\") {\n      source.skipToEnd();\n      return \"comment\";\n    }\n    return \"error\";\n  }\n\n  function beginParams(source, state) {\n    var ch = source.peek(), lastPlug;\n    if (ch == '{' || ch == '[') {\n      lastPlug = peekCommand(state);\n      lastPlug.openBracket(ch);\n      source.eat(ch);\n      setState(state, normal);\n      return \"bracket\";\n    }\n    if (/[ \\t\\r]/.test(ch)) {\n      source.eat(ch);\n      return null;\n    }\n    setState(state, normal);\n    popCommand(state);\n\n    return normal(source, state);\n  }\n\n  return {\n    name: \"stex\",\n    startState: function() {\n      var f = mathMode ? function(source, state){ return inMathMode(source, state); } : normal;\n      return {\n        cmdState: [],\n        f: f\n      };\n    },\n    copyState: function(s) {\n      return {\n        cmdState: s.cmdState.slice(),\n        f: s.f\n      };\n    },\n    token: function(stream, state) {\n      return state.f(stream, state);\n    },\n    blankLine: function(state) {\n      state.f = normal;\n      state.cmdState.length = 0;\n    },\n    languageData: {\n      commentTokens: {line: \"%\"}\n    }\n  };\n};\n\nexport const stex = mkStex(false)\nexport const stexMath = mkStex(true)\n"], "names": ["mkStex", "mathMode", "pushCommand", "state", "command", "peekCommand", "popCommand", "plug", "getMostPowerful", "context", "i", "addPluginPattern", "pluginName", "cmdStyle", "styles", "plugins", "setState", "f", "normal", "source", "cmdName", "beginParams", "inMathMode", "ch", "endModeSeq", "lastPlug", "s", "stream", "stex", "stexMath"], "mappings": "AAAA,SAASA,EAAOC,EAAU,CACxB,SAASC,EAAYC,EAAOC,EAAS,CACnCD,EAAM,SAAS,KAAKC,CAAO,CAC5B,CAED,SAASC,EAAYF,EAAO,CAC1B,OAAIA,EAAM,SAAS,OAAS,EACnBA,EAAM,SAASA,EAAM,SAAS,OAAS,CAAC,EAExC,IAEV,CAED,SAASG,EAAWH,EAAO,CACzB,IAAII,EAAOJ,EAAM,SAAS,IAAG,EACzBI,GACFA,EAAK,aAAY,CAEpB,CAGD,SAASC,EAAgBL,EAAO,CAE9B,QADIM,EAAUN,EAAM,SACXO,EAAID,EAAQ,OAAS,EAAGC,GAAK,EAAGA,IAAK,CAC5C,IAAIH,EAAOE,EAAQC,CAAC,EACpB,GAAIH,EAAK,MAAQ,UAGjB,OAAOA,CACR,CACD,MAAO,CAAE,gBAAiB,UAAW,CAAE,OAAO,IAAO,CAAA,CACtD,CAED,SAASI,EAAiBC,EAAYC,EAAUC,EAAQ,CACtD,OAAO,UAAY,CACjB,KAAK,KAAOF,EACZ,KAAK,UAAY,EACjB,KAAK,MAAQC,EACb,KAAK,OAASC,EACd,KAAK,SAAW,KAEhB,KAAK,gBAAkB,UAAW,CAChC,OAAO,KAAK,OAAO,KAAK,UAAY,CAAC,GAAK,IAClD,EACM,KAAK,YAAc,UAAW,CAC5B,YAAK,YACE,SACf,EACM,KAAK,aAAe,UAAW,EACrC,CACG,CAED,IAAIC,EAAU,CAAA,EAEdA,EAAQ,aAAkBJ,EAAiB,eAAgB,MAAO,CAAC,SAAU,SAAS,CAAC,EACvFI,EAAQ,cAAmBJ,EAAiB,gBAAiB,MAAO,CAAC,GAAI,MAAM,CAAC,EAChFI,EAAQ,WAAgBJ,EAAiB,aAAc,MAAO,CAAC,MAAM,CAAC,EACtEI,EAAQ,MAAWJ,EAAiB,QAAS,MAAO,CAAC,MAAM,CAAC,EAC5DI,EAAQ,IAASJ,EAAiB,MAAO,MAAO,CAAC,MAAM,CAAC,EAExDI,EAAQ,MAAeJ,EAAiB,QAAa,MAAO,CAAC,MAAM,CAAC,EACpEI,EAAQ,IAAeJ,EAAiB,MAAa,MAAO,CAAC,MAAM,CAAC,EACpEI,EAAQ,MAAeJ,EAAiB,QAAa,MAAO,CAAC,MAAM,CAAC,EACpEI,EAAQ,KAAeJ,EAAiB,OAAa,MAAO,CAAC,MAAM,CAAC,EACpEI,EAAQ,QAAeJ,EAAiB,UAAa,MAAO,CAAC,MAAM,CAAC,EACpEI,EAAQ,QAAeJ,EAAiB,UAAa,MAAO,CAAC,MAAM,CAAC,EACpEI,EAAQ,SAAeJ,EAAiB,WAAa,MAAO,CAAC,MAAM,CAAC,EAEpEI,EAAQ,QAAa,UAAY,CAC/B,KAAK,KAAO,UACZ,KAAK,MAAQ,MAEb,KAAK,gBAAkB,KAAK,YAAc,KAAK,aAAe,UAAW,EAC7E,EAEE,SAASC,EAASb,EAAOc,EAAG,CAC1Bd,EAAM,EAAIc,CACX,CAGD,SAASC,EAAOC,EAAQhB,EAAO,CAC7B,IAAII,EAEJ,GAAIY,EAAO,MAAM,uCAAuC,EAAG,CACzD,IAAIC,EAAUD,EAAO,QAAS,EAAC,MAAM,CAAC,EACtC,OAAAZ,EAAOQ,EAAQ,eAAeK,CAAO,EAAIL,EAAQK,CAAO,EAAIL,EAAQ,QACpER,EAAO,IAAIA,EACXL,EAAYC,EAAOI,CAAI,EACvBS,EAASb,EAAOkB,CAAW,EACpBd,EAAK,KACb,CAQD,GALIY,EAAO,MAAM,cAAc,GAK3BA,EAAO,MAAM,cAAc,EAC7B,MAAO,MAIT,GAAIA,EAAO,MAAM,KAAK,EACpB,OAAAH,EAASb,EAAO,SAASgB,EAAQhB,EAAM,CAAE,OAAOmB,EAAWH,EAAQhB,EAAO,KAAK,CAAI,CAAA,EAC5E,UAET,GAAIgB,EAAO,MAAM,KAAK,EACpB,OAAAH,EAASb,EAAO,SAASgB,EAAQhB,EAAM,CAAE,OAAOmB,EAAWH,EAAQhB,EAAO,KAAK,CAAI,CAAA,EAC5E,UAET,GAAIgB,EAAO,MAAM,IAAI,EACnB,OAAAH,EAASb,EAAO,SAASgB,EAAQhB,EAAM,CAAE,OAAOmB,EAAWH,EAAQhB,EAAO,IAAI,CAAI,CAAA,EAC3E,UAET,GAAIgB,EAAO,MAAM,GAAG,EAClB,OAAAH,EAASb,EAAO,SAASgB,EAAQhB,EAAM,CAAE,OAAOmB,EAAWH,EAAQhB,EAAO,GAAG,CAAI,CAAA,EAC1E,UAGT,IAAIoB,EAAKJ,EAAO,OAChB,GAAII,GAAM,IACR,OAAAJ,EAAO,UAAS,EACT,UACF,GAAII,GAAM,KAAOA,GAAM,IAAK,CAEjC,GADAhB,EAAOF,EAAYF,CAAK,EACpBI,EACFA,EAAK,aAAagB,CAAE,EACpBP,EAASb,EAAOkB,CAAW,MAE3B,OAAO,QAET,MAAO,SACR,KAAM,QAAIE,GAAM,KAAOA,GAAM,KAC5BhB,EAAOQ,EAAQ,QACfR,EAAO,IAAIA,EACXL,EAAYC,EAAOI,CAAI,EAChB,WACE,KAAK,KAAKgB,CAAE,GACrBJ,EAAO,SAAS,QAAQ,EACjB,SAEPA,EAAO,SAAS,SAAS,EACzBZ,EAAOC,EAAgBL,CAAK,EACxBI,EAAK,MAAQ,UACfA,EAAK,SAAWY,EAAO,WAElBZ,EAAK,kBAEf,CAED,SAASe,EAAWH,EAAQhB,EAAOqB,EAAY,CAC7C,GAAIL,EAAO,WACT,OAAO,KAET,GAAIK,GAAcL,EAAO,MAAMK,CAAU,EACvC,OAAAR,EAASb,EAAOe,CAAM,EACf,UAET,GAAIC,EAAO,MAAM,eAAe,EAC9B,MAAO,MAET,GAAIA,EAAO,MAAM,YAAY,EAC3B,MAAO,uBAWT,GARIA,EAAO,MAAM,cAAc,GAI3BA,EAAO,MAAM,YAAY,GAIzBA,EAAO,MAAM,SAAS,EACxB,MAAO,MAGT,GAAIA,EAAO,MAAM,0BAA0B,EACzC,OAAO,KAET,GAAIA,EAAO,MAAM,0BAA0B,EACzC,MAAO,SAET,IAAII,EAAKJ,EAAO,OAChB,OAAII,GAAM,KAAOA,GAAM,KAAOA,GAAM,KAAOA,GAAM,KAAOA,GAAM,KAAOA,GAAM,IAClE,UAGLA,GAAM,KACRJ,EAAO,UAAS,EACT,WAEF,OACR,CAED,SAASE,EAAYF,EAAQhB,EAAO,CAClC,IAAIoB,EAAKJ,EAAO,KAAI,EAAIM,EACxB,OAAIF,GAAM,KAAOA,GAAM,KACrBE,EAAWpB,EAAYF,CAAK,EAC5BsB,EAAS,YAAYF,CAAE,EACvBJ,EAAO,IAAII,CAAE,EACbP,EAASb,EAAOe,CAAM,EACf,WAEL,UAAU,KAAKK,CAAE,GACnBJ,EAAO,IAAII,CAAE,EACN,OAETP,EAASb,EAAOe,CAAM,EACtBZ,EAAWH,CAAK,EAETe,EAAOC,EAAQhB,CAAK,EAC5B,CAED,MAAO,CACL,KAAM,OACN,WAAY,UAAW,CACrB,IAAIc,EAAIhB,EAAW,SAASkB,EAAQhB,EAAM,CAAE,OAAOmB,EAAWH,EAAQhB,CAAK,CAAE,EAAKe,EAClF,MAAO,CACL,SAAU,CAAE,EACZ,EAAGD,CACX,CACK,EACD,UAAW,SAASS,EAAG,CACrB,MAAO,CACL,SAAUA,EAAE,SAAS,MAAO,EAC5B,EAAGA,EAAE,CACb,CACK,EACD,MAAO,SAASC,EAAQxB,EAAO,CAC7B,OAAOA,EAAM,EAAEwB,EAAQxB,CAAK,CAC7B,EACD,UAAW,SAASA,EAAO,CACzBA,EAAM,EAAIe,EACVf,EAAM,SAAS,OAAS,CACzB,EACD,aAAc,CACZ,cAAe,CAAC,KAAM,GAAG,CAC1B,CACL,CACA,CAEY,MAACyB,EAAO5B,EAAO,EAAK,EACnB6B,EAAW7B,EAAO,EAAI", "x_google_ignoreList": [0]}