/**
 * SuperSpider 搜索历史记录脚本
 * 处理用户搜索历史记录相关功能
 */

// 当前页码
let currentPage = 1;
// 每页记录数
const perPage = 10;
// 当前筛选条件
let currentFilters = {};
// 选中的搜索记录ID
let selectedRecordIds = [];
// 当前视图模式：'list' 或 'grid'
let currentViewMode = 'list';
// 当前排序方式
let currentSort = { field: 'created_at', order: 'desc' };

// 防抖变量
let loadHistoryTimeout = null;
let loadStatsTimeout = null;

// 初始化搜索历史记录功能
function initSearchHistoryFeatures() {
    console.log('初始化搜索历史记录功能...');

    // 搜索记录链接点击事件
    const searchHistoryLink = document.getElementById('search-history-link');
    console.log('搜索记录链接元素:', searchHistoryLink);

    if (searchHistoryLink) {
        // 移除可能存在的旧事件监听器
        searchHistoryLink.removeEventListener('click', handleSearchHistoryLinkClick);
        // 添加新的事件监听器
        searchHistoryLink.addEventListener('click', handleSearchHistoryLinkClick);
        console.log('搜索记录链接事件监听器已添加');
    } else {
        console.error('未找到搜索记录链接元素');
    }

    // 初始化模态框关闭事件
    initModalCloseEvents();
}

// 初始化模态框关闭事件
function initModalCloseEvents() {
    // 监听模态框关闭按钮
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('close-modal') ||
            e.target.closest('.close-modal')) {
            const modal = e.target.closest('.modal');
            if (modal) {
                modal.classList.remove('active');
            }
        }

        // 点击遮罩层关闭模态框
        if (e.target.classList.contains('modal-overlay')) {
            const modal = e.target.closest('.modal');
            if (modal) {
                modal.classList.remove('active');
            }
        }
    });

    // ESC键关闭模态框
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const activeModal = document.querySelector('.modal.active');
            if (activeModal) {
                activeModal.classList.remove('active');
            }
        }
    });
}

// 处理搜索记录链接点击事件
function handleSearchHistoryLinkClick(e) {
    if (e) e.preventDefault();
    console.log('搜索记录链接被点击');

    // 显示搜索历史记录模态框
    showSearchHistoryModal();
}

// 显示搜索历史记录模态框
function showSearchHistoryModal() {
    console.log('显示搜索历史记录模态框');

    const modal = document.getElementById('search-history-modal');
    if (modal) {
        modal.classList.add('active');

        // 初始化模态框功能
        setTimeout(() => {
            initSearchHistoryModalFeatures();
            loadSearchHistory();
            loadSearchStats();
        }, 100);
    } else {
        console.error('找不到搜索历史记录模态框元素');
    }
}

// 关闭搜索历史记录模态框
function closeSearchHistoryModal() {
    const modal = document.getElementById('search-history-modal');
    if (modal) {
        modal.classList.remove('active');
    }
}

// 加载搜索历史记录
async function loadSearchHistory() {
    console.log('开始加载搜索历史记录...');

    const recordsList = document.getElementById('search-records-list');
    const recordsPagination = document.getElementById('search-records-pagination');
    const recordsLoading = document.getElementById('search-records-loading');
    const recordsEmpty = document.getElementById('search-records-empty');

    console.log('DOM元素检查:', {
        recordsList: !!recordsList,
        recordsPagination: !!recordsPagination,
        recordsLoading: !!recordsLoading,
        recordsEmpty: !!recordsEmpty
    });

    if (!recordsList || !recordsPagination) {
        console.error('关键DOM元素未找到');
        return;
    }

    // 检查登录状态
    console.log('检查登录状态:', window.isLoggedIn);
    if (!window.isLoggedIn) {
        console.log('用户未登录，显示登录提示');
        recordsList.innerHTML = `
            <div class="error-message">
                <i class="fas fa-user-lock"></i>
                <p>请先登录后查看搜索历史记录</p>
                <button class="btn btn-primary" onclick="showLoginModal()">立即登录</button>
            </div>
        `;
        recordsPagination.innerHTML = '';
        return;
    }

    try {
        // 显示加载中
        if (recordsLoading) {
            recordsLoading.style.display = 'block';
        }

        // 隐藏空状态
        if (recordsEmpty) {
            recordsEmpty.style.display = 'none';
        }

        // 构建查询参数
        const params = new URLSearchParams({
            page: currentPage || 1,
            per_page: perPage
        });

        // 只添加有值的筛选条件
        if (currentFilters.platform) {
            params.append('platform', currentFilters.platform);
        }
        if (currentFilters.content_type) {
            params.append('content_type', currentFilters.content_type);
        }
        if (currentFilters.status) {
            params.append('status', currentFilters.status);
        }

        // 确定API端点
        let url = '/api/search/history';
        if (currentFilters.is_favorite) {
            url = '/api/search/favorites';
        }

        const fullUrl = `${url}?${params.toString()}`;
        console.log('发送API请求:', fullUrl);

        const response = await fetch(fullUrl, {
            method: 'GET',
            credentials: 'include', // 确保包含cookies
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        console.log('API响应状态:', response.status);

        // 检查响应状态
        if (response.status === 401) {
            // 未登录，显示登录提示
            if (recordsLoading) {
                recordsLoading.style.display = 'none';
            }

            recordsList.innerHTML = `
                <div class="empty-container">
                    <i class="fas fa-user-lock"></i>
                    <h3>需要登录</h3>
                    <p>请先登录后查看搜索记录</p>
                    <button class="btn btn-primary" onclick="showLoginModal()">立即登录</button>
                </div>
            `;

            // 清空分页
            recordsPagination.innerHTML = '';
            return;
        }

        const data = await response.json();
        console.log('完整API响应数据:', data);

        // 隐藏加载中
        if (recordsLoading) {
            recordsLoading.style.display = 'none';
        }

        if (data.success) {
            const { records, total, page, pages } = data.data;
            console.log('API返回的完整数据:', data.data);
            console.log('解析后的records:', records);
            console.log('records类型:', typeof records);
            console.log('records是否为数组:', Array.isArray(records));
            console.log('records长度:', records ? records.length : 'undefined');

            // 保存搜索记录，用于视图切换
            window.lastSearchRecords = records;

            // 更新当前页码
            currentPage = page;

            if (!records || !Array.isArray(records) || records.length === 0) {
                console.log('没有搜索记录，显示空状态');

                // 显示友好的空状态消息
                recordsList.innerHTML = `
                    <div class="empty-container">
                        <i class="fas fa-inbox"></i>
                        <p>暂无搜索记录</p>
                        <p class="empty-tip">开始使用SuperSpider搜索内容，这里将显示您的搜索历史</p>
                    </div>
                `;
                recordsPagination.innerHTML = '';
                return;
            }

            console.log('开始渲染', records.length, '条记录');
            console.log('第一条记录内容:', records[0]);

            // 渲染搜索列表
            renderSearchRecordsList(records);

            // 渲染分页
            renderPagination(page, pages, total);

            // 只在首次加载时加载搜索统计，避免重复请求
            if (currentPage === 1 && Object.keys(currentFilters).length === 0) {
                loadSearchStats();
            }

            // 重置选中的搜索记录ID
            selectedRecordIds = [];

            // 更新批量操作按钮状态
            updateBatchActionButtons();
        } else {
            console.error('获取搜索历史记录失败:', data.message);

            // 显示错误消息
            recordsList.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    <p>获取搜索历史记录失败: ${data.message}</p>
                    <button class="btn btn-primary" onclick="loadSearchHistory()">重试</button>
                </div>
            `;

            // 清空分页
            recordsPagination.innerHTML = '';
        }
    } catch (error) {
        console.error('获取搜索历史记录请求失败:', error);

        // 隐藏加载中
        if (recordsLoading) {
            recordsLoading.style.display = 'none';
        }

        // 显示错误消息
        recordsList.innerHTML = `
            <div class="error-message">
                <i class="fas fa-exclamation-circle"></i>
                <p>网络请求失败，请检查网络连接</p>
                <button class="btn btn-primary" onclick="loadSearchHistory()">重试</button>
            </div>
        `;

        // 清空分页
        recordsPagination.innerHTML = '';
    }
}

// 渲染搜索记录列表
function renderSearchRecordsList(records) {
    console.log('renderSearchRecordsList被调用，参数:', records);
    console.log('records长度:', records ? records.length : 'undefined');

    const recordsList = document.getElementById('search-records-list');
    console.log('找到search-records-list元素:', recordsList);

    if (!recordsList) {
        console.error('search-records-list 元素未找到');
        return;
    }

    // 清空列表
    recordsList.innerHTML = '';
    console.log('列表已清空');

    // 根据视图模式渲染
    if (currentViewMode === 'grid') {
        console.log('使用网格视图渲染');
        renderGridView(records, recordsList);
    } else {
        console.log('使用列表视图渲染');
        renderListView(records, recordsList);
    }

    console.log('渲染完成，容器内容:', recordsList.innerHTML.substring(0, 200));
}

// 渲染列表视图
function renderListView(records, container) {
    console.log('renderListView开始，records:', records);

    try {
        // 创建表格
        const table = document.createElement('table');
        table.className = 'search-records-table';
        table.style.cssText = 'width: 100%; border-collapse: collapse; background: white; display: table;';
        console.log('创建表格成功');

        // 创建表头
        const thead = document.createElement('thead');
        thead.innerHTML = `
            <tr>
                <th class="checkbox-column">
                    <input type="checkbox" id="select-all-checkbox">
                </th>
                <th>标题</th>
                <th>平台</th>
                <th>类型</th>
                <th>状态</th>
                <th>时间</th>
                <th>操作</th>
            </tr>
        `;
        table.appendChild(thead);
        console.log('表头创建成功');

        // 创建表体
        const tbody = document.createElement('tbody');
        console.log('开始处理', records.length, '条记录');

        records.forEach((record, index) => {
            console.log(`处理第${index + 1}条记录:`, record);

            try {
                const tr = document.createElement('tr');

                // 设置状态类名
                if (record.status === 'success') {
                    tr.classList.add('status-completed');
                } else if (record.status === 'failed') {
                    tr.classList.add('status-failed');
                }

                // 如果是收藏，添加收藏类名
                if (record.is_favorite) {
                    tr.classList.add('is-favorite');
                }

                // 格式化时间
                const formattedTime = record.created_at ?
                    new Date(record.created_at).toLocaleString('zh-CN') : '未知时间';

                // 行内容
                tr.innerHTML = `
                    <td class="checkbox-column">
                        <input type="checkbox" class="record-checkbox" data-id="${record.id}">
                    </td>
                    <td class="record-title" title="${record.title || '未知标题'}">${record.title || '未知标题'}</td>
                    <td>${getPlatformLabel(record.platform)}</td>
                    <td>${getContentTypeLabel(record.content_type)}</td>
                    <td><span class="status-badge status-${record.status}">${getStatusLabel(record.status)}</span></td>
                    <td>${formattedTime}</td>
                    <td>
                        <div class="record-actions">
                            <button class="action-btn favorite-btn ${record.is_favorite ? 'active' : ''}"
                                    data-id="${record.id}"
                                    title="${record.is_favorite ? '取消收藏' : '收藏'}"
                                    onclick="toggleFavorite(${record.id})">
                                <i class="fas fa-star"></i>
                            </button>
                            <button class="action-btn delete-btn"
                                    data-id="${record.id}"
                                    title="删除"
                                    onclick="deleteSearchRecord(${record.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                `;

                tbody.appendChild(tr);
                console.log(`第${index + 1}条记录添加成功`);
            } catch (error) {
                console.error(`处理第${index + 1}条记录时出错:`, error);
            }
        });

        table.appendChild(tbody);
        container.appendChild(table);
        console.log('表格添加到容器成功，表格行数:', tbody.children.length);

        // 添加复选框事件
        addCheckboxEvents();
        console.log('renderListView完成');
    } catch (error) {
        console.error('renderListView出错:', error);
    }
}

// 渲染网格视图
function renderGridView(records, container) {
    const grid = document.createElement('div');
    grid.className = 'search-records-grid';

    records.forEach(record => {
        const card = document.createElement('div');
        card.className = 'search-record-card';

        // 设置状态类名
        if (record.status === 'success') {
            card.classList.add('status-completed');
        } else if (record.status === 'failed') {
            card.classList.add('status-failed');
        }

        // 如果是收藏，添加收藏类名
        if (record.is_favorite) {
            card.classList.add('is-favorite');
        }

        // 缩略图
        let thumbnailHtml = '';
        if (record.thumbnail_url) {
            thumbnailHtml = `<img src="${record.thumbnail_url}" alt="${record.title || '未知标题'}" class="record-thumbnail">`;
        } else {
            thumbnailHtml = `<div class="record-thumbnail-placeholder">
                <i class="${getContentTypeIcon(record.content_type)}"></i>
            </div>`;
        }

        // 格式化时间
        const formattedTime = record.created_at ?
            new Date(record.created_at).toLocaleString('zh-CN') : '未知时间';

        // 设置卡片内容
        card.innerHTML = `
            <div class="record-card-header">
                <div class="record-checkbox-container">
                    <input type="checkbox" class="record-checkbox" data-id="${record.id}">
                </div>
                <div class="record-card-platform">${getPlatformLabel(record.platform)}</div>
                ${record.is_favorite ? '<div class="record-card-favorite"><i class="fas fa-star"></i></div>' : ''}
            </div>
            <div class="record-card-thumbnail">
                ${thumbnailHtml}
            </div>
            <div class="record-card-content">
                <h3 class="record-card-title" title="${record.title || '未知标题'}">${record.title || '未知标题'}</h3>
                <div class="record-card-author">${record.author || '未知作者'}</div>
                <div class="record-card-meta">
                    <span class="record-card-type">${getContentTypeLabel(record.content_type)}</span>
                    <span class="record-card-status">${getStatusLabel(record.status)}</span>
                </div>
                <div class="record-card-date">${formattedTime}</div>
                ${record.notes ? '<div class="record-card-notes-indicator"><i class="fas fa-sticky-note"></i> 有笔记</div>' : ''}
            </div>
            <div class="record-card-actions">
                ${record.video_url ?
                    `<a href="${record.video_url}" target="_blank" class="view-btn" title="查看视频">
                        <i class="fas fa-external-link-alt"></i>
                    </a>` : ''
                }
                <button class="action-btn favorite-btn ${record.is_favorite ? 'active' : ''}"
                        data-id="${record.id}"
                        title="${record.is_favorite ? '取消收藏' : '收藏'}"
                        onclick="toggleFavorite(${record.id})">
                    <i class="fas fa-star"></i>
                </button>
                <button class="action-btn notes-btn ${record.notes ? 'has-notes' : ''}"
                        data-id="${record.id}"
                        title="${record.notes ? '编辑笔记' : '添加笔记'}"
                        onclick="showNotesModal(${record.id}, '${record.title || '未知标题'}')">
                    <i class="fas fa-sticky-note"></i>
                </button>
                <button class="action-btn delete-btn"
                        data-id="${record.id}"
                        title="删除"
                        onclick="deleteSearchRecord(${record.id})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;

        grid.appendChild(card);
    });

    container.appendChild(grid);

    // 添加复选框事件
    addCheckboxEvents();
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initSearchHistoryFeatures();
});

// 辅助函数
function getPlatformLabel(platform) {
    const platforms = {
        'kuaishou': '快手',
        'douyin': '抖音',
        'bilibili': '哔哩哔哩',
        'csdn': 'CSDN'
    };
    return platforms[platform] || platform || '未知';
}

function getContentTypeLabel(contentType) {
    const types = {
        'video': '视频',
        'article': '文章',
        'resource': '资源',
        'image': '图片',
        'audio': '音频'
    };
    return types[contentType] || contentType || '未知';
}

function getStatusLabel(status) {
    const statuses = {
        'success': '成功',
        'failed': '失败',
        'pending': '处理中'
    };
    return statuses[status] || status || '未知';
}

function getContentTypeIcon(contentType) {
    const icons = {
        'video': 'fas fa-video',
        'article': 'fas fa-file-alt',
        'resource': 'fas fa-download',
        'image': 'fas fa-image',
        'audio': 'fas fa-music'
    };
    return icons[contentType] || 'fas fa-file';
}

// 添加复选框事件
function addCheckboxEvents() {
    const checkboxes = document.querySelectorAll('.record-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', () => {
            const recordId = parseInt(checkbox.dataset.id);

            if (checkbox.checked) {
                if (!selectedRecordIds.includes(recordId)) {
                    selectedRecordIds.push(recordId);
                }
            } else {
                selectedRecordIds = selectedRecordIds.filter(id => id !== recordId);

                // 取消全选复选框
                const selectAllCheckbox = document.getElementById('select-all-checkbox');
                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = false;
                }
            }

            // 更新批量操作按钮状态
            updateBatchActionButtons();
        });
    });
}

// 更新批量操作按钮状态
function updateBatchActionButtons() {
    const batchActionButtons = document.querySelectorAll('.batch-action-btn');
    const hasSelected = selectedRecordIds.length > 0;

    batchActionButtons.forEach(btn => {
        if (hasSelected) {
            btn.classList.remove('disabled');
        } else {
            btn.classList.add('disabled');
        }
    });

    // 更新选中数量显示
    const selectedCountEl = document.getElementById('selected-count');
    if (selectedCountEl) {
        selectedCountEl.textContent = selectedRecordIds.length;
    }
}

// 渲染分页
function renderPagination(currentPage, totalPages, totalItems) {
    const paginationContainer = document.getElementById('search-records-pagination');
    if (!paginationContainer) return;

    // 清空分页
    paginationContainer.innerHTML = '';

    // 如果只有一页，不显示分页
    if (totalPages <= 1) return;

    // 创建分页
    const pagination = document.createElement('div');
    pagination.className = 'pagination';

    // 添加总记录数信息
    const info = document.createElement('div');
    info.className = 'pagination-info';
    info.textContent = `共 ${totalItems} 条记录，${totalPages} 页`;
    pagination.appendChild(info);

    // 添加分页链接
    const links = document.createElement('div');
    links.className = 'pagination-links';

    // 上一页
    const prevLink = document.createElement('a');
    prevLink.className = `page-link ${currentPage <= 1 ? 'disabled' : ''}`;
    prevLink.href = '#';
    prevLink.dataset.page = currentPage - 1;
    prevLink.innerHTML = '<i class="fas fa-chevron-left"></i>';
    links.appendChild(prevLink);

    // 页码
    const maxPages = 5; // 最多显示的页码数
    let startPage = Math.max(1, currentPage - Math.floor(maxPages / 2));
    let endPage = Math.min(totalPages, startPage + maxPages - 1);

    if (endPage - startPage + 1 < maxPages) {
        startPage = Math.max(1, endPage - maxPages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
        const pageLink = document.createElement('a');
        pageLink.className = `page-link ${i === currentPage ? 'active' : ''}`;
        pageLink.href = '#';
        pageLink.dataset.page = i;
        pageLink.textContent = i;
        links.appendChild(pageLink);
    }

    // 下一页
    const nextLink = document.createElement('a');
    nextLink.className = `page-link ${currentPage >= totalPages ? 'disabled' : ''}`;
    nextLink.href = '#';
    nextLink.dataset.page = currentPage + 1;
    nextLink.innerHTML = '<i class="fas fa-chevron-right"></i>';
    links.appendChild(nextLink);

    pagination.appendChild(links);
    paginationContainer.appendChild(pagination);
}

// 加载搜索统计
async function loadSearchStats() {
    const statsContainer = document.getElementById('search-stats');
    if (!statsContainer) return;

    try {
        const response = await fetch('/api/search/stats');
        const data = await response.json();

        if (data.success) {
            const { total_searches, platform_stats, recent_searches, favorite_count, content_type_stats } = data.data;

            // 更新统计信息
            statsContainer.innerHTML = `
                <div class="stat-item">
                    <div class="stat-value">${total_searches}</div>
                    <div class="stat-label">总搜索数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${recent_searches}</div>
                    <div class="stat-label">最近7天</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${favorite_count || 0}</div>
                    <div class="stat-label">收藏</div>
                </div>
            `;
        } else {
            console.error('获取搜索统计失败:', data.message);
        }
    } catch (error) {
        console.error('获取搜索统计请求失败:', error);
    }
}

// 初始化搜索历史模态框功能
function initSearchHistoryModalFeatures() {
    // 当搜索历史模态框打开时，初始化其他功能
    const searchHistoryModal = document.getElementById('search-history-modal');
    if (searchHistoryModal && !searchHistoryModal.dataset.initialized) {
        // 标记模态框已初始化，防止重复初始化
        searchHistoryModal.dataset.initialized = 'true';

        // 初始化其他功能
        initSearchHistoryModalEvents();
    }
}

// 初始化搜索历史模态框事件
function initSearchHistoryModalEvents() {
    // 筛选表单提交
    const filterForm = document.getElementById('search-filter-form');
    if (filterForm) {
        filterForm.addEventListener('submit', (e) => {
            e.preventDefault();

            // 获取筛选条件
            const platform = document.getElementById('filter-platform').value;
            const status = document.getElementById('filter-status').value;
            const contentType = document.getElementById('filter-content-type').value;
            const favorite = document.getElementById('filter-favorite').checked;

            // 更新筛选条件
            currentFilters = {
                platform: platform || undefined,
                status: status || undefined,
                content_type: contentType || undefined
            };

            // 如果选择了只显示收藏
            if (favorite) {
                currentFilters.is_favorite = true;
            }

            // 重置页码并加载数据
            currentPage = 1;
            loadSearchHistory();
        });
    }

    // 重置筛选按钮
    const resetFilterBtn = document.getElementById('reset-filter-btn');
    if (resetFilterBtn) {
        resetFilterBtn.addEventListener('click', () => {
            // 重置筛选表单
            const filterForm = document.getElementById('search-filter-form');
            if (filterForm) {
                filterForm.reset();
            }

            // 清空筛选条件
            currentFilters = {};

            // 重置页码并加载数据
            currentPage = 1;
            loadSearchHistory();
        });
    }

    // 分页按钮
    document.addEventListener('click', (e) => {
        if (e.target.classList.contains('page-link')) {
            e.preventDefault();

            const page = parseInt(e.target.dataset.page);
            if (!isNaN(page) && page !== currentPage) {
                currentPage = page;
                loadSearchHistory();
            }
        }
    });
}

// 占位函数（需要在其他地方实现）
function toggleFavorite(recordId) {
    console.log('切换收藏状态:', recordId);
    // TODO: 实现收藏功能
}

function deleteSearchRecord(recordId) {
    console.log('删除搜索记录:', recordId);
    // TODO: 实现删除功能
}

function showNotesModal(recordId, title) {
    console.log('显示笔记模态框:', recordId, title);
    // TODO: 实现笔记功能
}

function showLoginModal() {
    console.log('显示登录模态框');
    // TODO: 实现登录模态框
}

// 兼容旧的函数名（向后兼容）
window.initDownloadFeatures = initSearchHistoryFeatures;
window.showDownloadsModal = showSearchHistoryModal;
