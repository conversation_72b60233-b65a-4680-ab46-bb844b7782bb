#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
哔哩哔哩爬虫模块
用于解析哔哩哔哩视频链接，获取视频信息并下载合并视频
"""

import os
import logging
import re
import requests
import subprocess
import traceback
from functools import lru_cache
from typing import Dict, Any, Optional
from requests.exceptions import ConnectionError, Timeout

from .base_spider import BaseSpider
from ..utils.settings import BILIBILI_COOKIE

# 创建日志记录器
logger = logging.getLogger(__name__)


def clean_text(text):
    """清理文本中的特殊字符和多余空格"""
    if not text:
        return ""
    return re.sub(r'[\r\xa0\x00-\x09\x0b-\x0c\x0e-\x1f\x7f-\xa0\u200b-\u200f\u2028-\u202f\u3000]+', '', text).strip()


@lru_cache(maxsize=20)
def get_final_url(url, cookie=BILIBILI_COOKIE):
    """获取重定向后的最终URL"""
    response = requests.get(
        url,
        headers={
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Cookie': cookie
        },
        allow_redirects=True,
        timeout=10
    )
    final_url = response.url.split('?')[0].split('#')[0]
    return final_url


class BilibiliSpider(BaseSpider):
    """
    哔哩哔哩视频爬虫类
    继承自BaseSpider，实现哔哩哔哩视频解析和下载功能
    """

    # BV号和AV号转换相关常量
    BV_TABLE = "fZodR9XQDSUm21yCkr6zBqiveYah8bt4xsWpHnJE7jL5VG3guMTKNPkmYnpj"
    BV_XOR = 177451812
    BV_ADD = 8728348608

    def __init__(self):
        """初始化哔哩哔哩爬虫"""
        super().__init__("哔哩哔哩爬虫")
        self.platform = "bilibili"

        # 设置请求头，模拟浏览器访问
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Cookie': BILIBILI_COOKIE,
            'Origin': 'https://www.bilibili.com',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }

    def _get_user_friendly_error(self, error):
        """将技术错误转换为用户友好的错误信息"""
        error_str = str(error).lower()

        if isinstance(error, (ConnectionError,)) or 'failed to resolve' in error_str:
            return "网络连接失败，请检查网络连接或稍后重试"
        elif isinstance(error, Timeout) or 'timeout' in error_str:
            return "请求超时，请检查网络连接或稍后重试"
        elif 'max retries exceeded' in error_str:
            return "网络请求失败，服务器可能暂时不可用，请稍后重试"
        elif 'ssl' in error_str or 'certificate' in error_str:
            return "安全连接失败，请检查网络设置"
        elif 'permission denied' in error_str:
            return "访问被拒绝，可能是网络限制或防火墙阻止"
        elif 'invalid url' in error_str or 'url' in error_str:
            return "视频链接格式不正确，请检查链接是否完整"
        elif 'ffmpeg' in error_str:
            return "视频合并失败，请确保系统已安装ffmpeg"
        else:
            return "解析失败，请检查视频链接是否有效或稍后重试"

    def validate_url(self, url: str) -> bool:
        """
        验证哔哩哔哩URL是否有效

        Args:
            url: 待验证的URL

        Returns:
            bool: URL是否有效
        """
        try:
            # 哔哩哔哩URL格式示例:
            # https://www.bilibili.com/video/BV1xx411c7mD
            # https://b23.tv/abc123
            # https://bilibili.com/video/av12345
            bilibili_patterns = [
                r'bilibili\.com',
                r'b23\.tv'
            ]

            return any(re.search(pattern, url) for pattern in bilibili_patterns)
        except Exception as e:
            logger.error(f"URL验证失败: {str(e)}")
            return False

    def extract_video_info(self, url: str) -> Dict[str, Any]:
        """
        从哔哩哔哩URL提取视频信息并下载合并视频

        Args:
            url: 哔哩哔哩视频URL

        Returns:
            Dict: 包含视频信息的字典
        """
        try:
            # 获取重定向后的URL
            final_url = get_final_url(url)
            logger.info(f"获取到最终URL: {final_url}")

            # 更新请求头
            headers = self.headers.copy()
            headers.update({'referer': final_url})

            # 获取网页内容
            clean_url = final_url.partition('?')[0]
            response = requests.get(url=clean_url, headers=headers, timeout=10)
            html = response.text

            # 提取视频信息
            title_match = re.search(r'"title":"(.*?)"', html)
            if not title_match:
                raise Exception("无法提取视频标题")
            title = clean_text(title_match.group(1))

            # 提取作者信息
            author_match = re.search(r'"owner":\{"mid":\d+,"name":"(.*?)"', html)
            author = clean_text(author_match.group(1)) if author_match else "未知作者"

            # 提取视频时长
            duration_match = re.search(r'"duration":(\d+)', html)
            duration = int(duration_match.group(1)) if duration_match else 0

            # 提取封面图片
            pic_match = re.search(r'"pic":"(.*?)"', html)
            cover_url = pic_match.group(1).replace('\\/', '/') if pic_match else ""

            # 提取描述
            desc_match = re.search(r'"desc":"(.*?)"', html)
            description = clean_text(desc_match.group(1)) if desc_match else ""

            # 获取视频和音频分段信息
            video_matches = re.findall(r'"baseUrl":"(.*?)"', html)
            audio_matches = re.findall(r'"id":30280,"baseUrl":"(.*?)",', html)

            if not video_matches or not audio_matches:
                raise Exception("无法获取视频或音频流地址")

            video_url = video_matches[0].replace('\\/', '/')
            audio_url = audio_matches[0].replace('\\/', '/')

            # 下载并合并视频（使用本地文件以避免防盗链问题）
            merged_video_url = self._download_and_merge_video(title, video_url, audio_url, headers)

            # 构建返回数据
            result = {
                'title': title,
                'author': author,
                'videoUrl': merged_video_url,  # 使用本地合并后的视频URL
                'coverUrl': cover_url,
                'description': description,
                'duration': duration,
                'view_count': 0,  # 可以从HTML中提取
                'like_count': 0,  # 可以从HTML中提取
                'coin_count': 0,  # 可以从HTML中提取
                'share_count': 0,  # 可以从HTML中提取
                'bvid': self._extract_bvid(final_url),
                'aid': 0,  # 可以从BV号转换
                'note': '视频已下载到本地服务器'
            }

            logger.info(f"成功解析视频信息: {title}")
            return result

        except Exception as e:
            logger.error(f"提取视频信息失败: {str(e)}")
            logger.error(traceback.format_exc())
            # 抛出用户友好的错误信息
            user_error = self._get_user_friendly_error(e)
            raise Exception(user_error)

    def _download_and_merge_video(self, title: str, video_url: str, audio_url: str, headers: dict) -> str:
        """
        下载视频和音频片段并合并

        Args:
            title: 视频标题
            video_url: 视频流URL
            audio_url: 音频流URL
            headers: 请求头

        Returns:
            str: 合并后的视频访问URL
        """
        try:
            # 获取spiders目录的绝对路径
            spiders_dir = os.path.dirname(os.path.abspath(__file__))

            # 确保必要的文件夹存在
            video_dir = os.path.join(spiders_dir, "video")
            audio_dir = os.path.join(spiders_dir, "audio")
            data_dir = os.path.join(spiders_dir, "data")

            for folder in [video_dir, audio_dir, data_dir]:
                if not os.path.exists(folder):
                    os.makedirs(folder, exist_ok=True)

            # 清理文件名中的特殊字符
            safe_title = clean_text(title)
            output_path = os.path.join(data_dir, f"{safe_title}.mp4")

            # 生成访问URL（使用Flask默认端口5000）
            web_url = f"http://127.0.0.1:5000/media/videos/{safe_title}.mp4"

            # 如果文件已存在，直接返回
            if os.path.exists(output_path):
                logger.info(f"视频文件已存在: {safe_title}")
                return web_url

            # 下载视频片段
            logger.info(f"开始下载视频片段: {safe_title}")
            video_response = requests.get(video_url, headers=headers, timeout=30)
            video_path = os.path.join(video_dir, f"{safe_title}.mp4")
            with open(video_path, 'wb') as f:
                f.write(video_response.content)

            # 下载音频片段
            logger.info(f"开始下载音频片段: {safe_title}")
            audio_response = requests.get(audio_url, headers=headers, timeout=30)
            audio_path = os.path.join(audio_dir, f"{safe_title}.mp3")
            with open(audio_path, 'wb') as f:
                f.write(audio_response.content)

            # 使用ffmpeg合并视频和音频
            logger.info(f"开始合并视频和音频: {safe_title}")
            cmd = f'ffmpeg -hide_banner -i "{video_path}" -i "{audio_path}" -c:v copy -c:a aac -strict experimental "{output_path}"'

            # 修复编码问题，使用正确的编码处理ffmpeg输出
            try:
                result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True, encoding='utf-8', errors='ignore')
            except subprocess.CalledProcessError:
                # 如果UTF-8失败，尝试使用系统默认编码
                result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True, encoding='gbk', errors='ignore')

            # 清理临时文件
            try:
                os.remove(video_path)
                os.remove(audio_path)
            except Exception as cleanup_error:
                logger.warning(f"清理临时文件失败: {cleanup_error}")

            logger.info(f"视频合并完成: {safe_title}")
            return web_url

        except subprocess.CalledProcessError as e:
            logger.error(f"ffmpeg合并失败: {e}")
            raise Exception("视频合并失败，请确保系统已安装ffmpeg")
        except Exception as e:
            logger.error(f"下载和合并视频失败: {str(e)}")
            raise Exception(f"下载失败: {str(e)}")

    def _extract_bvid(self, url: str) -> str:
        """从URL中提取BV号"""
        bv_match = re.search(r'BV([a-zA-Z0-9]+)', url)
        return f"BV{bv_match.group(1)}" if bv_match else ""

    def execute(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行哔哩哔哩爬虫任务

        Args:
            task: 任务参数
                {
                    "video_url": "哔哩哔哩视频URL"
                }

        Returns:
            Dict: 执行结果
        """
        try:
            video_url = task.get("video_url")

            if not video_url:
                return {
                    "success": False,
                    "message": "请提供视频链接",
                    "data": None
                }

            # 验证URL
            if not self.validate_url(video_url):
                return {
                    "success": False,
                    "message": "请提供有效的哔哩哔哩视频链接",
                    "data": None
                }

            # 提取视频信息
            video_info = self.extract_video_info(video_url)

            if not video_info:
                return {
                    "success": False,
                    "message": "无法获取视频信息，请检查链接是否有效",
                    "data": None
                }

            return {
                "success": True,
                "message": "解析成功",
                "data": video_info
            }

        except Exception as e:
            logger.error(f"哔哩哔哩爬虫执行失败: {str(e)}")
            logger.error(traceback.format_exc())

            # 返回用户友好的错误信息
            user_error = str(e) if any(keyword in str(e) for keyword in
                                     ['网络连接失败', '请求超时', '视频', '链接', '格式', 'ffmpeg']) else self._get_user_friendly_error(e)

            return {
                "success": False,
                "message": user_error,
                "data": None
            }

    def check_status(self) -> Dict[str, Any]:
        """
        检查哔哩哔哩爬虫状态

        Returns:
            Dict: 状态信息
        """
        return {
            "name": self.name,
            "platform": self.platform,
            "status": "ready",
            "version": "1.0.0"
        }

    def bv_to_av(self, bvid: str) -> Optional[int]:
        """
        BV号转AV号

        Args:
            bvid: BV号

        Returns:
            Optional[int]: AV号，转换失败返回None
        """
        try:
            if not bvid.startswith("BV"):
                return None

            bvid = bvid[2:]  # 去掉BV前缀

            # 构建映射表
            table = {}
            for i, char in enumerate(self.BV_TABLE):
                table[char] = i

            # 转换算法
            r = 0
            for i, char in enumerate([bvid[i] for i in [11, 10, 3, 8, 4, 6]]):
                r += table[char] * (58 ** i)

            return (r - self.BV_ADD) ^ self.BV_XOR

        except Exception as e:
            logger.error(f"BV号转AV号失败: {str(e)}")
            return None

    def av_to_bv(self, aid: int) -> Optional[str]:
        """
        AV号转BV号

        Args:
            aid: AV号

        Returns:
            Optional[str]: BV号，转换失败返回None
        """
        try:
            # 转换算法
            x = (aid ^ self.BV_XOR) + self.BV_ADD
            r = list("BV1  4 1 7  ")

            for i, pos in enumerate([11, 10, 3, 8, 4, 6]):
                r[pos] = self.BV_TABLE[x // (58 ** i) % 58]

            return "".join(r)

        except Exception as e:
            logger.error(f"AV号转BV号失败: {str(e)}")
            return None


# 全局爬虫实例（延迟初始化）
_bilibili_spider = None


def get_bilibili_spider() -> BilibiliSpider:
    """
    获取哔哩哔哩爬虫实例（延迟初始化）

    Returns:
        BilibiliSpider: 爬虫实例
    """
    global _bilibili_spider
    if _bilibili_spider is None:
        _bilibili_spider = BilibiliSpider()
    return _bilibili_spider


# 测试案例（已移除，使用类方法进行测试）
# if __name__ == '__main__':
#     spider = BilibiliSpider()
#     result = spider.execute({
#         "video_url": "https://www.bilibili.com/video/BV1xx411c7mD"
#     })
#     print(result)
