const __vite__fileDeps=["./PlotlyPlot-Co8MzvUf.js","./index-Ccc2t4AG.js","./index-Dz5CWoA7.css","./BokehPlot-CHWxUyLl.js","./BokehPlot-Cd-I2ErV.css","./AltairPlot-CnT8hBPg.js","./color-BEUIZb6Y.js","./vega-tooltip.module-DosJ6Ifo.js","./time-Bgyi_H-V.js","./step-Ce-xBr2D.js","./linear-CV3SENcB.js","./init-Dmth1JHB.js","./dsv-DB8NKgIY.js","./range-OtVwhkKS.js","./ordinal-BeghXfj9.js","./arc-Ctxh2KTd.js","./dispatch-kxCwF96_.js","./AltairPlot-CSe9xcFj.css","./MatplotlibPlot-CgZqwhB9.js","./MatplotlibPlot-AF_QcUtc.css"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{_ as k}from"./index-Ccc2t4AG.js";/* empty css                                                        */import"./MarkdownCode.svelte_svelte_type_style_lang-DzFJaVYu.js";import{E as Q}from"./Empty-ZqppqzTN.js";const{SvelteComponent:U,append:g,attr:s,detach:W,init:X,insert:$,noop:T,safe_not_equal:ee,svg_element:b}=window.__gradio__svelte__internal;function te(u){let e,o,l,n,_,r,f;return{c(){e=b("svg"),o=b("circle"),l=b("circle"),n=b("circle"),_=b("circle"),r=b("circle"),f=b("path"),s(o,"cx","20"),s(o,"cy","4"),s(o,"r","2"),s(o,"fill","currentColor"),s(l,"cx","8"),s(l,"cy","16"),s(l,"r","2"),s(l,"fill","currentColor"),s(n,"cx","28"),s(n,"cy","12"),s(n,"r","2"),s(n,"fill","currentColor"),s(_,"cx","11"),s(_,"cy","7"),s(_,"r","2"),s(_,"fill","currentColor"),s(r,"cx","16"),s(r,"cy","24"),s(r,"r","2"),s(r,"fill","currentColor"),s(f,"fill","currentColor"),s(f,"d","M30 3.413L28.586 2L4 26.585V2H2v26a2 2 0 0 0 2 2h26v-2H5.413Z"),s(e,"xmlns","http://www.w3.org/2000/svg"),s(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),s(e,"aria-hidden","true"),s(e,"role","img"),s(e,"class","iconify iconify--carbon"),s(e,"width","100%"),s(e,"height","100%"),s(e,"preserveAspectRatio","xMidYMid meet"),s(e,"viewBox","0 0 32 32")},m(t,c){$(t,e,c),g(e,o),g(e,l),g(e,n),g(e,_),g(e,r),g(e,f)},p:T,i:T,o:T,d(t){t&&W(e)}}}let oe=class extends U{constructor(e){super(),X(this,e,null,te,ee,{})}};const{SvelteComponent:le,add_flush_callback:ne,bind:N,binding_callbacks:j,bubble:z,check_outros:L,construct_svelte_component:H,create_component:v,destroy_component:y,detach:M,empty:O,flush:h,group_outros:A,init:ie,insert:D,mount_component:P,noop:re,safe_not_equal:Y,transition_in:m,transition_out:d}=window.__gradio__svelte__internal,{createEventDispatcher:se}=window.__gradio__svelte__internal;function _e(u){let e,o;return e=new Q({props:{unpadded_box:!0,size:"large",$$slots:{default:[ae]},$$scope:{ctx:u}}}),{c(){v(e.$$.fragment)},m(l,n){P(e,l,n),o=!0},p(l,n){const _={};n&4194304&&(_.$$scope={dirty:n,ctx:l}),e.$set(_)},i(l){o||(m(e.$$.fragment,l),o=!0)},o(l){d(e.$$.fragment,l),o=!1},d(l){y(e,l)}}}function ce(u){let e=u[12],o,l,n=B(u);return{c(){n.c(),o=O()},m(_,r){n.m(_,r),D(_,o,r),l=!0},p(_,r){r&4096&&Y(e,e=_[12])?(A(),d(n,1,1,re),L(),n=B(_),n.c(),m(n,1),n.m(o.parentNode,o)):n.p(_,r)},i(_){l||(m(n),l=!0)},o(_){d(n),l=!1},d(_){_&&M(o),n.d(_)}}}function ae(u){let e,o;return e=new oe({}),{c(){v(e.$$.fragment)},m(l,n){P(e,l,n),o=!0},i(l){o||(m(e.$$.fragment,l),o=!0)},o(l){d(e.$$.fragment,l),o=!1},d(l){y(e,l)}}}function B(u){let e,o,l,n;function _(t){u[16](t)}var r=u[10];function f(t,c){let a={value:t[0],colors:t[1],theme_mode:t[3],show_label:t[2],caption:t[4],bokeh_version:t[5],show_actions_button:t[6],gradio:t[7],_selectable:t[9],x_lim:t[8]};return t[11]!==void 0&&(a.loaded_plotly_css=t[11]),{props:a}}return r&&(e=H(r,f(u)),j.push(()=>N(e,"loaded_plotly_css",_)),e.$on("load",u[17]),e.$on("select",u[18])),{c(){e&&v(e.$$.fragment),l=O()},m(t,c){e&&P(e,t,c),D(t,l,c),n=!0},p(t,c){if(c&1024&&r!==(r=t[10])){if(e){A();const a=e;d(a.$$.fragment,1,0,()=>{y(a,1)}),L()}r?(e=H(r,f(t)),j.push(()=>N(e,"loaded_plotly_css",_)),e.$on("load",t[17]),e.$on("select",t[18]),v(e.$$.fragment),m(e.$$.fragment,1),P(e,l.parentNode,l)):e=null}else if(r){const a={};c&1&&(a.value=t[0]),c&2&&(a.colors=t[1]),c&8&&(a.theme_mode=t[3]),c&4&&(a.show_label=t[2]),c&16&&(a.caption=t[4]),c&32&&(a.bokeh_version=t[5]),c&64&&(a.show_actions_button=t[6]),c&128&&(a.gradio=t[7]),c&512&&(a._selectable=t[9]),c&256&&(a.x_lim=t[8]),!o&&c&2048&&(o=!0,a.loaded_plotly_css=t[11],ne(()=>o=!1)),e.$set(a)}},i(t){n||(e&&m(e.$$.fragment,t),n=!0)},o(t){e&&d(e.$$.fragment,t),n=!1},d(t){t&&M(l),e&&y(e,t)}}}function ue(u){let e,o,l,n;const _=[ce,_e],r=[];function f(t,c){return t[0]&&t[10]?0:1}return e=f(u),o=r[e]=_[e](u),{c(){o.c(),l=O()},m(t,c){r[e].m(t,c),D(t,l,c),n=!0},p(t,[c]){let a=e;e=f(t),e===a?r[e].p(t,c):(A(),d(r[a],1,1,()=>{r[a]=null}),L(),o=r[e],o?o.p(t,c):(o=r[e]=_[e](t),o.c()),m(o,1),o.m(l.parentNode,l))},i(t){n||(m(o),n=!0)},o(t){d(o),n=!1},d(t){t&&M(l),r[e].d(t)}}}function fe(u,e,o){let{value:l}=e,n,{colors:_=[]}=e,{show_label:r}=e,{theme_mode:f}=e,{caption:t}=e,{bokeh_version:c}=e,{show_actions_button:a}=e,{gradio:R}=e,{x_lim:S=null}=e,{_selectable:V}=e,p=null,E=l?.type,C=!1;const Z=se(),I={plotly:()=>k(()=>import("./PlotlyPlot-Co8MzvUf.js"),__vite__mapDeps([0,1,2]),import.meta.url),bokeh:()=>k(()=>import("./BokehPlot-CHWxUyLl.js"),__vite__mapDeps([3,4]),import.meta.url),altair:()=>k(()=>import("./AltairPlot-CnT8hBPg.js"),__vite__mapDeps([5,1,2,6,7,8,9,10,11,12,13,14,15,16,17]),import.meta.url),matplotlib:()=>k(()=>import("./MatplotlibPlot-CgZqwhB9.js"),__vite__mapDeps([18,19]),import.meta.url)};let w={};const x=typeof window<"u";let q=0;function F(i){C=i,o(11,C)}function G(i){z.call(this,u,i)}function J(i){z.call(this,u,i)}return u.$$set=i=>{"value"in i&&o(0,l=i.value),"colors"in i&&o(1,_=i.colors),"show_label"in i&&o(2,r=i.show_label),"theme_mode"in i&&o(3,f=i.theme_mode),"caption"in i&&o(4,t=i.caption),"bokeh_version"in i&&o(5,c=i.bokeh_version),"show_actions_button"in i&&o(6,a=i.show_actions_button),"gradio"in i&&o(7,R=i.gradio),"x_lim"in i&&o(8,S=i.x_lim),"_selectable"in i&&o(9,V=i._selectable)},u.$$.update=()=>{if(u.$$.dirty&62465&&l!==n){o(12,q+=1);let i=l?.type;i!==E&&o(10,p=null),i&&i in I&&x&&(w[i]?o(10,p=w[i]):I[i]().then(K=>{o(10,p=K.default),o(15,w[i]=p,w)})),o(13,n=l),o(14,E=i),Z("change")}},[l,_,r,f,t,c,a,R,S,V,p,C,q,n,E,w,F,G,J]}class he extends le{constructor(e){super(),ie(this,e,fe,ue,Y,{value:0,colors:1,show_label:2,theme_mode:3,caption:4,bokeh_version:5,show_actions_button:6,gradio:7,x_lim:8,_selectable:9})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),h()}get colors(){return this.$$.ctx[1]}set colors(e){this.$$set({colors:e}),h()}get show_label(){return this.$$.ctx[2]}set show_label(e){this.$$set({show_label:e}),h()}get theme_mode(){return this.$$.ctx[3]}set theme_mode(e){this.$$set({theme_mode:e}),h()}get caption(){return this.$$.ctx[4]}set caption(e){this.$$set({caption:e}),h()}get bokeh_version(){return this.$$.ctx[5]}set bokeh_version(e){this.$$set({bokeh_version:e}),h()}get show_actions_button(){return this.$$.ctx[6]}set show_actions_button(e){this.$$set({show_actions_button:e}),h()}get gradio(){return this.$$.ctx[7]}set gradio(e){this.$$set({gradio:e}),h()}get x_lim(){return this.$$.ctx[8]}set x_lim(e){this.$$set({x_lim:e}),h()}get _selectable(){return this.$$.ctx[9]}set _selectable(e){this.$$set({_selectable:e}),h()}}const we=Object.freeze(Object.defineProperty({__proto__:null,default:he},Symbol.toStringTag,{value:"Module"}));export{oe as P,he as a,we as b};
//# sourceMappingURL=Plot-DHdxFihW.js.map
