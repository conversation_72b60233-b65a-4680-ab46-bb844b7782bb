2025-06-10 18:00:13,990 - werkzeug - INFO -  * Restarting with stat
2025-06-10 18:00:15,043 - backend.utils.scheduler - INFO - 添加定时任务: 清理过期Pro用户, 间隔: 6小时
2025-06-10 18:00:15,043 - backend.utils.scheduler - INFO - 添加定时任务: 每日重置用户统计, 间隔: 24小时
2025-06-10 18:00:15,044 - backend.utils.scheduler - INFO - 执行定时任务: 清理过期Pro用户
2025-06-10 18:00:15,044 - backend.utils.scheduler - INFO - 任务调度器已启动
2025-06-10 18:00:15,045 - backend.utils.scheduler - INFO - 定时任务调度器初始化完成
2025-06-10 18:00:15,045 - supertools - INFO - 定时任务调度器初始化成功
2025-06-10 18:00:15,784 - backend.utils.scheduler - INFO - 任务 清理过期Pro用户 执行完成，结果: 降级了 0 个过期用户
2025-06-10 18:00:15,785 - backend.utils.scheduler - INFO - 执行定时任务: 每日重置用户统计
2025-06-10 18:00:15,799 - backend.utils.scheduler - INFO - 任务 每日重置用户统计 执行完成，结果: 每日重置完成: 用户统计 0 个，VIP账号 0 个
2025-06-10 18:00:15,807 - werkzeug - WARNING -  * Debugger is active!
2025-06-10 18:01:47,644 - supertools.auth - INFO - 验证码发送到 13157568559: 946987
2025-06-10 18:02:10,248 - backend.api.search_api - INFO - 获取搜索历史 - 用户ID: 1, 用户名: yumu
2025-06-10 18:02:10,248 - backend.api.search_api - INFO - 查询参数: page=1, per_page=10, platform=None, content_type=None, status=None
2025-06-10 18:02:10,255 - backend.api.search_api - INFO - 用户 1 总共有 5 条搜索记录
2025-06-10 18:02:10,265 - backend.api.search_api - INFO - 分页结果: total=5, pages=1, 当前页记录数=5
2025-06-10 18:02:10,266 - backend.api.search_api - INFO - 返回数据: records数量=5, total=5
