const __vite__fileDeps=["./module-BdtwaIMt.js","./module-C-VadMaF.js","./index-Ccc2t4AG.js","./index-Dz5CWoA7.css","./module-BA06XY8_.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{_ as et,n as un}from"./index-Ccc2t4AG.js";import{a as cn}from"./Upload-D2lWV7KS.js";import{M as St}from"./ModifyUpload-CkvPtjlQ.js";/* empty css                                                        */import"./MarkdownCode.svelte_svelte_type_style_lang-DzFJaVYu.js";import{B as _n}from"./BlockLabel-3KxTaaiM.js";import{M as dn}from"./Music-CDm0RGMk.js";import{a as fn,S as mn}from"./SelectSource-DC1-vFVA.js";import"./StreamingBar.svelte_svelte_type_style_lang-CDNxkBIr.js";import{S as gn}from"./StreamingBar-BU9S4hA7.js";import{s as tt,W as hn,p as nt,a as Ve,A as pn}from"./AudioPlayer-UyFUW52_.js";import{P as bn}from"./Trim-JQYgj7Jd.js";import{f as se}from"./utils-BsGrhMNe.js";function He(n,e,t,i){return new(t||(t=Promise))(function(o,a){function r(u){try{l(i.next(u))}catch(s){a(s)}}function c(u){try{l(i.throw(u))}catch(s){a(s)}}function l(u){var s;u.done?o(u.value):(s=u.value,s instanceof t?s:new t(function(_){_(s)})).then(r,c)}l((i=i.apply(n,[])).next())})}class vn{constructor(){this.listeners={},this.on=this.addEventListener,this.un=this.removeEventListener}addEventListener(e,t,i){if(this.listeners[e]||(this.listeners[e]=new Set),this.listeners[e].add(t),i?.once){const o=()=>{this.removeEventListener(e,o),this.removeEventListener(e,t)};return this.addEventListener(e,o),o}return()=>this.removeEventListener(e,t)}removeEventListener(e,t){var i;(i=this.listeners[e])===null||i===void 0||i.delete(t)}once(e,t){return this.on(e,t,{once:!0})}unAll(){this.listeners={}}emit(e,...t){this.listeners[e]&&this.listeners[e].forEach(i=>i(...t))}}class wn extends vn{constructor(e){super(),this.subscriptions=[],this.options=e}onInit(){}init(e){this.wavesurfer=e,this.onInit()}destroy(){this.emit("destroy"),this.subscriptions.forEach(e=>e())}}const kn=["audio/webm","audio/wav","audio/mpeg","audio/mp4","audio/mp3"];class Pe extends wn{constructor(e){var t;super(Object.assign(Object.assign({},e),{audioBitsPerSecond:(t=e.audioBitsPerSecond)!==null&&t!==void 0?t:128e3})),this.stream=null,this.mediaRecorder=null}static create(e){return new Pe(e||{})}renderMicStream(e){const t=new AudioContext,i=t.createMediaStreamSource(e),o=t.createAnalyser();i.connect(o);const a=o.frequencyBinCount,r=new Float32Array(a),c=a/t.sampleRate;let l;const u=()=>{o.getFloatTimeDomainData(r),this.wavesurfer&&(this.wavesurfer.options.cursorWidth=0,this.wavesurfer.options.interact=!1,this.wavesurfer.load("",[r],c)),l=requestAnimationFrame(u)};return u(),()=>{cancelAnimationFrame(l),i?.disconnect(),t?.close()}}startMic(e){return He(this,void 0,void 0,function*(){let t;try{t=yield navigator.mediaDevices.getUserMedia({audio:!e?.deviceId||{deviceId:e.deviceId}})}catch(o){throw new Error("Error accessing the microphone: "+o.message)}const i=this.renderMicStream(t);return this.subscriptions.push(this.once("destroy",i)),this.stream=t,t})}stopMic(){this.stream&&(this.stream.getTracks().forEach(e=>e.stop()),this.stream=null,this.mediaRecorder=null)}startRecording(e){return He(this,void 0,void 0,function*(){const t=this.stream||(yield this.startMic(e)),i=this.mediaRecorder||new MediaRecorder(t,{mimeType:this.options.mimeType||kn.find(a=>MediaRecorder.isTypeSupported(a)),audioBitsPerSecond:this.options.audioBitsPerSecond});this.mediaRecorder=i,this.stopRecording();const o=[];i.ondataavailable=a=>{a.data.size>0&&o.push(a.data)},i.onstop=()=>{var a;const r=new Blob(o,{type:i.mimeType});this.emit("record-end",r),this.options.renderRecordedAudio!==!1&&((a=this.wavesurfer)===null||a===void 0||a.load(URL.createObjectURL(r)))},i.start(),this.emit("record-start")})}isRecording(){var e;return((e=this.mediaRecorder)===null||e===void 0?void 0:e.state)==="recording"}isPaused(){var e;return((e=this.mediaRecorder)===null||e===void 0?void 0:e.state)==="paused"}stopRecording(){var e;this.isRecording()&&((e=this.mediaRecorder)===null||e===void 0||e.stop())}pauseRecording(){var e;this.isRecording()&&((e=this.mediaRecorder)===null||e===void 0||e.pause(),this.emit("record-pause"))}resumeRecording(){var e;this.isPaused()&&((e=this.mediaRecorder)===null||e===void 0||e.resume(),this.emit("record-resume"))}static getAvailableAudioDevices(){return He(this,void 0,void 0,function*(){return navigator.mediaDevices.enumerateDevices().then(e=>e.filter(t=>t.kind==="audioinput"))})}destroy(){super.destroy(),this.stopRecording(),this.stopMic()}}const{SvelteComponent:yn,append:At,attr:it,destroy_each:Dn,detach:Ne,element:Ge,empty:Rn,ensure_array_like:ot,flush:rt,init:En,insert:Fe,noop:st,safe_not_equal:Mn,set_data:Lt,set_input_value:Ye,text:Pt}=window.__gradio__svelte__internal,{createEventDispatcher:Sn}=window.__gradio__svelte__internal;function lt(n,e,t){const i=n.slice();return i[3]=e[t],i}function An(n){let e,t=ot(n[0]),i=[];for(let o=0;o<t.length;o+=1)i[o]=at(lt(n,t,o));return{c(){for(let o=0;o<i.length;o+=1)i[o].c();e=Rn()},m(o,a){for(let r=0;r<i.length;r+=1)i[r]&&i[r].m(o,a);Fe(o,e,a)},p(o,a){if(a&1){t=ot(o[0]);let r;for(r=0;r<t.length;r+=1){const c=lt(o,t,r);i[r]?i[r].p(c,a):(i[r]=at(c),i[r].c(),i[r].m(e.parentNode,e))}for(;r<i.length;r+=1)i[r].d(1);i.length=t.length}},d(o){o&&Ne(e),Dn(i,o)}}}function Ln(n){let e,t=n[1]("audio.no_microphone")+"",i;return{c(){e=Ge("option"),i=Pt(t),e.__value="",Ye(e,e.__value)},m(o,a){Fe(o,e,a),At(e,i)},p(o,a){a&2&&t!==(t=o[1]("audio.no_microphone")+"")&&Lt(i,t)},d(o){o&&Ne(e)}}}function at(n){let e,t=n[3].label+"",i,o;return{c(){e=Ge("option"),i=Pt(t),e.__value=o=n[3].deviceId,Ye(e,e.__value)},m(a,r){Fe(a,e,r),At(e,i)},p(a,r){r&1&&t!==(t=a[3].label+"")&&Lt(i,t),r&1&&o!==(o=a[3].deviceId)&&(e.__value=o,Ye(e,e.__value))},d(a){a&&Ne(e)}}}function Pn(n){let e,t;function i(r,c){return r[0].length===0?Ln:An}let o=i(n),a=o(n);return{c(){e=Ge("select"),a.c(),it(e,"class","mic-select svelte-1ya9x7a"),it(e,"aria-label","Select input device"),e.disabled=t=n[0].length===0},m(r,c){Fe(r,e,c),a.m(e,null)},p(r,[c]){o===(o=i(r))&&a?a.p(r,c):(a.d(1),a=o(r),a&&(a.c(),a.m(e,null))),c&1&&t!==(t=r[0].length===0)&&(e.disabled=t)},i:st,o:st,d(r){r&&Ne(e),a.d()}}}function Cn(n,e,t){let{i18n:i}=e,{micDevices:o=[]}=e;const a=Sn();return n.$$set=r=>{"i18n"in r&&t(1,i=r.i18n),"micDevices"in r&&t(0,o=r.micDevices)},n.$$.update=()=>{if(n.$$.dirty&2&&typeof window<"u")try{let r=[];Pe.getAvailableAudioDevices().then(c=>{t(0,o=c),c.forEach(l=>{l.deviceId&&r.push(l)}),t(0,o=r)})}catch(r){throw r instanceof DOMException&&r.name=="NotAllowedError"&&a("error",i("audio.allow_recording_access")),r}},[o,i]}class Ct extends yn{constructor(e){super(),En(this,e,Cn,Pn,Mn,{i18n:1,micDevices:0})}get i18n(){return this.$$.ctx[1]}set i18n(e){this.$$set({i18n:e}),rt()}get micDevices(){return this.$$.ctx[0]}set micDevices(e){this.$$set({micDevices:e}),rt()}}const{SvelteComponent:Bn,add_flush_callback:In,append:I,attr:F,bind:zn,binding_callbacks:he,create_component:ut,destroy_component:ct,detach:Bt,element:ie,flush:me,init:Tn,insert:It,listen:ye,mount_component:_t,run_all:Un,safe_not_equal:jn,set_data:De,space:ge,text:Re,transition_in:dt,transition_out:ft}=window.__gradio__svelte__internal;function mt(n){let e,t;return{c(){e=ie("time"),t=Re(n[2]),F(e,"class","duration-button duration svelte-1oiuk2f")},m(i,o){It(i,e,o),I(e,t)},p(i,o){o&4&&De(t,i[2])},d(i){i&&Bt(e)}}}function qn(n){let e,t,i,o=n[1]("audio.record")+"",a,r,c,l=n[1]("audio.stop")+"",u,s,_,f,b=n[1]("audio.stop")+"",k,D,h,y,g,v,P=n[1]("audio.resume")+"",w,C,z,B,O,S,p,le;y=new bn({});let A=n[4]&&!n[3]&&mt(n);function H(R){n[23](R)}let de={i18n:n[1]};return n[5]!==void 0&&(de.micDevices=n[5]),B=new Ct({props:de}),he.push(()=>zn(B,"micDevices",H)),{c(){e=ie("div"),t=ie("div"),i=ie("button"),a=Re(o),r=ge(),c=ie("button"),u=Re(l),_=ge(),f=ie("button"),k=Re(b),D=ge(),h=ie("button"),ut(y.$$.fragment),g=ge(),v=ie("button"),w=Re(P),C=ge(),A&&A.c(),z=ge(),ut(B.$$.fragment),F(i,"class","record record-button svelte-1oiuk2f"),F(c,"class",s="stop-button "+(n[0].isPaused()?"stop-button-paused":"")+" svelte-1oiuk2f"),F(f,"id","stop-paused"),F(f,"class","stop-button-paused svelte-1oiuk2f"),F(h,"aria-label","pause"),F(h,"class","pause-button svelte-1oiuk2f"),F(v,"class","resume-button svelte-1oiuk2f"),F(t,"class","wrapper svelte-1oiuk2f"),F(e,"class","controls svelte-1oiuk2f")},m(R,E){It(R,e,E),I(e,t),I(t,i),I(i,a),n[13](i),I(t,r),I(t,c),I(c,u),n[15](c),I(t,_),I(t,f),I(f,k),n[17](f),I(t,D),I(t,h),_t(y,h,null),n[19](h),I(t,g),I(t,v),I(v,w),n[21](v),I(t,C),A&&A.m(t,null),I(e,z),_t(B,e,null),S=!0,p||(le=[ye(i,"click",n[14]),ye(c,"click",n[16]),ye(f,"click",n[18]),ye(h,"click",n[20]),ye(v,"click",n[22])],p=!0)},p(R,[E]){(!S||E&2)&&o!==(o=R[1]("audio.record")+"")&&De(a,o),(!S||E&2)&&l!==(l=R[1]("audio.stop")+"")&&De(u,l),(!S||E&1&&s!==(s="stop-button "+(R[0].isPaused()?"stop-button-paused":"")+" svelte-1oiuk2f"))&&F(c,"class",s),(!S||E&2)&&b!==(b=R[1]("audio.stop")+"")&&De(k,b),(!S||E&2)&&P!==(P=R[1]("audio.resume")+"")&&De(w,P),R[4]&&!R[3]?A?A.p(R,E):(A=mt(R),A.c(),A.m(t,null)):A&&(A.d(1),A=null);const j={};E&2&&(j.i18n=R[1]),!O&&E&32&&(O=!0,j.micDevices=R[5],In(()=>O=!1)),B.$set(j)},i(R){S||(dt(y.$$.fragment,R),dt(B.$$.fragment,R),S=!0)},o(R){ft(y.$$.fragment,R),ft(B.$$.fragment,R),S=!1},d(R){R&&Bt(e),n[13](null),n[15](null),n[17](null),ct(y),n[19](null),n[21](null),A&&A.d(),ct(B),p=!1,Un(le)}}}function On(n,e,t){let{record:i}=e,{i18n:o}=e,{recording:a=!1}=e,r=[],c,l,u,s,_,f=!1,{record_time:b}=e,{show_recording_waveform:k}=e,{timing:D=!1}=e;function h(p){he[p?"unshift":"push"](()=>{c=p,t(6,c),t(0,i)})}const y=()=>i.startRecording();function g(p){he[p?"unshift":"push"](()=>{s=p,t(9,s),t(0,i)})}const v=()=>{i.isPaused()&&(i.resumeRecording(),i.stopRecording()),i.stopRecording()};function P(p){he[p?"unshift":"push"](()=>{_=p,t(10,_),t(0,i)})}const w=()=>{i.isPaused()&&(i.resumeRecording(),i.stopRecording()),i.stopRecording()};function C(p){he[p?"unshift":"push"](()=>{l=p,t(7,l),t(0,i)})}const z=()=>i.pauseRecording();function B(p){he[p?"unshift":"push"](()=>{u=p,t(8,u),t(0,i)})}const O=()=>i.resumeRecording();function S(p){r=p,t(5,r)}return n.$$set=p=>{"record"in p&&t(0,i=p.record),"i18n"in p&&t(1,o=p.i18n),"recording"in p&&t(11,a=p.recording),"record_time"in p&&t(2,b=p.record_time),"show_recording_waveform"in p&&t(3,k=p.show_recording_waveform),"timing"in p&&t(4,D=p.timing)},n.$$.update=()=>{n.$$.dirty&1&&i.on("record-start",()=>{i.startMic(),t(6,c.style.display="none",c),t(9,s.style.display="flex",s),t(7,l.style.display="block",l)}),n.$$.dirty&1&&i.on("record-end",()=>{i.isPaused()&&(i.resumeRecording(),i.stopRecording()),i.stopMic(),t(6,c.style.display="flex",c),t(9,s.style.display="none",s),t(7,l.style.display="none",l),t(6,c.disabled=!1,c)}),n.$$.dirty&1&&i.on("record-pause",()=>{t(7,l.style.display="none",l),t(8,u.style.display="block",u),t(9,s.style.display="none",s),t(10,_.style.display="flex",_)}),n.$$.dirty&1&&i.on("record-resume",()=>{t(7,l.style.display="block",l),t(8,u.style.display="none",u),t(6,c.style.display="none",c),t(9,s.style.display="flex",s),t(10,_.style.display="none",_)}),n.$$.dirty&6145&&(a&&!f?(i.startRecording(),t(12,f=!0)):(i.stopRecording(),t(12,f=!1)))},[i,o,b,k,D,r,c,l,u,s,_,a,f,h,y,g,v,P,w,C,z,B,O,S]}class Wn extends Bn{constructor(e){super(),Tn(this,e,On,qn,jn,{record:0,i18n:1,recording:11,record_time:2,show_recording_waveform:3,timing:4})}get record(){return this.$$.ctx[0]}set record(e){this.$$set({record:e}),me()}get i18n(){return this.$$.ctx[1]}set i18n(e){this.$$set({i18n:e}),me()}get recording(){return this.$$.ctx[11]}set recording(e){this.$$set({recording:e}),me()}get record_time(){return this.$$.ctx[2]}set record_time(e){this.$$set({record_time:e}),me()}get show_recording_waveform(){return this.$$.ctx[3]}set show_recording_waveform(e){this.$$set({show_recording_waveform:e}),me()}get timing(){return this.$$.ctx[4]}set timing(e){this.$$set({timing:e}),me()}}const{SvelteComponent:Nn,add_flush_callback:Ue,append:W,attr:K,bind:je,binding_callbacks:re,check_outros:gt,create_component:zt,destroy_component:Tt,detach:Ce,element:Q,flush:te,group_outros:ht,init:Fn,insert:Be,mount_component:Ut,noop:Hn,safe_not_equal:Vn,set_data:jt,space:pe,text:qt,transition_in:oe,transition_out:be}=window.__gradio__svelte__internal,{onMount:Yn}=window.__gradio__svelte__internal,{createEventDispatcher:Gn}=window.__gradio__svelte__internal;function pt(n){let e,t,i,o,a,r=n[0]==="edit"&&n[17]>0&&bt(n);function c(s,_){return s[16]?Kn:Jn}let l=c(n),u=l(n);return{c(){e=Q("div"),t=Q("time"),t.textContent="0:00",i=pe(),o=Q("div"),r&&r.c(),a=pe(),u.c(),K(t,"class","time svelte-9n45fh"),K(e,"class","timestamps svelte-9n45fh")},m(s,_){Be(s,e,_),W(e,t),n[23](t),W(e,i),W(e,o),r&&r.m(o,null),W(o,a),u.m(o,null)},p(s,_){s[0]==="edit"&&s[17]>0?r?r.p(s,_):(r=bt(s),r.c(),r.m(o,a)):r&&(r.d(1),r=null),l===(l=c(s))&&u?u.p(s,_):(u.d(1),u=l(s),u&&(u.c(),u.m(o,null)))},d(s){s&&Ce(e),n[23](null),r&&r.d(),u.d()}}}function bt(n){let e,t=se(n[17])+"",i;return{c(){e=Q("time"),i=qt(t),K(e,"class","trim-duration svelte-9n45fh")},m(o,a){Be(o,e,a),W(e,i)},p(o,a){a[0]&131072&&t!==(t=se(o[17])+"")&&jt(i,t)},d(o){o&&Ce(e)}}}function Jn(n){let e;return{c(){e=Q("time"),e.textContent="0:00",K(e,"class","duration svelte-9n45fh")},m(t,i){Be(t,e,i),n[24](e)},p:Hn,d(t){t&&Ce(e),n[24](null)}}}function Kn(n){let e,t=se(n[15])+"",i;return{c(){e=Q("time"),i=qt(t),K(e,"class","duration svelte-9n45fh")},m(o,a){Be(o,e,a),W(e,i)},p(o,a){a[0]&32768&&t!==(t=se(o[15])+"")&&jt(i,t)},d(o){o&&Ce(e)}}}function vt(n){let e,t,i;function o(r){n[25](r)}let a={i18n:n[1],timing:n[16],recording:n[5],show_recording_waveform:n[2].show_recording_waveform,record_time:se(n[15])};return n[7]!==void 0&&(a.record=n[7]),e=new Wn({props:a}),re.push(()=>je(e,"record",o)),{c(){zt(e.$$.fragment)},m(r,c){Ut(e,r,c),i=!0},p(r,c){const l={};c[0]&2&&(l.i18n=r[1]),c[0]&65536&&(l.timing=r[16]),c[0]&32&&(l.recording=r[5]),c[0]&4&&(l.show_recording_waveform=r[2].show_recording_waveform),c[0]&32768&&(l.record_time=se(r[15])),!t&&c[0]&128&&(t=!0,l.record=r[7],Ue(()=>t=!1)),e.$set(l)},i(r){i||(oe(e.$$.fragment,r),i=!0)},o(r){be(e.$$.fragment,r),i=!1},d(r){Tt(e,r)}}}function wt(n){let e,t,i,o,a;function r(s){n[26](s)}function c(s){n[27](s)}function l(s){n[28](s)}let u={container:n[11],playing:n[10],audio_duration:n[14],i18n:n[1],editable:n[4],interactive:!0,handle_trim_audio:n[18],show_redo:!0,handle_reset_value:n[3],waveform_options:n[2]};return n[6]!==void 0&&(u.waveform=n[6]),n[17]!==void 0&&(u.trimDuration=n[17]),n[0]!==void 0&&(u.mode=n[0]),e=new hn({props:u}),re.push(()=>je(e,"waveform",r)),re.push(()=>je(e,"trimDuration",c)),re.push(()=>je(e,"mode",l)),{c(){zt(e.$$.fragment)},m(s,_){Ut(e,s,_),a=!0},p(s,_){const f={};_[0]&2048&&(f.container=s[11]),_[0]&1024&&(f.playing=s[10]),_[0]&16384&&(f.audio_duration=s[14]),_[0]&2&&(f.i18n=s[1]),_[0]&16&&(f.editable=s[4]),_[0]&8&&(f.handle_reset_value=s[3]),_[0]&4&&(f.waveform_options=s[2]),!t&&_[0]&64&&(t=!0,f.waveform=s[6],Ue(()=>t=!1)),!i&&_[0]&131072&&(i=!0,f.trimDuration=s[17],Ue(()=>i=!1)),!o&&_[0]&1&&(o=!0,f.mode=s[0],Ue(()=>o=!1)),e.$set(f)},i(s){a||(oe(e.$$.fragment,s),a=!0)},o(s){be(e.$$.fragment,s),a=!1},d(s){Tt(e,s)}}}function Qn(n){let e,t,i,o,a,r,c,l,u=(n[16]||n[13])&&n[2].show_recording_waveform&&pt(n),s=n[12]&&!n[13]&&vt(n),_=n[6]&&n[13]&&wt(n);return{c(){e=Q("div"),t=Q("div"),i=pe(),o=Q("div"),a=pe(),u&&u.c(),r=pe(),s&&s.c(),c=pe(),_&&_.c(),K(t,"class","microphone svelte-9n45fh"),K(t,"data-testid","microphone-waveform"),K(o,"data-testid","recording-waveform"),K(e,"class","component-wrapper svelte-9n45fh")},m(f,b){Be(f,e,b),W(e,t),n[21](t),W(e,i),W(e,o),n[22](o),W(e,a),u&&u.m(e,null),W(e,r),s&&s.m(e,null),W(e,c),_&&_.m(e,null),l=!0},p(f,b){(f[16]||f[13])&&f[2].show_recording_waveform?u?u.p(f,b):(u=pt(f),u.c(),u.m(e,r)):u&&(u.d(1),u=null),f[12]&&!f[13]?s?(s.p(f,b),b[0]&12288&&oe(s,1)):(s=vt(f),s.c(),oe(s,1),s.m(e,c)):s&&(ht(),be(s,1,1,()=>{s=null}),gt()),f[6]&&f[13]?_?(_.p(f,b),b[0]&8256&&oe(_,1)):(_=wt(f),_.c(),oe(_,1),_.m(e,null)):_&&(ht(),be(_,1,1,()=>{_=null}),gt())},i(f){l||(oe(s),oe(_),l=!0)},o(f){be(s),be(_),l=!1},d(f){f&&Ce(e),n[21](null),n[22](null),u&&u.d(),s&&s.d(),_&&_.d()}}}function Xn(n,e,t){let{mode:i}=e,{i18n:o}=e,{dispatch_blob:a}=e,{waveform_settings:r}=e,{waveform_options:c={show_recording_waveform:!0}}=e,{handle_reset_value:l}=e,{editable:u=!0}=e,{recording:s=!1}=e,_,f,b=!1,k,D,h,y=null,g,v,P,w=0,C,z=!1,B=0;const O=()=>{clearInterval(C),C=setInterval(()=>{t(15,w++,w)},1e3)},S=Gn();function p(){if(O(),t(16,z=!0),S("start_recording"),c.show_recording_waveform){let m=D;m&&(m.style.display="block")}}async function le(m){t(15,w=0),t(16,z=!1),clearInterval(C);try{const V=await m.arrayBuffer(),L=await new AudioContext({sampleRate:r.sampleRate}).decodeAudioData(V);L&&await nt(L).then(async J=>{await a([J],"change"),await a([J],"stop_recording")})}catch(V){console.error(V)}}const A=()=>{D&&t(12,D.innerHTML="",D),_!==void 0&&_.destroy(),D&&(_=Ve.create({...r,normalize:!1,container:D}),t(7,h=_.registerPlugin(Pe.create())),h?.on("record-end",le),h?.on("record-start",p),h?.on("record-pause",()=>{S("pause_recording"),clearInterval(C)}),h?.on("record-end",m=>{t(13,y=URL.createObjectURL(m));const V=D,ue=k;V&&(V.style.display="none"),ue&&y&&(ue.innerHTML="",H())}))},H=()=>{let m=k;!y||!m||t(6,f=Ve.create({container:m,url:y,...r}))},de=async(m,V)=>{t(0,i="edit");const ue=f.getDecodedData();ue&&await nt(ue,m,V).then(async L=>{await a([L],"change"),await a([L],"stop_recording"),f.destroy(),H()}),S("edit")};Yn(()=>{A(),window.addEventListener("keydown",m=>{m.key==="ArrowRight"?tt(f,.1):m.key==="ArrowLeft"&&tt(f,-.1)})});function R(m){re[m?"unshift":"push"](()=>{D=m,t(12,D)})}function E(m){re[m?"unshift":"push"](()=>{k=m,t(11,k)})}function j(m){re[m?"unshift":"push"](()=>{g=m,t(8,g),t(6,f)})}function Y(m){re[m?"unshift":"push"](()=>{v=m,t(9,v),t(6,f)})}function G(m){h=m,t(7,h)}function fe(m){f=m,t(6,f)}function Te(m){B=m,t(17,B)}function ae(m){i=m,t(0,i)}return n.$$set=m=>{"mode"in m&&t(0,i=m.mode),"i18n"in m&&t(1,o=m.i18n),"dispatch_blob"in m&&t(19,a=m.dispatch_blob),"waveform_settings"in m&&t(20,r=m.waveform_settings),"waveform_options"in m&&t(2,c=m.waveform_options),"handle_reset_value"in m&&t(3,l=m.handle_reset_value),"editable"in m&&t(4,u=m.editable),"recording"in m&&t(5,s=m.recording)},n.$$.update=()=>{n.$$.dirty[0]&128&&h?.on("record-resume",()=>{O()}),n.$$.dirty[0]&576&&f?.on("decode",m=>{t(14,P=m),v&&t(9,v.textContent=se(m),v)}),n.$$.dirty[0]&320&&f?.on("timeupdate",m=>g&&t(8,g.textContent=se(m),g)),n.$$.dirty[0]&64&&f?.on("pause",()=>{S("pause"),t(10,b=!1)}),n.$$.dirty[0]&64&&f?.on("play",()=>{S("play"),t(10,b=!0)}),n.$$.dirty[0]&64&&f?.on("finish",()=>{S("stop"),t(10,b=!1)})},[i,o,c,l,u,s,f,h,g,v,b,k,D,y,P,w,z,B,de,a,r,R,E,j,Y,G,fe,Te,ae]}class Zn extends Nn{constructor(e){super(),Fn(this,e,Xn,Qn,Vn,{mode:0,i18n:1,dispatch_blob:19,waveform_settings:20,waveform_options:2,handle_reset_value:3,editable:4,recording:5},null,[-1,-1])}get mode(){return this.$$.ctx[0]}set mode(e){this.$$set({mode:e}),te()}get i18n(){return this.$$.ctx[1]}set i18n(e){this.$$set({i18n:e}),te()}get dispatch_blob(){return this.$$.ctx[19]}set dispatch_blob(e){this.$$set({dispatch_blob:e}),te()}get waveform_settings(){return this.$$.ctx[20]}set waveform_settings(e){this.$$set({waveform_settings:e}),te()}get waveform_options(){return this.$$.ctx[2]}set waveform_options(e){this.$$set({waveform_options:e}),te()}get handle_reset_value(){return this.$$.ctx[3]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),te()}get editable(){return this.$$.ctx[4]}set editable(e){this.$$set({editable:e}),te()}get recording(){return this.$$.ctx[5]}set recording(e){this.$$set({recording:e}),te()}}const{SvelteComponent:$n,add_flush_callback:xn,append:N,attr:X,bind:ei,binding_callbacks:Ot,check_outros:ti,create_component:Wt,destroy_component:Nt,detach:Ie,element:ee,flush:ne,group_outros:ni,init:ii,insert:ze,listen:Je,mount_component:Ft,noop:We,null_to_empty:kt,safe_not_equal:oi,set_data:Ke,set_style:yt,space:Me,text:Qe,transition_in:qe,transition_out:Oe}=window.__gradio__svelte__internal,{onMount:ri}=window.__gradio__svelte__internal;function Dt(n){let e;return{c(){e=ee("div"),yt(e,"display",n[0]?"block":"none")},m(t,i){ze(t,e,i),n[11](e)},p(t,i){i&1&&yt(e,"display",t[0]?"block":"none")},d(t){t&&Ie(e),n[11](null)}}}function si(n){let e,t,i,o=n[4]("audio.record")+"",a,r,c;return{c(){e=ee("button"),t=ee("span"),t.innerHTML='<span class="dot"></span>',i=Me(),a=Qe(o),X(t,"class","record-icon"),X(e,"class","record-button svelte-1fz19cj")},m(l,u){ze(l,e,u),N(e,t),N(e,i),N(e,a),r||(c=Je(e,"click",n[14]),r=!0)},p(l,u){u&16&&o!==(o=l[4]("audio.record")+"")&&Ke(a,o)},i:We,o:We,d(l){l&&Ie(e),r=!1,c()}}}function li(n){let e,t,i,o,a=n[4]("audio.waiting")+"",r,c,l,u;return i=new fn({}),{c(){e=ee("button"),t=ee("div"),Wt(i.$$.fragment),o=Me(),r=Qe(a),X(t,"class","icon svelte-1fz19cj"),X(e,"class","spinner-button svelte-1fz19cj")},m(s,_){ze(s,e,_),N(e,t),Ft(i,t,null),N(e,o),N(e,r),c=!0,l||(u=Je(e,"click",n[13]),l=!0)},p(s,_){(!c||_&16)&&a!==(a=s[4]("audio.waiting")+"")&&Ke(r,a)},i(s){c||(qe(i.$$.fragment,s),c=!0)},o(s){Oe(i.$$.fragment,s),c=!1},d(s){s&&Ie(e),Nt(i),l=!1,u()}}}function ai(n){let e,t,i,o=(n[1]?n[4]("audio.pause"):n[4]("audio.stop"))+"",a,r,c,l;return{c(){e=ee("button"),t=ee("span"),t.innerHTML='<span class="pinger"></span> <span class="dot"></span>',i=Me(),a=Qe(o),X(t,"class","record-icon"),X(e,"class",r=kt(n[1]?"stop-button-paused":"stop-button")+" svelte-1fz19cj")},m(u,s){ze(u,e,s),N(e,t),N(e,i),N(e,a),c||(l=Je(e,"click",n[12]),c=!0)},p(u,s){s&18&&o!==(o=(u[1]?u[4]("audio.pause"):u[4]("audio.stop"))+"")&&Ke(a,o),s&2&&r!==(r=kt(u[1]?"stop-button-paused":"stop-button")+" svelte-1fz19cj")&&X(e,"class",r)},i:We,o:We,d(u){u&&Ie(e),c=!1,l()}}}function ui(n){let e,t,i,o,a,r,c,l,u,s=n[5].show_recording_waveform&&Dt(n);const _=[ai,li,si],f=[];function b(h,y){return h[0]&&!h[6]?0:h[0]&&h[6]?1:2}o=b(n),a=f[o]=_[o](n);function k(h){n[15](h)}let D={i18n:n[4]};return n[9]!==void 0&&(D.micDevices=n[9]),c=new Ct({props:D}),Ot.push(()=>ei(c,"micDevices",k)),{c(){e=ee("div"),s&&s.c(),t=Me(),i=ee("div"),a.c(),r=Me(),Wt(c.$$.fragment),X(i,"class","controls svelte-1fz19cj"),X(e,"class","mic-wrap svelte-1fz19cj")},m(h,y){ze(h,e,y),s&&s.m(e,null),N(e,t),N(e,i),f[o].m(i,null),N(i,r),Ft(c,i,null),u=!0},p(h,[y]){h[5].show_recording_waveform?s?s.p(h,y):(s=Dt(h),s.c(),s.m(e,t)):s&&(s.d(1),s=null);let g=o;o=b(h),o===g?f[o].p(h,y):(ni(),Oe(f[g],1,1,()=>{f[g]=null}),ti(),a=f[o],a?a.p(h,y):(a=f[o]=_[o](h),a.c()),qe(a,1),a.m(i,r));const v={};y&16&&(v.i18n=h[4]),!l&&y&512&&(l=!0,v.micDevices=h[9],xn(()=>l=!1)),c.$set(v)},i(h){u||(qe(a),qe(c.$$.fragment,h),u=!0)},o(h){Oe(a),Oe(c.$$.fragment,h),u=!1},d(h){h&&Ie(e),s&&s.d(),f[o].d(),Nt(c)}}}function ci(n,e,t){let{recording:i=!1}=e,{paused_recording:o=!1}=e,{stop:a}=e,{record:r}=e,{i18n:c}=e,{waveform_settings:l}=e,{waveform_options:u={show_recording_waveform:!0}}=e,{waiting:s=!1}=e,_,f,b,k=[];ri(()=>{D()});const D=()=>{_!==void 0&&_.destroy(),b&&(_=Ve.create({...l,height:100,container:b}),t(7,f=_.registerPlugin(Pe.create())))};function h(w){Ot[w?"unshift":"push"](()=>{b=w,t(8,b)})}const y=()=>{f?.stopMic(),a()},g=()=>{a()},v=()=>{f?.startMic(),r()};function P(w){k=w,t(9,k)}return n.$$set=w=>{"recording"in w&&t(0,i=w.recording),"paused_recording"in w&&t(1,o=w.paused_recording),"stop"in w&&t(2,a=w.stop),"record"in w&&t(3,r=w.record),"i18n"in w&&t(4,c=w.i18n),"waveform_settings"in w&&t(10,l=w.waveform_settings),"waveform_options"in w&&t(5,u=w.waveform_options),"waiting"in w&&t(6,s=w.waiting)},[i,o,a,r,c,u,s,f,b,k,l,h,y,g,v,P]}class _i extends $n{constructor(e){super(),ii(this,e,ci,ui,oi,{recording:0,paused_recording:1,stop:2,record:3,i18n:4,waveform_settings:10,waveform_options:5,waiting:6})}get recording(){return this.$$.ctx[0]}set recording(e){this.$$set({recording:e}),ne()}get paused_recording(){return this.$$.ctx[1]}set paused_recording(e){this.$$set({paused_recording:e}),ne()}get stop(){return this.$$.ctx[2]}set stop(e){this.$$set({stop:e}),ne()}get record(){return this.$$.ctx[3]}set record(e){this.$$set({record:e}),ne()}get i18n(){return this.$$.ctx[4]}set i18n(e){this.$$set({i18n:e}),ne()}get waveform_settings(){return this.$$.ctx[10]}set waveform_settings(e){this.$$set({waveform_settings:e}),ne()}get waveform_options(){return this.$$.ctx[5]}set waveform_options(e){this.$$set({waveform_options:e}),ne()}get waiting(){return this.$$.ctx[6]}set waiting(e){this.$$set({waiting:e}),ne()}}const{SvelteComponent:di,add_flush_callback:Se,append:Rt,attr:Et,bind:Ae,binding_callbacks:Le,bubble:_e,check_outros:Xe,create_component:Z,create_slot:fi,destroy_component:$,detach:ve,element:mi,empty:Ht,flush:M,get_all_dirty_from_scope:gi,get_slot_changes:hi,group_outros:Ze,init:pi,insert:we,mount_component:x,safe_not_equal:bi,space:Ee,transition_in:T,transition_out:U,update_slot_base:vi}=window.__gradio__svelte__internal,{onDestroy:wi,createEventDispatcher:ki,tick:Yi}=window.__gradio__svelte__internal;function yi(n){let e,t,i,o,a;e=new St({props:{i18n:n[12],download:n[9]?n[2].url:null}}),e.$on("clear",n[28]),e.$on("edit",n[47]);function r(l){n[48](l)}let c={value:n[2],label:n[5],i18n:n[12],dispatch_blob:n[26],waveform_settings:n[13],waveform_options:n[15],trim_region_settings:n[14],handle_reset_value:n[16],editable:n[17],loop:n[7],interactive:!0};return n[24]!==void 0&&(c.mode=n[24]),i=new pn({props:c}),Le.push(()=>Ae(i,"mode",r)),i.$on("stop",n[49]),i.$on("play",n[50]),i.$on("pause",n[51]),i.$on("edit",n[52]),{c(){Z(e.$$.fragment),t=Ee(),Z(i.$$.fragment)},m(l,u){x(e,l,u),we(l,t,u),x(i,l,u),a=!0},p(l,u){const s={};u[0]&4096&&(s.i18n=l[12]),u[0]&516&&(s.download=l[9]?l[2].url:null),e.$set(s);const _={};u[0]&4&&(_.value=l[2]),u[0]&32&&(_.label=l[5]),u[0]&4096&&(_.i18n=l[12]),u[0]&8192&&(_.waveform_settings=l[13]),u[0]&32768&&(_.waveform_options=l[15]),u[0]&16384&&(_.trim_region_settings=l[14]),u[0]&65536&&(_.handle_reset_value=l[16]),u[0]&131072&&(_.editable=l[17]),u[0]&128&&(_.loop=l[7]),!o&&u[0]&16777216&&(o=!0,_.mode=l[24],Se(()=>o=!1)),i.$set(_)},i(l){a||(T(e.$$.fragment,l),T(i.$$.fragment,l),a=!0)},o(l){U(e.$$.fragment,l),U(i.$$.fragment,l),a=!1},d(l){l&&ve(t),$(e,l),$(i,l)}}}function Di(n){let e,t,i,o;const a=[Ei,Ri],r=[];function c(l,u){return l[3]==="microphone"?0:l[3]==="upload"?1:-1}return~(e=c(n))&&(t=r[e]=a[e](n)),{c(){t&&t.c(),i=Ht()},m(l,u){~e&&r[e].m(l,u),we(l,i,u),o=!0},p(l,u){let s=e;e=c(l),e===s?~e&&r[e].p(l,u):(t&&(Ze(),U(r[s],1,1,()=>{r[s]=null}),Xe()),~e?(t=r[e],t?t.p(l,u):(t=r[e]=a[e](l),t.c()),T(t,1),t.m(i.parentNode,i)):t=null)},i(l){o||(T(t),o=!0)},o(l){U(t),o=!1},d(l){l&&ve(i),~e&&r[e].d(l)}}}function Ri(n){let e,t,i,o;function a(l){n[44](l)}function r(l){n[45](l)}let c={filetype:"audio/aac,audio/midi,audio/mpeg,audio/ogg,audio/wav,audio/x-wav,audio/opus,audio/webm,audio/flac,audio/vnd.rn-realaudio,audio/x-ms-wma,audio/x-aiff,audio/amr,audio/*",root:n[6],max_file_size:n[18],upload:n[19],stream_handler:n[20],aria_label:n[12]("audio.drop_to_upload"),$$slots:{default:[Mi]},$$scope:{ctx:n}};return n[0]!==void 0&&(c.dragging=n[0]),n[4]!==void 0&&(c.uploading=n[4]),e=new cn({props:c}),Le.push(()=>Ae(e,"dragging",a)),Le.push(()=>Ae(e,"uploading",r)),e.$on("load",n[29]),e.$on("error",n[46]),{c(){Z(e.$$.fragment)},m(l,u){x(e,l,u),o=!0},p(l,u){const s={};u[0]&64&&(s.root=l[6]),u[0]&262144&&(s.max_file_size=l[18]),u[0]&524288&&(s.upload=l[19]),u[0]&1048576&&(s.stream_handler=l[20]),u[0]&4096&&(s.aria_label=l[12]("audio.drop_to_upload")),u[1]&8388608&&(s.$$scope={dirty:u,ctx:l}),!t&&u[0]&1&&(t=!0,s.dragging=l[0],Se(()=>t=!1)),!i&&u[0]&16&&(i=!0,s.uploading=l[4],Se(()=>i=!1)),e.$set(s)},i(l){o||(T(e.$$.fragment,l),o=!0)},o(l){U(e.$$.fragment,l),o=!1},d(l){$(e,l)}}}function Ei(n){let e,t,i,o,a,r;e=new St({props:{i18n:n[12]}}),e.$on("clear",n[28]);const c=[Ai,Si],l=[];function u(s,_){return s[11]?0:1}return i=u(n),o=l[i]=c[i](n),{c(){Z(e.$$.fragment),t=Ee(),o.c(),a=Ht()},m(s,_){x(e,s,_),we(s,t,_),l[i].m(s,_),we(s,a,_),r=!0},p(s,_){const f={};_[0]&4096&&(f.i18n=s[12]),e.$set(f);let b=i;i=u(s),i===b?l[i].p(s,_):(Ze(),U(l[b],1,1,()=>{l[b]=null}),Xe(),o=l[i],o?o.p(s,_):(o=l[i]=c[i](s),o.c()),T(o,1),o.m(a.parentNode,a))},i(s){r||(T(e.$$.fragment,s),T(o),r=!0)},o(s){U(e.$$.fragment,s),U(o),r=!1},d(s){s&&(ve(t),ve(a)),$(e,s),l[i].d(s)}}}function Mi(n){let e;const t=n[39].default,i=fi(t,n,n[54],null);return{c(){i&&i.c()},m(o,a){i&&i.m(o,a),e=!0},p(o,a){i&&i.p&&(!e||a[1]&8388608)&&vi(i,t,o,o[54],e?hi(t,o[54],a,null):gi(o[54]),null)},i(o){e||(T(i,o),e=!0)},o(o){U(i,o),e=!1},d(o){i&&i.d(o)}}}function Si(n){let e,t,i;function o(r){n[40](r)}let a={i18n:n[12],editable:n[17],recording:n[1],dispatch_blob:n[26],waveform_settings:n[13],waveform_options:n[15],handle_reset_value:n[16]};return n[24]!==void 0&&(a.mode=n[24]),e=new Zn({props:a}),Le.push(()=>Ae(e,"mode",o)),e.$on("start_recording",n[41]),e.$on("pause_recording",n[42]),e.$on("stop_recording",n[43]),{c(){Z(e.$$.fragment)},m(r,c){x(e,r,c),i=!0},p(r,c){const l={};c[0]&4096&&(l.i18n=r[12]),c[0]&131072&&(l.editable=r[17]),c[0]&2&&(l.recording=r[1]),c[0]&8192&&(l.waveform_settings=r[13]),c[0]&32768&&(l.waveform_options=r[15]),c[0]&65536&&(l.handle_reset_value=r[16]),!t&&c[0]&16777216&&(t=!0,l.mode=r[24],Se(()=>t=!1)),e.$set(l)},i(r){i||(T(e.$$.fragment,r),i=!0)},o(r){U(e.$$.fragment,r),i=!1},d(r){$(e,r)}}}function Ai(n){let e,t;return e=new _i({props:{record:n[27],recording:n[1],stop:n[30],i18n:n[12],waveform_settings:n[13],waveform_options:n[15],waiting:n[23]==="waiting"}}),{c(){Z(e.$$.fragment)},m(i,o){x(e,i,o),t=!0},p(i,o){const a={};o[0]&2&&(a.recording=i[1]),o[0]&4096&&(a.i18n=i[12]),o[0]&8192&&(a.waveform_settings=i[13]),o[0]&32768&&(a.waveform_options=i[15]),o[0]&8388608&&(a.waiting=i[23]==="waiting"),e.$set(a)},i(i){t||(T(e.$$.fragment,i),t=!0)},o(i){U(e.$$.fragment,i),t=!1},d(i){$(e,i)}}}function Li(n){let e,t,i,o,a,r,c,l,u,s,_,f;e=new _n({props:{show_label:n[8],Icon:dn,float:n[3]==="upload"&&n[2]===null,label:n[5]||n[12]("audio.audio")}}),o=new gn({props:{time_limit:n[22]}});const b=[Di,yi],k=[];function D(g,v){return g[2]===null||g[11]?0:1}r=D(n),c=k[r]=b[r](n);function h(g){n[53](g)}let y={sources:n[10],handle_clear:n[28]};return n[3]!==void 0&&(y.active_source=n[3]),u=new mn({props:y}),Le.push(()=>Ae(u,"active_source",h)),{c(){Z(e.$$.fragment),t=Ee(),i=mi("div"),Z(o.$$.fragment),a=Ee(),c.c(),l=Ee(),Z(u.$$.fragment),Et(i,"class",_="audio-container "+n[21]+" svelte-1ud6e7m")},m(g,v){x(e,g,v),we(g,t,v),we(g,i,v),x(o,i,null),Rt(i,a),k[r].m(i,null),Rt(i,l),x(u,i,null),f=!0},p(g,v){const P={};v[0]&256&&(P.show_label=g[8]),v[0]&12&&(P.float=g[3]==="upload"&&g[2]===null),v[0]&4128&&(P.label=g[5]||g[12]("audio.audio")),e.$set(P);const w={};v[0]&4194304&&(w.time_limit=g[22]),o.$set(w);let C=r;r=D(g),r===C?k[r].p(g,v):(Ze(),U(k[C],1,1,()=>{k[C]=null}),Xe(),c=k[r],c?c.p(g,v):(c=k[r]=b[r](g),c.c()),T(c,1),c.m(i,l));const z={};v[0]&1024&&(z.sources=g[10]),!s&&v[0]&8&&(s=!0,z.active_source=g[3],Se(()=>s=!1)),u.$set(z),(!f||v[0]&2097152&&_!==(_="audio-container "+g[21]+" svelte-1ud6e7m"))&&Et(i,"class",_)},i(g){f||(T(e.$$.fragment,g),T(o.$$.fragment,g),T(c),T(u.$$.fragment,g),f=!0)},o(g){U(e.$$.fragment,g),U(o.$$.fragment,g),U(c),U(u.$$.fragment,g),f=!1},d(g){g&&(ve(t),ve(i)),$(e,g),$(o),k[r].d(),$(u)}}}const Mt=44;function Pi(n,e,t){let{$$slots:i={},$$scope:o}=e,{value:a=null}=e,{label:r}=e,{root:c}=e,{loop:l}=e,{show_label:u=!0}=e,{show_download_button:s=!1}=e,{sources:_=["microphone","upload"]}=e,{pending:f=!1}=e,{streaming:b=!1}=e,{i18n:k}=e,{waveform_settings:D}=e,{trim_region_settings:h={}}=e,{waveform_options:y={}}=e,{dragging:g}=e,{active_source:v}=e,{handle_reset_value:P=()=>{}}=e,{editable:w=!0}=e,{max_file_size:C=null}=e,{upload:z}=e,{stream_handler:B}=e,{stream_every:O}=e,{uploading:S=!1}=e,{recording:p=!1}=e,{class_name:le=""}=e,A=null,H="closed";const de=d=>{d==="closed"?(t(22,A=null),t(23,H="closed")):d==="waiting"?t(23,H="waiting"):t(23,H="open")},R=d=>{p&&t(22,A=d)};let E,j="",Y,G=[],fe=!1,Te=!1,ae=[],m;function V(){m=[et(()=>import("./module-BdtwaIMt.js"),__vite__mapDeps([0,1,2,3]),import.meta.url),et(()=>import("./module-BA06XY8_.js"),__vite__mapDeps([4,1]),import.meta.url)]}typeof window<"u"&&b&&V();const L=ki(),J=async(d,q)=>{let ce=new File(d,"audio.wav");const ke=await un([ce],q==="stream");t(2,a=(await z(ke,c,void 0,C||void 0))?.filter(Boolean)[0]),L(q,a)};wi(()=>{b&&E&&E.state!=="inactive"&&E.stop()});async function Vt(){let d;try{d=await navigator.mediaDevices.getUserMedia({audio:!0})}catch(q){if(!navigator.mediaDevices){L("error",k("audio.no_device_support"));return}if(q instanceof DOMException&&q.name=="NotAllowedError"){L("error",k("audio.allow_recording_access"));return}throw q}if(d!=null){if(b){const[{MediaRecorder:q,register:ce},{connect:ke}]=await Promise.all(m);await ce(await ke()),t(35,E=new q(d,{mimeType:"audio/wav"})),E.addEventListener("dataavailable",Yt)}else t(35,E=new MediaRecorder(d)),E.addEventListener("dataavailable",q=>{ae.push(q.data)});E.addEventListener("stop",async()=>{t(1,p=!1),await J(ae,"change"),await J(ae,"stop_recording"),ae=[]}),Te=!0}}async function Yt(d){let q=await d.data.arrayBuffer(),ce=new Uint8Array(q);if(Y||(t(36,Y=new Uint8Array(q.slice(0,Mt))),ce=new Uint8Array(q.slice(Mt))),f)G.push(ce);else{let ke=[Y].concat(G,[ce]);if(!p||H==="waiting")return;J(ke,"stream"),t(37,G=[])}}async function $e(){t(1,p=!0),L("start_recording"),Te||await Vt(),t(36,Y=void 0),b&&E.state!="recording"&&E.start(O*1e3)}function Gt(){L("change",null),L("clear"),t(24,j=""),t(2,a=null)}function Jt({detail:d}){t(2,a=d),L("change",d),L("upload",d)}async function xe(){t(1,p=!1),b&&(L("close_stream"),L("stop_recording"),E.stop(),f&&t(38,fe=!0),J(ae,"stop_recording"),L("clear"),t(24,j=""))}function Kt(d){j=d,t(24,j)}function Qt(d){_e.call(this,n,d)}function Xt(d){_e.call(this,n,d)}function Zt(d){_e.call(this,n,d)}function $t(d){g=d,t(0,g)}function xt(d){S=d,t(4,S)}const en=({detail:d})=>L("error",d),tn=()=>t(24,j="edit");function nn(d){j=d,t(24,j)}function on(d){_e.call(this,n,d)}function rn(d){_e.call(this,n,d)}function sn(d){_e.call(this,n,d)}function ln(d){_e.call(this,n,d)}function an(d){v=d,t(3,v)}return n.$$set=d=>{"value"in d&&t(2,a=d.value),"label"in d&&t(5,r=d.label),"root"in d&&t(6,c=d.root),"loop"in d&&t(7,l=d.loop),"show_label"in d&&t(8,u=d.show_label),"show_download_button"in d&&t(9,s=d.show_download_button),"sources"in d&&t(10,_=d.sources),"pending"in d&&t(31,f=d.pending),"streaming"in d&&t(11,b=d.streaming),"i18n"in d&&t(12,k=d.i18n),"waveform_settings"in d&&t(13,D=d.waveform_settings),"trim_region_settings"in d&&t(14,h=d.trim_region_settings),"waveform_options"in d&&t(15,y=d.waveform_options),"dragging"in d&&t(0,g=d.dragging),"active_source"in d&&t(3,v=d.active_source),"handle_reset_value"in d&&t(16,P=d.handle_reset_value),"editable"in d&&t(17,w=d.editable),"max_file_size"in d&&t(18,C=d.max_file_size),"upload"in d&&t(19,z=d.upload),"stream_handler"in d&&t(20,B=d.stream_handler),"stream_every"in d&&t(32,O=d.stream_every),"uploading"in d&&t(4,S=d.uploading),"recording"in d&&t(1,p=d.recording),"class_name"in d&&t(21,le=d.class_name),"$$scope"in d&&t(54,o=d.$$scope)},n.$$.update=()=>{if(n.$$.dirty[0]&1&&L("drag",g),n.$$.dirty[1]&225&&fe&&f===!1&&(t(38,fe=!1),Y&&G)){let d=[Y].concat(G);t(37,G=[]),J(d,"stream")}n.$$.dirty[0]&2|n.$$.dirty[1]&16&&!p&&E&&xe(),n.$$.dirty[0]&2|n.$$.dirty[1]&16&&p&&E&&$e()},[g,p,a,v,S,r,c,l,u,s,_,b,k,D,h,y,P,w,C,z,B,le,A,H,j,L,J,$e,Gt,Jt,xe,f,O,de,R,E,Y,G,fe,i,Kt,Qt,Xt,Zt,$t,xt,en,tn,nn,on,rn,sn,ln,an,o]}class Ci extends di{constructor(e){super(),pi(this,e,Pi,Li,bi,{value:2,label:5,root:6,loop:7,show_label:8,show_download_button:9,sources:10,pending:31,streaming:11,i18n:12,waveform_settings:13,trim_region_settings:14,waveform_options:15,dragging:0,active_source:3,handle_reset_value:16,editable:17,max_file_size:18,upload:19,stream_handler:20,stream_every:32,uploading:4,recording:1,class_name:21,modify_stream:33,set_time_limit:34},null,[-1,-1])}get value(){return this.$$.ctx[2]}set value(e){this.$$set({value:e}),M()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),M()}get root(){return this.$$.ctx[6]}set root(e){this.$$set({root:e}),M()}get loop(){return this.$$.ctx[7]}set loop(e){this.$$set({loop:e}),M()}get show_label(){return this.$$.ctx[8]}set show_label(e){this.$$set({show_label:e}),M()}get show_download_button(){return this.$$.ctx[9]}set show_download_button(e){this.$$set({show_download_button:e}),M()}get sources(){return this.$$.ctx[10]}set sources(e){this.$$set({sources:e}),M()}get pending(){return this.$$.ctx[31]}set pending(e){this.$$set({pending:e}),M()}get streaming(){return this.$$.ctx[11]}set streaming(e){this.$$set({streaming:e}),M()}get i18n(){return this.$$.ctx[12]}set i18n(e){this.$$set({i18n:e}),M()}get waveform_settings(){return this.$$.ctx[13]}set waveform_settings(e){this.$$set({waveform_settings:e}),M()}get trim_region_settings(){return this.$$.ctx[14]}set trim_region_settings(e){this.$$set({trim_region_settings:e}),M()}get waveform_options(){return this.$$.ctx[15]}set waveform_options(e){this.$$set({waveform_options:e}),M()}get dragging(){return this.$$.ctx[0]}set dragging(e){this.$$set({dragging:e}),M()}get active_source(){return this.$$.ctx[3]}set active_source(e){this.$$set({active_source:e}),M()}get handle_reset_value(){return this.$$.ctx[16]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),M()}get editable(){return this.$$.ctx[17]}set editable(e){this.$$set({editable:e}),M()}get max_file_size(){return this.$$.ctx[18]}set max_file_size(e){this.$$set({max_file_size:e}),M()}get upload(){return this.$$.ctx[19]}set upload(e){this.$$set({upload:e}),M()}get stream_handler(){return this.$$.ctx[20]}set stream_handler(e){this.$$set({stream_handler:e}),M()}get stream_every(){return this.$$.ctx[32]}set stream_every(e){this.$$set({stream_every:e}),M()}get uploading(){return this.$$.ctx[4]}set uploading(e){this.$$set({uploading:e}),M()}get recording(){return this.$$.ctx[1]}set recording(e){this.$$set({recording:e}),M()}get class_name(){return this.$$.ctx[21]}set class_name(e){this.$$set({class_name:e}),M()}get modify_stream(){return this.$$.ctx[33]}get set_time_limit(){return this.$$.ctx[34]}}const Gi=Ci;export{Gi as I};
//# sourceMappingURL=InteractiveAudio-C1oZjHXI.js.map
