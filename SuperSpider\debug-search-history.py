#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
搜索历史记录调试脚本
用于测试搜索历史记录功能
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from backend.main import create_app
from backend.models.search_record import SearchRecord
from backend.models.user import User
from backend.main import db
import json

def test_search_history_api():
    """测试搜索历史记录API"""
    app = create_app()
    
    with app.app_context():
        # 检查数据库连接
        try:
            db.create_all()
            print("✓ 数据库连接成功")
        except Exception as e:
            print(f"✗ 数据库连接失败: {e}")
            return
        
        # 检查用户表
        try:
            user_count = User.query.count()
            print(f"✓ 用户表中有 {user_count} 个用户")
        except Exception as e:
            print(f"✗ 查询用户表失败: {e}")
            return
        
        # 检查搜索记录表
        try:
            record_count = SearchRecord.query.count()
            print(f"✓ 搜索记录表中有 {record_count} 条记录")
        except Exception as e:
            print(f"✗ 查询搜索记录表失败: {e}")
            return
        
        # 如果没有搜索记录，创建一些测试数据
        if record_count == 0:
            print("创建测试搜索记录...")
            try:
                # 获取第一个用户，如果没有用户则创建一个
                user = User.query.first()
                if not user:
                    user = User(
                        username='testuser',
                        phone='13800138000'
                    )
                    user.set_password('123456')
                    db.session.add(user)
                    db.session.commit()
                    print("✓ 创建测试用户成功")
                
                # 创建测试搜索记录
                test_records = [
                    {
                        'user_id': user.id,
                        'platform': 'douyin',
                        'content_type': 'video',
                        'content_url': 'https://www.douyin.com/video/test1',
                        'title': '测试抖音视频1',
                        'author': '测试作者1',
                        'status': 'success'
                    },
                    {
                        'user_id': user.id,
                        'platform': 'kuaishou',
                        'content_type': 'video',
                        'content_url': 'https://www.kuaishou.com/video/test2',
                        'title': '测试快手视频2',
                        'author': '测试作者2',
                        'status': 'success'
                    },
                    {
                        'user_id': user.id,
                        'platform': 'bilibili',
                        'content_type': 'video',
                        'content_url': 'https://www.bilibili.com/video/test3',
                        'title': '测试B站视频3',
                        'author': '测试作者3',
                        'status': 'failed'
                    }
                ]
                
                for record_data in test_records:
                    record = SearchRecord(**record_data)
                    db.session.add(record)
                
                db.session.commit()
                print("✓ 创建测试搜索记录成功")
                
            except Exception as e:
                print(f"✗ 创建测试数据失败: {e}")
                db.session.rollback()
                return
        
        # 测试API端点
        with app.test_client() as client:
            print("\n测试API端点...")
            
            # 测试未登录状态
            response = client.get('/api/search/history')
            print(f"未登录状态 - 状态码: {response.status_code}")
            if response.status_code == 401:
                print("✓ 未登录正确返回401")
            else:
                print(f"✗ 未登录应该返回401，实际返回: {response.status_code}")
            
            # 模拟登录状态（这里需要实际的登录逻辑）
            print("\n需要实际登录才能测试完整功能")
            print("请启动应用并在浏览器中登录后测试")

def check_file_structure():
    """检查文件结构"""
    print("检查文件结构...")
    
    files_to_check = [
        'frontend/static/js/search-history.js',
        'frontend/static/css/downloads.css',
        'frontend/templates/index.html',
        'backend/api/search_api.py',
        'backend/models/search_record.py'
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"✓ {file_path} 存在")
        else:
            print(f"✗ {file_path} 不存在")

def main():
    print("=== 搜索历史记录功能调试 ===\n")
    
    # 检查文件结构
    check_file_structure()
    print()
    
    # 测试API
    test_search_history_api()
    
    print("\n=== 调试完成 ===")
    print("建议:")
    print("1. 启动应用: python run.py")
    print("2. 访问: http://localhost:5000")
    print("3. 登录后点击'搜索记录'链接")
    print("4. 或访问测试页面: http://localhost:5000/test-search-history")

if __name__ == '__main__':
    main()
