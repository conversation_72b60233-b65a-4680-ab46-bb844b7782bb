{"version": 3, "file": "KHR_draco_mesh_compression-BTNrRjd9.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Meshes/Compression/dracoCompressionWorker.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Meshes/Compression/dracoCodec.js", "../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Meshes/Compression/dracoDecoder.js", "../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/glTF/2.0/Extensions/KHR_draco_mesh_compression.js"], "sourcesContent": ["/**\n * @internal\n */\nexport function EncodeMesh(module /** EncoderModule */, attributes, indices, options) {\n    const encoderModule = module;\n    let encoder = null;\n    let meshBuilder = null;\n    let mesh = null;\n    let encodedNativeBuffer = null;\n    const attributeIDs = {}; // Babylon kind -> Draco unique id\n    // Double-check that at least a position attribute is provided\n    const positionAttribute = attributes.find((a) => a.dracoName === \"POSITION\");\n    if (!positionAttribute) {\n        throw new Error(\"Position attribute is required for Draco encoding\");\n    }\n    // If no indices are provided, assume mesh is unindexed. Let's generate them, since Draco meshes require them.\n    // TODO: This may be the POINT_CLOUD case, but need to investigate. Should work for now-- just less efficient.\n    if (!indices) {\n        // Assume position attribute is the largest attribute.\n        const positionVerticesCount = positionAttribute.data.length / positionAttribute.size;\n        indices = new (positionVerticesCount > 65535 ? Uint32Array : Uint16Array)(positionVerticesCount);\n        for (let i = 0; i < positionVerticesCount; i++) {\n            indices[i] = i;\n        }\n    }\n    try {\n        encoder = new encoderModule.Encoder();\n        meshBuilder = new encoderModule.MeshBuilder();\n        mesh = new encoderModule.Mesh();\n        // Add the faces\n        meshBuilder.AddFacesToMesh(mesh, indices.length / 3, indices);\n        const addAttributeMap = new Map([\n            [Float32Array, (mb, m, a, c, s, d) => mb.AddFloatAttribute(m, a, c, s, d)],\n            [Uint32Array, (mb, m, a, c, s, d) => mb.AddUInt32Attribute(m, a, c, s, d)],\n            [Uint16Array, (mb, m, a, c, s, d) => mb.AddUInt16Attribute(m, a, c, s, d)],\n            [Uint8Array, (mb, m, a, c, s, d) => mb.AddUInt8Attribute(m, a, c, s, d)],\n            [Int32Array, (mb, m, a, c, s, d) => mb.AddInt32Attribute(m, a, c, s, d)],\n            [Int16Array, (mb, m, a, c, s, d) => mb.AddInt16Attribute(m, a, c, s, d)],\n            [Int8Array, (mb, m, a, c, s, d) => mb.AddInt8Attribute(m, a, c, s, d)],\n        ]);\n        // Add the attributes\n        for (const attribute of attributes) {\n            if (attribute.data instanceof Uint8ClampedArray) {\n                attribute.data = new Uint8Array(attribute.data); // Draco does not support Uint8ClampedArray\n            }\n            const addAttribute = addAttributeMap.get(attribute.data.constructor);\n            const verticesCount = attribute.data.length / attribute.size;\n            attributeIDs[attribute.kind] = addAttribute(meshBuilder, mesh, encoderModule[attribute.dracoName], verticesCount, attribute.size, attribute.data);\n            if (options.quantizationBits && options.quantizationBits[attribute.dracoName]) {\n                encoder.SetAttributeQuantization(encoderModule[attribute.dracoName], options.quantizationBits[attribute.dracoName]);\n            }\n        }\n        // Set the options\n        if (options.method) {\n            encoder.SetEncodingMethod(encoderModule[options.method]);\n        }\n        if (options.encodeSpeed !== undefined && options.decodeSpeed !== undefined) {\n            encoder.SetSpeedOptions(options.encodeSpeed, options.decodeSpeed);\n        }\n        // Encode to native buffer\n        encodedNativeBuffer = new encoderModule.DracoInt8Array();\n        const encodedLength = encoder.EncodeMeshToDracoBuffer(mesh, encodedNativeBuffer);\n        if (encodedLength <= 0) {\n            throw new Error(\"Draco encoding failed.\");\n        }\n        // Copy the native buffer data to worker heap\n        const encodedData = new Int8Array(encodedLength);\n        for (let i = 0; i < encodedLength; i++) {\n            encodedData[i] = encodedNativeBuffer.GetValue(i);\n        }\n        return { data: encodedData, attributeIDs: attributeIDs };\n    }\n    finally {\n        if (mesh) {\n            encoderModule.destroy(mesh);\n        }\n        if (meshBuilder) {\n            encoderModule.destroy(meshBuilder);\n        }\n        if (encoder) {\n            encoderModule.destroy(encoder);\n        }\n        if (encodedNativeBuffer) {\n            encoderModule.destroy(encodedNativeBuffer);\n        }\n    }\n}\n/**\n * The worker function that gets converted to a blob url to pass into a worker.\n * To be used if a developer wants to create their own worker instance and inject it instead of using the default worker.\n */\nexport function EncoderWorkerFunction() {\n    let encoderPromise;\n    onmessage = (event) => {\n        const message = event.data;\n        switch (message.id) {\n            case \"init\": {\n                // if URL is provided then load the script. Otherwise expect the script to be loaded already\n                if (message.url) {\n                    importScripts(message.url);\n                }\n                const initEncoderObject = message.wasmBinary ? { wasmBinary: message.wasmBinary } : {};\n                encoderPromise = DracoEncoderModule(initEncoderObject);\n                postMessage({ id: \"initDone\" });\n                break;\n            }\n            case \"encodeMesh\": {\n                if (!encoderPromise) {\n                    throw new Error(\"Draco encoder module is not available\");\n                }\n                encoderPromise.then((encoder) => {\n                    const result = EncodeMesh(encoder, message.attributes, message.indices, message.options);\n                    postMessage({ id: \"encodeMeshDone\", encodedMeshData: result }, result ? [result.data.buffer] : undefined);\n                });\n                break;\n            }\n        }\n    };\n}\n/**\n * @internal\n */\nexport function DecodeMesh(module /** DecoderModule */, data, attributeIDs, onIndicesData, onAttributeData) {\n    const decoderModule = module;\n    let decoder = null;\n    let buffer = null;\n    let geometry = null;\n    try {\n        decoder = new decoderModule.Decoder();\n        buffer = new decoderModule.DecoderBuffer();\n        buffer.Init(data, data.byteLength);\n        let status;\n        const type = decoder.GetEncodedGeometryType(buffer);\n        switch (type) {\n            case decoderModule.TRIANGULAR_MESH: {\n                const mesh = new decoderModule.Mesh();\n                status = decoder.DecodeBufferToMesh(buffer, mesh);\n                if (!status.ok() || mesh.ptr === 0) {\n                    throw new Error(status.error_msg());\n                }\n                const numFaces = mesh.num_faces();\n                const numIndices = numFaces * 3;\n                const byteLength = numIndices * 4;\n                const ptr = decoderModule._malloc(byteLength);\n                try {\n                    decoder.GetTrianglesUInt32Array(mesh, byteLength, ptr);\n                    const indices = new Uint32Array(numIndices);\n                    indices.set(new Uint32Array(decoderModule.HEAPF32.buffer, ptr, numIndices));\n                    onIndicesData(indices);\n                }\n                finally {\n                    decoderModule._free(ptr);\n                }\n                geometry = mesh;\n                break;\n            }\n            case decoderModule.POINT_CLOUD: {\n                const pointCloud = new decoderModule.PointCloud();\n                status = decoder.DecodeBufferToPointCloud(buffer, pointCloud);\n                if (!status.ok() || !pointCloud.ptr) {\n                    throw new Error(status.error_msg());\n                }\n                geometry = pointCloud;\n                break;\n            }\n            default: {\n                throw new Error(`Invalid geometry type ${type}`);\n            }\n        }\n        const numPoints = geometry.num_points();\n        const processAttribute = (decoder, geometry, kind, attribute /** Attribute */) => {\n            const dataType = attribute.data_type();\n            const numComponents = attribute.num_components();\n            const normalized = attribute.normalized();\n            const byteStride = attribute.byte_stride();\n            const byteOffset = attribute.byte_offset();\n            const dataTypeInfo = {\n                [decoderModule.DT_FLOAT32]: { typedArrayConstructor: Float32Array, heap: decoderModule.HEAPF32 },\n                [decoderModule.DT_INT8]: { typedArrayConstructor: Int8Array, heap: decoderModule.HEAP8 },\n                [decoderModule.DT_INT16]: { typedArrayConstructor: Int16Array, heap: decoderModule.HEAP16 },\n                [decoderModule.DT_INT32]: { typedArrayConstructor: Int32Array, heap: decoderModule.HEAP32 },\n                [decoderModule.DT_UINT8]: { typedArrayConstructor: Uint8Array, heap: decoderModule.HEAPU8 },\n                [decoderModule.DT_UINT16]: { typedArrayConstructor: Uint16Array, heap: decoderModule.HEAPU16 },\n                [decoderModule.DT_UINT32]: { typedArrayConstructor: Uint32Array, heap: decoderModule.HEAPU32 },\n            };\n            const info = dataTypeInfo[dataType];\n            if (!info) {\n                throw new Error(`Invalid data type ${dataType}`);\n            }\n            const numValues = numPoints * numComponents;\n            const byteLength = numValues * info.typedArrayConstructor.BYTES_PER_ELEMENT;\n            const ptr = decoderModule._malloc(byteLength);\n            try {\n                decoder.GetAttributeDataArrayForAllPoints(geometry, attribute, dataType, byteLength, ptr);\n                const data = new info.typedArrayConstructor(info.heap.buffer, ptr, numValues);\n                onAttributeData(kind, data.slice(), numComponents, byteOffset, byteStride, normalized);\n            }\n            finally {\n                decoderModule._free(ptr);\n            }\n        };\n        if (attributeIDs) {\n            for (const kind in attributeIDs) {\n                const id = attributeIDs[kind];\n                const attribute = decoder.GetAttributeByUniqueId(geometry, id);\n                processAttribute(decoder, geometry, kind, attribute);\n            }\n        }\n        else {\n            const dracoAttributeTypes = {\n                position: decoderModule.POSITION,\n                normal: decoderModule.NORMAL,\n                color: decoderModule.COLOR,\n                uv: decoderModule.TEX_COORD,\n            };\n            for (const kind in dracoAttributeTypes) {\n                const id = decoder.GetAttributeId(geometry, dracoAttributeTypes[kind]);\n                if (id !== -1) {\n                    const attribute = decoder.GetAttribute(geometry, id);\n                    processAttribute(decoder, geometry, kind, attribute);\n                }\n            }\n        }\n        return numPoints;\n    }\n    finally {\n        if (geometry) {\n            decoderModule.destroy(geometry);\n        }\n        if (buffer) {\n            decoderModule.destroy(buffer);\n        }\n        if (decoder) {\n            decoderModule.destroy(decoder);\n        }\n    }\n}\n/**\n * The worker function that gets converted to a blob url to pass into a worker.\n * To be used if a developer wants to create their own worker instance and inject it instead of using the default worker.\n */\nexport function DecoderWorkerFunction() {\n    let decoderPromise;\n    onmessage = (event) => {\n        const message = event.data;\n        switch (message.id) {\n            case \"init\": {\n                // if URL is provided then load the script. Otherwise expect the script to be loaded already\n                if (message.url) {\n                    importScripts(message.url);\n                }\n                const initDecoderObject = message.wasmBinary ? { wasmBinary: message.wasmBinary } : {};\n                decoderPromise = DracoDecoderModule(initDecoderObject);\n                postMessage({ id: \"initDone\" });\n                break;\n            }\n            case \"decodeMesh\": {\n                if (!decoderPromise) {\n                    throw new Error(\"Draco decoder module is not available\");\n                }\n                decoderPromise.then((decoder) => {\n                    const numPoints = DecodeMesh(decoder, message.dataView, message.attributes, (indices) => {\n                        postMessage({ id: \"indices\", data: indices }, [indices.buffer]);\n                    }, (kind, data, size, offset, stride, normalized) => {\n                        postMessage({ id: \"attribute\", kind, data, size, byteOffset: offset, byteStride: stride, normalized }, [data.buffer]);\n                    });\n                    postMessage({ id: \"decodeMeshDone\", totalVertices: numPoints });\n                });\n                break;\n            }\n        }\n    };\n}\n// For backwards compatibility\nexport { DecoderWorkerFunction as workerFunction };\n/**\n * Initializes a worker that was created for the draco agent pool\n * @param worker  The worker to initialize\n * @param wasmBinary The wasm binary to load into the worker\n * @param moduleUrl The url to the draco decoder module (optional)\n * @returns A promise that resolves when the worker is initialized\n */\nexport function initializeWebWorker(worker, wasmBinary, moduleUrl) {\n    return new Promise((resolve, reject) => {\n        const onError = (error) => {\n            worker.removeEventListener(\"error\", onError);\n            worker.removeEventListener(\"message\", onMessage);\n            reject(error);\n        };\n        const onMessage = (event) => {\n            if (event.data.id === \"initDone\") {\n                worker.removeEventListener(\"error\", onError);\n                worker.removeEventListener(\"message\", onMessage);\n                resolve(worker);\n            }\n        };\n        worker.addEventListener(\"error\", onError);\n        worker.addEventListener(\"message\", onMessage);\n        // Load with either JS-only or WASM version\n        if (!wasmBinary) {\n            worker.postMessage({\n                id: \"init\",\n                url: moduleUrl,\n            });\n        }\n        else {\n            // clone the array buffer to make it transferable\n            const clone = wasmBinary.slice(0);\n            worker.postMessage({\n                id: \"init\",\n                url: moduleUrl,\n                wasmBinary: clone,\n            }, [clone]);\n        }\n        // note: no transfer list as the ArrayBuffer is shared across main thread and pool workers\n    });\n}\n//# sourceMappingURL=dracoCompressionWorker.js.map", "import { Tools } from \"../../Misc/tools.js\";\nimport { AutoReleaseWorkerPool } from \"../../Misc/workerPool.js\";\nimport { initializeWebWorker } from \"./dracoCompressionWorker.js\";\n/**\n * @internal\n */\nexport function _GetDefaultNumWorkers() {\n    if (typeof navigator !== \"object\" || !navigator.hardwareConcurrency) {\n        return 1;\n    }\n    // Use 50% of the available logical processors but capped at 4.\n    return Math.min(Math.floor(navigator.hardwareConcurrency * 0.5), 4);\n}\n/**\n * @internal\n */\nexport function _IsConfigurationAvailable(config) {\n    return !!((config.wasmUrl && (config.wasmBinary || config.wasmBinaryUrl) && typeof WebAssembly === \"object\") || config.fallbackUrl);\n    // TODO: Account for jsModule\n}\n/**\n * Base class for a Draco codec.\n * @internal\n */\nexport class DracoCodec {\n    /**\n     * Constructor\n     * @param configuration The configuration for the DracoCodec instance.\n     */\n    constructor(configuration) {\n        // check if the codec binary and worker pool was injected\n        // Note - it is expected that the developer checked if WebWorker, WebAssembly and the URL object are available\n        if (configuration.workerPool) {\n            // Set the promise accordingly\n            this._workerPoolPromise = Promise.resolve(configuration.workerPool);\n            return;\n        }\n        // to avoid making big changes to the code here, if wasmBinary is provided use it in the wasmBinaryPromise\n        const wasmBinaryProvided = configuration.wasmBinary;\n        const numberOfWorkers = configuration.numWorkers ?? _GetDefaultNumWorkers();\n        const useWorkers = numberOfWorkers && typeof Worker === \"function\" && typeof URL === \"function\";\n        const urlNeeded = useWorkers || !configuration.jsModule;\n        // code maintained here for back-compat with no changes\n        const codecInfo = configuration.wasmUrl && configuration.wasmBinaryUrl && typeof WebAssembly === \"object\"\n            ? {\n                url: urlNeeded ? Tools.GetBabylonScriptURL(configuration.wasmUrl, true) : \"\",\n                wasmBinaryPromise: wasmBinaryProvided\n                    ? Promise.resolve(wasmBinaryProvided)\n                    : Tools.LoadFileAsync(Tools.GetBabylonScriptURL(configuration.wasmBinaryUrl, true)),\n            }\n            : {\n                url: urlNeeded ? Tools.GetBabylonScriptURL(configuration.fallbackUrl) : \"\",\n                wasmBinaryPromise: Promise.resolve(undefined),\n            };\n        // If using workers, initialize a worker pool with either the wasm or url?\n        if (useWorkers) {\n            this._workerPoolPromise = codecInfo.wasmBinaryPromise.then((wasmBinary) => {\n                const workerContent = this._getWorkerContent();\n                const workerBlobUrl = URL.createObjectURL(new Blob([workerContent], { type: \"application/javascript\" }));\n                return new AutoReleaseWorkerPool(numberOfWorkers, () => {\n                    const worker = new Worker(workerBlobUrl);\n                    return initializeWebWorker(worker, wasmBinary, codecInfo.url);\n                });\n            });\n        }\n        else {\n            this._modulePromise = codecInfo.wasmBinaryPromise.then(async (wasmBinary) => {\n                if (!this._isModuleAvailable()) {\n                    if (!configuration.jsModule) {\n                        if (!codecInfo.url) {\n                            throw new Error(\"Draco codec module is not available\");\n                        }\n                        await Tools.LoadBabylonScriptAsync(codecInfo.url);\n                    }\n                }\n                return this._createModuleAsync(wasmBinary, configuration.jsModule);\n            });\n        }\n    }\n    /**\n     * Returns a promise that resolves when ready. Call this manually to ensure the draco codec is ready before use.\n     * @returns a promise that resolves when ready\n     */\n    async whenReadyAsync() {\n        if (this._workerPoolPromise) {\n            await this._workerPoolPromise;\n            return;\n        }\n        if (this._modulePromise) {\n            await this._modulePromise;\n            return;\n        }\n    }\n    /**\n     * Stop all async operations and release resources.\n     */\n    dispose() {\n        if (this._workerPoolPromise) {\n            this._workerPoolPromise.then((workerPool) => {\n                workerPool.dispose();\n            });\n        }\n        delete this._workerPoolPromise;\n        delete this._modulePromise;\n    }\n}\n//# sourceMappingURL=dracoCodec.js.map", "import { _IsConfigurationAvailable, DracoCodec } from \"./dracoCodec.js\";\nimport { Tools } from \"../../Misc/tools.js\";\nimport { Geometry } from \"../geometry.js\";\nimport { VertexBuffer } from \"../buffer.js\";\nimport { Logger } from \"../../Misc/logger.js\";\nimport { DecodeMesh, DecoderWorkerFunction } from \"./dracoCompressionWorker.js\";\n/**\n * @experimental This class is an experimental version of `DracoCompression` and is subject to change.\n *\n * Draco Decoder (https://google.github.io/draco/)\n *\n * This class wraps the Draco decoder module.\n *\n * By default, the configuration points to a copy of the Draco decoder files for glTF from the Babylon.js cdn https://cdn.babylonjs.com/draco_wasm_wrapper_gltf.js.\n *\n * To update the configuration, use the following code:\n * ```javascript\n *     DracoDecoder.DefaultConfiguration = {\n *          wasmUrl: \"<url to the WebAssembly library>\",\n *          wasmBinaryUrl: \"<url to the WebAssembly binary>\",\n *          fallbackUrl: \"<url to the fallback JavaScript library>\",\n *     };\n * ```\n *\n * Draco has two versions, one for WebAssembly and one for JavaScript. The decoder configuration can be set to only support WebAssembly or only support the JavaScript version.\n * Decoding will automatically fallback to the JavaScript version if WebAssembly version is not configured or if WebAssembly is not supported by the browser.\n * Use `DracoDecoder.DefaultAvailable` to determine if the decoder configuration is available for the current context.\n *\n * To decode Draco compressed data, get the default DracoDecoder object and call decodeMeshToGeometryAsync:\n * ```javascript\n *     var geometry = await DracoDecoder.Default.decodeMeshToGeometryAsync(data);\n * ```\n */\nexport class DracoDecoder extends DracoCodec {\n    /**\n     * Returns true if the decoder's `DefaultConfiguration` is available.\n     */\n    static get DefaultAvailable() {\n        return _IsConfigurationAvailable(DracoDecoder.DefaultConfiguration);\n    }\n    /**\n     * Default instance for the DracoDecoder.\n     */\n    static get Default() {\n        DracoDecoder._Default ?? (DracoDecoder._Default = new DracoDecoder());\n        return DracoDecoder._Default;\n    }\n    /**\n     * Reset the default DracoDecoder object to null and disposing the removed default instance.\n     * Note that if the workerPool is a member of the static DefaultConfiguration object it is recommended not to run dispose,\n     * unless the static worker pool is no longer needed.\n     * @param skipDispose set to true to not dispose the removed default instance\n     */\n    static ResetDefault(skipDispose) {\n        if (DracoDecoder._Default) {\n            if (!skipDispose) {\n                DracoDecoder._Default.dispose();\n            }\n            DracoDecoder._Default = null;\n        }\n    }\n    _isModuleAvailable() {\n        return typeof DracoDecoderModule !== \"undefined\";\n    }\n    async _createModuleAsync(wasmBinary, jsModule /** DracoDecoderModule */) {\n        const module = await (jsModule || DracoDecoderModule)({ wasmBinary });\n        return { module };\n    }\n    _getWorkerContent() {\n        return `${DecodeMesh}(${DecoderWorkerFunction})()`;\n    }\n    /**\n     * Creates a new Draco decoder.\n     * @param configuration Optional override of the configuration for the DracoDecoder. If not provided, defaults to {@link DracoDecoder.DefaultConfiguration}.\n     */\n    constructor(configuration = DracoDecoder.DefaultConfiguration) {\n        super(configuration);\n    }\n    /**\n     * Decode Draco compressed mesh data to mesh data.\n     * @param data The ArrayBuffer or ArrayBufferView of the compressed Draco data\n     * @param attributes A map of attributes from vertex buffer kinds to Draco unique ids\n     * @param gltfNormalizedOverride A map of attributes from vertex buffer kinds to normalized flags to override the Draco normalization\n     * @returns A promise that resolves with the decoded mesh data\n     */\n    decodeMeshToMeshDataAsync(data, attributes, gltfNormalizedOverride) {\n        const dataView = data instanceof ArrayBuffer ? new Int8Array(data) : new Int8Array(data.buffer, data.byteOffset, data.byteLength);\n        const applyGltfNormalizedOverride = (kind, normalized) => {\n            if (gltfNormalizedOverride && gltfNormalizedOverride[kind] !== undefined) {\n                if (normalized !== gltfNormalizedOverride[kind]) {\n                    Logger.Warn(`Normalized flag from Draco data (${normalized}) does not match normalized flag from glTF accessor (${gltfNormalizedOverride[kind]}). Using flag from glTF accessor.`);\n                }\n                return gltfNormalizedOverride[kind];\n            }\n            else {\n                return normalized;\n            }\n        };\n        if (this._workerPoolPromise) {\n            return this._workerPoolPromise.then((workerPool) => {\n                return new Promise((resolve, reject) => {\n                    workerPool.push((worker, onComplete) => {\n                        let resultIndices = null;\n                        const resultAttributes = [];\n                        const onError = (error) => {\n                            worker.removeEventListener(\"error\", onError);\n                            worker.removeEventListener(\"message\", onMessage);\n                            reject(error);\n                            onComplete();\n                        };\n                        const onMessage = (event) => {\n                            const message = event.data;\n                            switch (message.id) {\n                                case \"indices\": {\n                                    resultIndices = message.data;\n                                    break;\n                                }\n                                case \"attribute\": {\n                                    resultAttributes.push({\n                                        kind: message.kind,\n                                        data: message.data,\n                                        size: message.size,\n                                        byteOffset: message.byteOffset,\n                                        byteStride: message.byteStride,\n                                        normalized: applyGltfNormalizedOverride(message.kind, message.normalized),\n                                    });\n                                    break;\n                                }\n                                case \"decodeMeshDone\": {\n                                    worker.removeEventListener(\"error\", onError);\n                                    worker.removeEventListener(\"message\", onMessage);\n                                    resolve({ indices: resultIndices, attributes: resultAttributes, totalVertices: message.totalVertices });\n                                    onComplete();\n                                    break;\n                                }\n                            }\n                        };\n                        worker.addEventListener(\"error\", onError);\n                        worker.addEventListener(\"message\", onMessage);\n                        const dataViewCopy = dataView.slice();\n                        worker.postMessage({ id: \"decodeMesh\", dataView: dataViewCopy, attributes: attributes }, [dataViewCopy.buffer]);\n                    });\n                });\n            });\n        }\n        if (this._modulePromise) {\n            return this._modulePromise.then((decoder) => {\n                let resultIndices = null;\n                const resultAttributes = [];\n                const numPoints = DecodeMesh(decoder.module, dataView, attributes, (indices) => {\n                    resultIndices = indices;\n                }, (kind, data, size, byteOffset, byteStride, normalized) => {\n                    resultAttributes.push({\n                        kind,\n                        data,\n                        size,\n                        byteOffset,\n                        byteStride,\n                        normalized,\n                    });\n                });\n                return { indices: resultIndices, attributes: resultAttributes, totalVertices: numPoints };\n            });\n        }\n        throw new Error(\"Draco decoder module is not available\");\n    }\n    /**\n     * Decode Draco compressed mesh data to Babylon geometry.\n     * @param name The name to use when creating the geometry\n     * @param scene The scene to use when creating the geometry\n     * @param data The ArrayBuffer or ArrayBufferView of the Draco compressed data\n     * @param attributes A map of attributes from vertex buffer kinds to Draco unique ids\n     * @returns A promise that resolves with the decoded geometry\n     */\n    async decodeMeshToGeometryAsync(name, scene, data, attributes) {\n        const meshData = await this.decodeMeshToMeshDataAsync(data, attributes);\n        const geometry = new Geometry(name, scene);\n        if (meshData.indices) {\n            geometry.setIndices(meshData.indices);\n        }\n        for (const attribute of meshData.attributes) {\n            geometry.setVerticesBuffer(new VertexBuffer(scene.getEngine(), attribute.data, attribute.kind, false, undefined, attribute.byteStride, undefined, attribute.byteOffset, attribute.size, undefined, attribute.normalized, true), meshData.totalVertices);\n        }\n        return geometry;\n    }\n    /** @internal */\n    async _decodeMeshToGeometryForGltfAsync(name, scene, data, attributes, gltfNormalizedOverride, boundingInfo) {\n        const meshData = await this.decodeMeshToMeshDataAsync(data, attributes, gltfNormalizedOverride);\n        const geometry = new Geometry(name, scene);\n        if (boundingInfo) {\n            geometry._boundingInfo = boundingInfo;\n            geometry.useBoundingInfoFromGeometry = true;\n        }\n        if (meshData.indices) {\n            geometry.setIndices(meshData.indices);\n        }\n        for (const attribute of meshData.attributes) {\n            geometry.setVerticesBuffer(new VertexBuffer(scene.getEngine(), attribute.data, attribute.kind, false, undefined, attribute.byteStride, undefined, attribute.byteOffset, attribute.size, undefined, attribute.normalized, true), meshData.totalVertices);\n        }\n        return geometry;\n    }\n}\n/**\n * Default configuration for the DracoDecoder. Defaults to the following:\n * - numWorkers: 50% of the available logical processors, capped to 4. If no logical processors are available, defaults to 1.\n * - wasmUrl: `\"https://cdn.babylonjs.com/draco_wasm_wrapper_gltf.js\"`\n * - wasmBinaryUrl: `\"https://cdn.babylonjs.com/draco_decoder_gltf.wasm\"`\n * - fallbackUrl: `\"https://cdn.babylonjs.com/draco_decoder_gltf.js\"`\n */\nDracoDecoder.DefaultConfiguration = {\n    wasmUrl: `${Tools._DefaultCdnUrl}/draco_wasm_wrapper_gltf.js`,\n    wasmBinaryUrl: `${Tools._DefaultCdnUrl}/draco_decoder_gltf.wasm`,\n    fallbackUrl: `${Tools._DefaultCdnUrl}/draco_decoder_gltf.js`,\n};\nDracoDecoder._Default = null;\n//# sourceMappingURL=dracoDecoder.js.map", "import { DracoDecoder } from \"@babylonjs/core/Meshes/Compression/dracoDecoder.js\";\nimport { VertexBuffer } from \"@babylonjs/core/Buffers/buffer.js\";\nimport { GLTFLoader, ArrayItem, LoadBoundingInfoFromPositionAccessor } from \"../glTFLoader.js\";\nimport { registerGLTFExtension, unregisterGLTFExtension } from \"../glTFLoaderExtensionRegistry.js\";\nconst NAME = \"KHR_draco_mesh_compression\";\n/**\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_draco_mesh_compression/README.md)\n */\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport class KHR_draco_mesh_compression {\n    /**\n     * @internal\n     */\n    constructor(loader) {\n        /**\n         * The name of this extension.\n         */\n        this.name = NAME;\n        /**\n         * Defines whether to use the normalized flag from the glTF accessor instead of the Draco data. Defaults to true.\n         */\n        this.useNormalizedFlagFromAccessor = true;\n        this._loader = loader;\n        this.enabled = DracoDecoder.DefaultAvailable && this._loader.isExtensionUsed(NAME);\n    }\n    /** @internal */\n    dispose() {\n        delete this.dracoDecoder;\n        this._loader = null;\n    }\n    /**\n     * @internal\n     */\n    _loadVertexDataAsync(context, primitive, babylonMesh) {\n        return GLTFLoader.LoadExtensionAsync(context, primitive, this.name, (extensionContext, extension) => {\n            if (primitive.mode != undefined) {\n                if (primitive.mode !== 4 /* MeshPrimitiveMode.TRIANGLES */ && primitive.mode !== 5 /* MeshPrimitiveMode.TRIANGLE_STRIP */) {\n                    throw new Error(`${context}: Unsupported mode ${primitive.mode}`);\n                }\n            }\n            const attributes = {};\n            const normalized = {};\n            const loadAttribute = (name, kind) => {\n                const uniqueId = extension.attributes[name];\n                if (uniqueId == undefined) {\n                    return;\n                }\n                babylonMesh._delayInfo = babylonMesh._delayInfo || [];\n                if (babylonMesh._delayInfo.indexOf(kind) === -1) {\n                    babylonMesh._delayInfo.push(kind);\n                }\n                attributes[kind] = uniqueId;\n                if (this.useNormalizedFlagFromAccessor) {\n                    const accessor = ArrayItem.TryGet(this._loader.gltf.accessors, primitive.attributes[name]);\n                    if (accessor) {\n                        normalized[kind] = accessor.normalized || false;\n                    }\n                }\n            };\n            loadAttribute(\"POSITION\", VertexBuffer.PositionKind);\n            loadAttribute(\"NORMAL\", VertexBuffer.NormalKind);\n            loadAttribute(\"TANGENT\", VertexBuffer.TangentKind);\n            loadAttribute(\"TEXCOORD_0\", VertexBuffer.UVKind);\n            loadAttribute(\"TEXCOORD_1\", VertexBuffer.UV2Kind);\n            loadAttribute(\"TEXCOORD_2\", VertexBuffer.UV3Kind);\n            loadAttribute(\"TEXCOORD_3\", VertexBuffer.UV4Kind);\n            loadAttribute(\"TEXCOORD_4\", VertexBuffer.UV5Kind);\n            loadAttribute(\"TEXCOORD_5\", VertexBuffer.UV6Kind);\n            loadAttribute(\"JOINTS_0\", VertexBuffer.MatricesIndicesKind);\n            loadAttribute(\"WEIGHTS_0\", VertexBuffer.MatricesWeightsKind);\n            loadAttribute(\"COLOR_0\", VertexBuffer.ColorKind);\n            const bufferView = ArrayItem.Get(extensionContext, this._loader.gltf.bufferViews, extension.bufferView);\n            if (!bufferView._dracoBabylonGeometry) {\n                bufferView._dracoBabylonGeometry = this._loader.loadBufferViewAsync(`/bufferViews/${bufferView.index}`, bufferView).then((data) => {\n                    const dracoDecoder = this.dracoDecoder || DracoDecoder.Default;\n                    const positionAccessor = ArrayItem.TryGet(this._loader.gltf.accessors, primitive.attributes[\"POSITION\"]);\n                    const babylonBoundingInfo = !this._loader.parent.alwaysComputeBoundingBox && !babylonMesh.skeleton && positionAccessor ? LoadBoundingInfoFromPositionAccessor(positionAccessor) : null;\n                    return dracoDecoder\n                        ._decodeMeshToGeometryForGltfAsync(babylonMesh.name, this._loader.babylonScene, data, attributes, normalized, babylonBoundingInfo)\n                        .catch((error) => {\n                        throw new Error(`${context}: ${error.message}`);\n                    });\n                });\n            }\n            return bufferView._dracoBabylonGeometry;\n        });\n    }\n}\nunregisterGLTFExtension(NAME);\nregisterGLTFExtension(NAME, true, (loader) => new KHR_draco_mesh_compression(loader));\n//# sourceMappingURL=KHR_draco_mesh_compression.js.map"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "module", "data", "attributeIDs", "onIndicesData", "onAttributeData", "decoderModule", "decoder", "buffer", "geometry", "status", "type", "mesh", "numIndices", "byteLength", "ptr", "indices", "pointCloud", "numPoints", "processAttribute", "kind", "attribute", "dataType", "numComponents", "normalized", "byteStride", "byteOffset", "info", "numValues", "id", "dracoAttributeTypes", "DecoderWorkerFunction", "decoderPromise", "event", "message", "initDecoderObject", "size", "offset", "stride", "initializeWebWorker", "worker", "wasmBinary", "moduleUrl", "resolve", "reject", "onError", "error", "onMessage", "clone", "_GetDefaultNumWorkers", "_IsConfigurationAvailable", "config", "DracoCodec", "configuration", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "numberOfWorkers", "useWorkers", "url<PERSON><PERSON>ed", "codecInfo", "Tools", "workerContent", "workerBlobUrl", "AutoReleaseWorkerPool", "workerPool", "DracoDecoder", "skipDispose", "jsModule", "attributes", "gltfNormalizedOverride", "dataView", "applyGltfNormalizedOverride", "<PERSON><PERSON>", "onComplete", "resultIndices", "resultAttributes", "dataViewCopy", "name", "scene", "meshData", "Geometry", "VertexBuffer", "boundingInfo", "NAME", "KHR_draco_mesh_compression", "loader", "context", "primitive", "<PERSON><PERSON><PERSON><PERSON>", "GLTFLoader", "extensionContext", "extension", "loadAttribute", "uniqueId", "accessor", "ArrayItem", "bufferView", "dracoDecoder", "positionAccessor", "babylonBoundingInfo", "LoadBoundingInfoFromPositionAccessor", "unregisterGLTFExtension", "registerGLTFExtension"], "mappings": "4aA0HO,SAASA,EAAWC,EAA6BC,EAAMC,EAAcC,EAAeC,EAAiB,CACxG,MAAMC,EAAgBL,EACtB,IAAIM,EAAU,KACVC,EAAS,KACTC,EAAW,KACf,GAAI,CACAF,EAAU,IAAID,EAAc,QAC5BE,EAAS,IAAIF,EAAc,cAC3BE,EAAO,KAAKN,EAAMA,EAAK,UAAU,EACjC,IAAIQ,EACJ,MAAMC,EAAOJ,EAAQ,uBAAuBC,CAAM,EAClD,OAAQG,EAAI,CACR,KAAKL,EAAc,gBAAiB,CAChC,MAAMM,EAAO,IAAIN,EAAc,KAE/B,GADAI,EAASH,EAAQ,mBAAmBC,EAAQI,CAAI,EAC5C,CAACF,EAAO,GAAE,GAAME,EAAK,MAAQ,EAC7B,MAAM,IAAI,MAAMF,EAAO,UAAW,CAAA,EAGtC,MAAMG,EADWD,EAAK,YACQ,EACxBE,EAAaD,EAAa,EAC1BE,EAAMT,EAAc,QAAQQ,CAAU,EAC5C,GAAI,CACAP,EAAQ,wBAAwBK,EAAME,EAAYC,CAAG,EACrD,MAAMC,EAAU,IAAI,YAAYH,CAAU,EAC1CG,EAAQ,IAAI,IAAI,YAAYV,EAAc,QAAQ,OAAQS,EAAKF,CAAU,CAAC,EAC1ET,EAAcY,CAAO,CACxB,QACO,CACJV,EAAc,MAAMS,CAAG,CAC1B,CACDN,EAAWG,EACX,KACH,CACD,KAAKN,EAAc,YAAa,CAC5B,MAAMW,EAAa,IAAIX,EAAc,WAErC,GADAI,EAASH,EAAQ,yBAAyBC,EAAQS,CAAU,EACxD,CAACP,EAAO,GAAI,GAAI,CAACO,EAAW,IAC5B,MAAM,IAAI,MAAMP,EAAO,UAAW,CAAA,EAEtCD,EAAWQ,EACX,KACH,CACD,QACI,MAAM,IAAI,MAAM,yBAAyBN,CAAI,EAAE,CAEtD,CACD,MAAMO,EAAYT,EAAS,aACrBU,EAAmB,CAACZ,EAASE,EAAUW,EAAMC,IAA+B,CAC9E,MAAMC,EAAWD,EAAU,YACrBE,EAAgBF,EAAU,iBAC1BG,EAAaH,EAAU,aACvBI,EAAaJ,EAAU,cACvBK,EAAaL,EAAU,cAUvBM,EATe,CACjB,CAACrB,EAAc,UAAU,EAAG,CAAE,sBAAuB,aAAc,KAAMA,EAAc,OAAS,EAChG,CAACA,EAAc,OAAO,EAAG,CAAE,sBAAuB,UAAW,KAAMA,EAAc,KAAO,EACxF,CAACA,EAAc,QAAQ,EAAG,CAAE,sBAAuB,WAAY,KAAMA,EAAc,MAAQ,EAC3F,CAACA,EAAc,QAAQ,EAAG,CAAE,sBAAuB,WAAY,KAAMA,EAAc,MAAQ,EAC3F,CAACA,EAAc,QAAQ,EAAG,CAAE,sBAAuB,WAAY,KAAMA,EAAc,MAAQ,EAC3F,CAACA,EAAc,SAAS,EAAG,CAAE,sBAAuB,YAAa,KAAMA,EAAc,OAAS,EAC9F,CAACA,EAAc,SAAS,EAAG,CAAE,sBAAuB,YAAa,KAAMA,EAAc,OAAS,CAC9G,EACsCgB,CAAQ,EAClC,GAAI,CAACK,EACD,MAAM,IAAI,MAAM,qBAAqBL,CAAQ,EAAE,EAEnD,MAAMM,EAAYV,EAAYK,EACxBT,EAAac,EAAYD,EAAK,sBAAsB,kBACpDZ,EAAMT,EAAc,QAAQQ,CAAU,EAC5C,GAAI,CACAP,EAAQ,kCAAkCE,EAAUY,EAAWC,EAAUR,EAAYC,CAAG,EACxF,MAAMb,EAAO,IAAIyB,EAAK,sBAAsBA,EAAK,KAAK,OAAQZ,EAAKa,CAAS,EAC5EvB,EAAgBe,EAAMlB,EAAK,MAAK,EAAIqB,EAAeG,EAAYD,EAAYD,CAAU,CACxF,QACO,CACJlB,EAAc,MAAMS,CAAG,CAC1B,CACb,EACQ,GAAIZ,EACA,UAAWiB,KAAQjB,EAAc,CAC7B,MAAM0B,EAAK1B,EAAaiB,CAAI,EACtBC,EAAYd,EAAQ,uBAAuBE,EAAUoB,CAAE,EAC7DV,EAAiBZ,EAASE,EAAUW,EAAMC,CAAS,CACtD,KAEA,CACD,MAAMS,EAAsB,CACxB,SAAUxB,EAAc,SACxB,OAAQA,EAAc,OACtB,MAAOA,EAAc,MACrB,GAAIA,EAAc,SAClC,EACY,UAAWc,KAAQU,EAAqB,CACpC,MAAMD,EAAKtB,EAAQ,eAAeE,EAAUqB,EAAoBV,CAAI,CAAC,EACrE,GAAIS,IAAO,GAAI,CACX,MAAMR,EAAYd,EAAQ,aAAaE,EAAUoB,CAAE,EACnDV,EAAiBZ,EAASE,EAAUW,EAAMC,CAAS,CACtD,CACJ,CACJ,CACD,OAAOH,CACV,QACO,CACAT,GACAH,EAAc,QAAQG,CAAQ,EAE9BD,GACAF,EAAc,QAAQE,CAAM,EAE5BD,GACAD,EAAc,QAAQC,CAAO,CAEpC,CACL,CAKO,SAASwB,GAAwB,CACpC,IAAIC,EACJ,UAAaC,GAAU,CACnB,MAAMC,EAAUD,EAAM,KACtB,OAAQC,EAAQ,GAAE,CACd,IAAK,OAAQ,CAELA,EAAQ,KACR,cAAcA,EAAQ,GAAG,EAE7B,MAAMC,EAAoBD,EAAQ,WAAa,CAAE,WAAYA,EAAQ,UAAY,EAAG,GACpFF,EAAiB,mBAAmBG,CAAiB,EACrD,YAAY,CAAE,GAAI,UAAU,CAAE,EAC9B,KACH,CACD,IAAK,aAAc,CACf,GAAI,CAACH,EACD,MAAM,IAAI,MAAM,uCAAuC,EAE3DA,EAAe,KAAMzB,GAAY,CAC7B,MAAMW,EAAYlB,EAAWO,EAAS2B,EAAQ,SAAUA,EAAQ,WAAalB,GAAY,CACrF,YAAY,CAAE,GAAI,UAAW,KAAMA,CAAO,EAAI,CAACA,EAAQ,MAAM,CAAC,CACtF,EAAuB,CAACI,EAAMlB,EAAMkC,EAAMC,EAAQC,EAAQd,IAAe,CACjD,YAAY,CAAE,GAAI,YAAa,KAAAJ,EAAM,KAAAlB,EAAM,KAAAkC,EAAM,WAAYC,EAAQ,WAAYC,EAAQ,WAAAd,CAAU,EAAI,CAACtB,EAAK,MAAM,CAAC,CAC5I,CAAqB,EACD,YAAY,CAAE,GAAI,iBAAkB,cAAegB,CAAW,CAAA,CAClF,CAAiB,EACD,KACH,CACJ,CACT,CACA,CAUO,SAASqB,EAAoBC,EAAQC,EAAYC,EAAW,CAC/D,OAAO,IAAI,QAAQ,CAACC,EAASC,IAAW,CACpC,MAAMC,EAAWC,GAAU,CACvBN,EAAO,oBAAoB,QAASK,CAAO,EAC3CL,EAAO,oBAAoB,UAAWO,CAAS,EAC/CH,EAAOE,CAAK,CACxB,EACcC,EAAad,GAAU,CACrBA,EAAM,KAAK,KAAO,aAClBO,EAAO,oBAAoB,QAASK,CAAO,EAC3CL,EAAO,oBAAoB,UAAWO,CAAS,EAC/CJ,EAAQH,CAAM,EAE9B,EAIQ,GAHAA,EAAO,iBAAiB,QAASK,CAAO,EACxCL,EAAO,iBAAiB,UAAWO,CAAS,EAExC,CAACN,EACDD,EAAO,YAAY,CACf,GAAI,OACJ,IAAKE,CACrB,CAAa,MAEA,CAED,MAAMM,EAAQP,EAAW,MAAM,CAAC,EAChCD,EAAO,YAAY,CACf,GAAI,OACJ,IAAKE,EACL,WAAYM,CAC5B,EAAe,CAACA,CAAK,CAAC,CACb,CAET,CAAK,CACL,CCtTO,SAASC,GAAwB,CACpC,OAAI,OAAO,WAAc,UAAY,CAAC,UAAU,oBACrC,EAGJ,KAAK,IAAI,KAAK,MAAM,UAAU,oBAAsB,EAAG,EAAG,CAAC,CACtE,CAIO,SAASC,EAA0BC,EAAQ,CAC9C,MAAO,CAAC,EAAGA,EAAO,UAAYA,EAAO,YAAcA,EAAO,gBAAkB,OAAO,aAAgB,UAAaA,EAAO,YAE3H,CAKO,MAAMC,CAAW,CAKpB,YAAYC,EAAe,CAGvB,GAAIA,EAAc,WAAY,CAE1B,KAAK,mBAAqB,QAAQ,QAAQA,EAAc,UAAU,EAClE,MACH,CAED,MAAMC,EAAqBD,EAAc,WACnCE,EAAkBF,EAAc,YAAcJ,EAAqB,EACnEO,EAAaD,GAAmB,OAAO,QAAW,YAAc,OAAO,KAAQ,WAC/EE,EAAYD,GAAc,CAACH,EAAc,SAEzCK,EAAYL,EAAc,SAAWA,EAAc,eAAiB,OAAO,aAAgB,SAC3F,CACE,IAAKI,EAAYE,EAAM,oBAAoBN,EAAc,QAAS,EAAI,EAAI,GAC1E,kBAAmBC,EACb,QAAQ,QAAQA,CAAkB,EAClCK,EAAM,cAAcA,EAAM,oBAAoBN,EAAc,cAAe,EAAI,CAAC,CACzF,EACC,CACE,IAAKI,EAAYE,EAAM,oBAAoBN,EAAc,WAAW,EAAI,GACxE,kBAAmB,QAAQ,QAAQ,MAAS,CAC5D,EAEYG,EACA,KAAK,mBAAqBE,EAAU,kBAAkB,KAAMjB,GAAe,CACvE,MAAMmB,EAAgB,KAAK,oBACrBC,EAAgB,IAAI,gBAAgB,IAAI,KAAK,CAACD,CAAa,EAAG,CAAE,KAAM,wBAAwB,CAAE,CAAC,EACvG,OAAO,IAAIE,EAAsBP,EAAiB,IAAM,CACpD,MAAMf,EAAS,IAAI,OAAOqB,CAAa,EACvC,OAAOtB,EAAoBC,EAAQC,EAAYiB,EAAU,GAAG,CAChF,CAAiB,CACjB,CAAa,EAGD,KAAK,eAAiBA,EAAU,kBAAkB,KAAK,MAAOjB,GAAe,CACzE,GAAI,CAAC,KAAK,sBACF,CAACY,EAAc,SAAU,CACzB,GAAI,CAACK,EAAU,IACX,MAAM,IAAI,MAAM,qCAAqC,EAEzD,MAAMC,EAAM,uBAAuBD,EAAU,GAAG,CACnD,CAEL,OAAO,KAAK,mBAAmBjB,EAAYY,EAAc,QAAQ,CACjF,CAAa,CAER,CAKD,MAAM,gBAAiB,CACnB,GAAI,KAAK,mBAAoB,CACzB,MAAM,KAAK,mBACX,MACH,CACD,GAAI,KAAK,eAAgB,CACrB,MAAM,KAAK,eACX,MACH,CACJ,CAID,SAAU,CACF,KAAK,oBACL,KAAK,mBAAmB,KAAMU,GAAe,CACzCA,EAAW,QAAO,CAClC,CAAa,EAEL,OAAO,KAAK,mBACZ,OAAO,KAAK,cACf,CACL,CCxEO,MAAMC,UAAqBZ,CAAW,CAIzC,WAAW,kBAAmB,CAC1B,OAAOF,EAA0Bc,EAAa,oBAAoB,CACrE,CAID,WAAW,SAAU,CACjB,OAAAA,EAAa,WAAaA,EAAa,SAAW,IAAIA,GAC/CA,EAAa,QACvB,CAOD,OAAO,aAAaC,EAAa,CACzBD,EAAa,WACRC,GACDD,EAAa,SAAS,UAE1BA,EAAa,SAAW,KAE/B,CACD,oBAAqB,CACjB,OAAO,OAAO,mBAAuB,GACxC,CACD,MAAM,mBAAmBvB,EAAYyB,EAAoC,CAErE,MAAO,CAAE,OADM,MAAOA,GAAY,oBAAoB,CAAE,WAAAzB,CAAU,CAAE,CACrD,CAClB,CACD,mBAAoB,CAChB,MAAO,GAAGzC,CAAU,IAAI+B,CAAqB,KAChD,CAKD,YAAYsB,EAAgBW,EAAa,qBAAsB,CAC3D,MAAMX,CAAa,CACtB,CAQD,0BAA0BnD,EAAMiE,EAAYC,EAAwB,CAChE,MAAMC,EAAWnE,aAAgB,YAAc,IAAI,UAAUA,CAAI,EAAI,IAAI,UAAUA,EAAK,OAAQA,EAAK,WAAYA,EAAK,UAAU,EAC1HoE,EAA8B,CAAClD,EAAMI,IACnC4C,GAA0BA,EAAuBhD,CAAI,IAAM,QACvDI,IAAe4C,EAAuBhD,CAAI,GAC1CmD,EAAO,KAAK,oCAAoC/C,CAAU,wDAAwD4C,EAAuBhD,CAAI,CAAC,mCAAmC,EAE9KgD,EAAuBhD,CAAI,GAG3BI,EAGf,GAAI,KAAK,mBACL,OAAO,KAAK,mBAAmB,KAAMuC,GAC1B,IAAI,QAAQ,CAACpB,EAASC,IAAW,CACpCmB,EAAW,KAAK,CAACvB,EAAQgC,IAAe,CACpC,IAAIC,EAAgB,KACpB,MAAMC,EAAmB,CAAA,EACnB7B,EAAWC,GAAU,CACvBN,EAAO,oBAAoB,QAASK,CAAO,EAC3CL,EAAO,oBAAoB,UAAWO,CAAS,EAC/CH,EAAOE,CAAK,EACZ0B,GAC5B,EAC8BzB,EAAad,GAAU,CACzB,MAAMC,EAAUD,EAAM,KACtB,OAAQC,EAAQ,GAAE,CACd,IAAK,UAAW,CACZuC,EAAgBvC,EAAQ,KACxB,KACH,CACD,IAAK,YAAa,CACdwC,EAAiB,KAAK,CAClB,KAAMxC,EAAQ,KACd,KAAMA,EAAQ,KACd,KAAMA,EAAQ,KACd,WAAYA,EAAQ,WACpB,WAAYA,EAAQ,WACpB,WAAYoC,EAA4BpC,EAAQ,KAAMA,EAAQ,UAAU,CAChH,CAAqC,EACD,KACH,CACD,IAAK,iBAAkB,CACnBM,EAAO,oBAAoB,QAASK,CAAO,EAC3CL,EAAO,oBAAoB,UAAWO,CAAS,EAC/CJ,EAAQ,CAAE,QAAS8B,EAAe,WAAYC,EAAkB,cAAexC,EAAQ,aAAa,CAAE,EACtGsC,IACA,KACH,CACJ,CAC7B,EACwBhC,EAAO,iBAAiB,QAASK,CAAO,EACxCL,EAAO,iBAAiB,UAAWO,CAAS,EAC5C,MAAM4B,EAAeN,EAAS,QAC9B7B,EAAO,YAAY,CAAE,GAAI,aAAc,SAAUmC,EAAc,WAAYR,CAAU,EAAI,CAACQ,EAAa,MAAM,CAAC,CACtI,CAAqB,CACrB,CAAiB,CACJ,EAEL,GAAI,KAAK,eACL,OAAO,KAAK,eAAe,KAAMpE,GAAY,CACzC,IAAIkE,EAAgB,KACpB,MAAMC,EAAmB,CAAA,EACnBxD,EAAYlB,EAAWO,EAAQ,OAAQ8D,EAAUF,EAAanD,GAAY,CAC5EyD,EAAgBzD,CACpC,EAAmB,CAACI,EAAMlB,EAAMkC,EAAMV,EAAYD,EAAYD,IAAe,CACzDkD,EAAiB,KAAK,CAClB,KAAAtD,EACA,KAAAlB,EACA,KAAAkC,EACA,WAAAV,EACA,WAAAD,EACA,WAAAD,CACxB,CAAqB,CACrB,CAAiB,EACD,MAAO,CAAE,QAASiD,EAAe,WAAYC,EAAkB,cAAexD,EAC9F,CAAa,EAEL,MAAM,IAAI,MAAM,uCAAuC,CAC1D,CASD,MAAM,0BAA0B0D,EAAMC,EAAO3E,EAAMiE,EAAY,CAC3D,MAAMW,EAAW,MAAM,KAAK,0BAA0B5E,EAAMiE,CAAU,EAChE1D,EAAW,IAAIsE,EAASH,EAAMC,CAAK,EACrCC,EAAS,SACTrE,EAAS,WAAWqE,EAAS,OAAO,EAExC,UAAWzD,KAAayD,EAAS,WAC7BrE,EAAS,kBAAkB,IAAIuE,EAAaH,EAAM,UAAS,EAAIxD,EAAU,KAAMA,EAAU,KAAM,GAAO,OAAWA,EAAU,WAAY,OAAWA,EAAU,WAAYA,EAAU,KAAM,OAAWA,EAAU,WAAY,EAAI,EAAGyD,EAAS,aAAa,EAE1P,OAAOrE,CACV,CAED,MAAM,kCAAkCmE,EAAMC,EAAO3E,EAAMiE,EAAYC,EAAwBa,EAAc,CACzG,MAAMH,EAAW,MAAM,KAAK,0BAA0B5E,EAAMiE,EAAYC,CAAsB,EACxF3D,EAAW,IAAIsE,EAASH,EAAMC,CAAK,EACrCI,IACAxE,EAAS,cAAgBwE,EACzBxE,EAAS,4BAA8B,IAEvCqE,EAAS,SACTrE,EAAS,WAAWqE,EAAS,OAAO,EAExC,UAAWzD,KAAayD,EAAS,WAC7BrE,EAAS,kBAAkB,IAAIuE,EAAaH,EAAM,UAAS,EAAIxD,EAAU,KAAMA,EAAU,KAAM,GAAO,OAAWA,EAAU,WAAY,OAAWA,EAAU,WAAYA,EAAU,KAAM,OAAWA,EAAU,WAAY,EAAI,EAAGyD,EAAS,aAAa,EAE1P,OAAOrE,CACV,CACL,CAQAuD,EAAa,qBAAuB,CAChC,QAAS,GAAGL,EAAM,cAAc,8BAChC,cAAe,GAAGA,EAAM,cAAc,2BACtC,YAAa,GAAGA,EAAM,cAAc,wBACxC,EACAK,EAAa,SAAW,KClNxB,MAAMkB,EAAO,6BAKN,MAAMC,CAA2B,CAIpC,YAAYC,EAAQ,CAIhB,KAAK,KAAOF,EAIZ,KAAK,8BAAgC,GACrC,KAAK,QAAUE,EACf,KAAK,QAAUpB,EAAa,kBAAoB,KAAK,QAAQ,gBAAgBkB,CAAI,CACpF,CAED,SAAU,CACN,OAAO,KAAK,aACZ,KAAK,QAAU,IAClB,CAID,qBAAqBG,EAASC,EAAWC,EAAa,CAClD,OAAOC,EAAW,mBAAmBH,EAASC,EAAW,KAAK,KAAM,CAACG,EAAkBC,IAAc,CACjG,GAAIJ,EAAU,MAAQ,MACdA,EAAU,OAAS,GAAuCA,EAAU,OAAS,EAC7E,MAAM,IAAI,MAAM,GAAGD,CAAO,sBAAsBC,EAAU,IAAI,EAAE,EAGxE,MAAMnB,EAAa,CAAA,EACb3C,EAAa,CAAA,EACbmE,EAAgB,CAACf,EAAMxD,IAAS,CAClC,MAAMwE,EAAWF,EAAU,WAAWd,CAAI,EAC1C,GAAIgB,GAAY,OAGhBL,EAAY,WAAaA,EAAY,YAAc,CAAA,EAC/CA,EAAY,WAAW,QAAQnE,CAAI,IAAM,IACzCmE,EAAY,WAAW,KAAKnE,CAAI,EAEpC+C,EAAW/C,CAAI,EAAIwE,EACf,KAAK,+BAA+B,CACpC,MAAMC,EAAWC,EAAU,OAAO,KAAK,QAAQ,KAAK,UAAWR,EAAU,WAAWV,CAAI,CAAC,EACrFiB,IACArE,EAAWJ,CAAI,EAAIyE,EAAS,YAAc,GAEjD,CACjB,EACYF,EAAc,WAAYX,EAAa,YAAY,EACnDW,EAAc,SAAUX,EAAa,UAAU,EAC/CW,EAAc,UAAWX,EAAa,WAAW,EACjDW,EAAc,aAAcX,EAAa,MAAM,EAC/CW,EAAc,aAAcX,EAAa,OAAO,EAChDW,EAAc,aAAcX,EAAa,OAAO,EAChDW,EAAc,aAAcX,EAAa,OAAO,EAChDW,EAAc,aAAcX,EAAa,OAAO,EAChDW,EAAc,aAAcX,EAAa,OAAO,EAChDW,EAAc,WAAYX,EAAa,mBAAmB,EAC1DW,EAAc,YAAaX,EAAa,mBAAmB,EAC3DW,EAAc,UAAWX,EAAa,SAAS,EAC/C,MAAMe,EAAaD,EAAU,IAAIL,EAAkB,KAAK,QAAQ,KAAK,YAAaC,EAAU,UAAU,EACtG,OAAKK,EAAW,wBACZA,EAAW,sBAAwB,KAAK,QAAQ,oBAAoB,gBAAgBA,EAAW,KAAK,GAAIA,CAAU,EAAE,KAAM7F,GAAS,CAC/H,MAAM8F,EAAe,KAAK,cAAgBhC,EAAa,QACjDiC,EAAmBH,EAAU,OAAO,KAAK,QAAQ,KAAK,UAAWR,EAAU,WAAW,QAAW,EACjGY,EAAsB,CAAC,KAAK,QAAQ,OAAO,0BAA4B,CAACX,EAAY,UAAYU,EAAmBE,EAAqCF,CAAgB,EAAI,KAClL,OAAOD,EACF,kCAAkCT,EAAY,KAAM,KAAK,QAAQ,aAAcrF,EAAMiE,EAAY3C,EAAY0E,CAAmB,EAChI,MAAOpD,GAAU,CAClB,MAAM,IAAI,MAAM,GAAGuC,CAAO,KAAKvC,EAAM,OAAO,EAAE,CACtE,CAAqB,CACrB,CAAiB,GAEEiD,EAAW,qBAC9B,CAAS,CACJ,CACL,CACAK,EAAwBlB,CAAI,EAC5BmB,EAAsBnB,EAAM,GAAOE,GAAW,IAAID,EAA2BC,CAAM,CAAC", "x_google_ignoreList": [0, 1, 2, 3]}