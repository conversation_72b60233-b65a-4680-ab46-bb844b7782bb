var f=Object.prototype.hasOwnProperty;function s(t,e,r){for(r of t.keys())if(u(r,e))return r}function u(t,e){var r,i,n;if(t===e)return!0;if(t&&e&&(r=t.constructor)===e.constructor){if(r===Date)return t.getTime()===e.getTime();if(r===RegExp)return t.toString()===e.toString();if(r===Array){if((i=t.length)===e.length)for(;i--&&u(t[i],e[i]););return i===-1}if(r===Set){if(t.size!==e.size)return!1;for(i of t)if(n=i,n&&typeof n=="object"&&(n=s(e,n),!n)||!e.has(n))return!1;return!0}if(r===Map){if(t.size!==e.size)return!1;for(i of t)if(n=i[0],n&&typeof n=="object"&&(n=s(e,n),!n)||!u(i[1],e.get(n)))return!1;return!0}if(r===ArrayBuffer)t=new Uint8Array(t),e=new Uint8Array(e);else if(r===DataView){if((i=t.byteLength)===e.byteLength)for(;i--&&t.getInt8(i)===e.getInt8(i););return i===-1}if(ArrayBuffer.isView(t)){if((i=t.byteLength)===e.byteLength)for(;i--&&t[i]===e[i];);return i===-1}if(!r||typeof t=="object"){i=0;for(r in t)if(f.call(t,r)&&++i&&!f.call(e,r)||!(r in e)||!u(t[r],e[r]))return!1;return Object.keys(e).length===i}}return t!==t&&e!==e}export{u as d};
//# sourceMappingURL=index-tFQomdd2.js.map
