{"name": "@self/build", "version": "0.2.1", "description": "Gradio UI packages", "type": "module", "main": "out/index.js", "private": "true", "author": "", "license": "ISC", "scripts": {"build": "esbuild src/index.ts --platform=node --format=esm --target=node18 --bundle --packages=external --outfile=out/index.js && cp src/component_loader.js out/"}, "dependencies": {"@gradio/theme": "workspace:^", "esbuild": "^0.21.0", "svelte-i18n": "^3.6.0"}, "peerDependencies": {"svelte": "^4.0.0"}, "main_changeset": true, "repository": {"type": "git", "url": "git+https://github.com/gradio-app/gradio.git", "directory": "js/build"}}