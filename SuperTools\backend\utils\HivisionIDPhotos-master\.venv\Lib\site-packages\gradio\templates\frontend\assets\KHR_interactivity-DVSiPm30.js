const __vite__fileDeps=["./flowGraphPlayAnimationBlock-DQh66smU.js","./declarationMapper-r-RREw_K.js","./index-Cb4A4-Xi.js","./index-Ccc2t4AG.js","./index-Dz5CWoA7.css","./animationGroup-BasjwvgR.js","./bone-CgNwSK5F.js","./objectModelMapping-D3Nr8hfO.js","./flowGraphStopAnimationBlock-Ad8vc_ad.js","./flowGraphPauseAnimationBlock-DEDB__5r.js","./flowGraphInterpolationBlock-BqJW2qFy.js","./flowGraphSceneReadyEventBlock-Cs83muND.js","./flowGraphSceneTickEventBlock-C0G_eXum.js","./flowGraphSendCustomEventBlock-DfkJ-s1L.js","./flowGraphReceiveCustomEventBlock-BNMGCFYB.js","./flowGraphMeshPickEventBlock-CvXmR7ch.js","./flowGraphMathBlocks-BzIhuErF.js","./flowGraphBinaryOperationBlock-DaBPP43z.js","./flowGraphCachedOperationBlock-D--7yusk.js","./flowGraphUnaryOperationBlock-BsZVallq.js","./flowGraphTernaryOperationBlock-DqI69tK1.js","./flowGraphVectorMathBlocks-M_wfn49G.js","./flowGraphMatrixMathBlocks-Bixr0sb_.js","./flowGraphBranchBlock-Cmz3HUH_.js","./flowGraphSetDelayBlock-DsrOvLHy.js","./flowGraphCancelDelayBlock-DR3yfbrj.js","./flowGraphCounterBlock-BazS5lEJ.js","./flowGraphDebounceBlock-DEdqoOLr.js","./flowGraphThrottleBlock-D2tWef1Y.js","./flowGraphDoNBlock-DXcMjbAJ.js","./flowGraphFlipFlopBlock-D-QhtGXk.js","./flowGraphForLoopBlock-9LTiiNg-.js","./flowGraphMultiGateBlock-jaDVjzzS.js","./flowGraphSequenceBlock-Cr5WTdwC.js","./flowGraphSwitchBlock-C4FBt2Pq.js","./flowGraphWaitAllBlock-daeh0TjD.js","./flowGraphWhileLoopBlock-CKjqOxbf.js","./flowGraphConsoleLogBlock-CySDeX0T.js","./flowGraphConditionalDataBlock-CQ_HDF-s.js","./flowGraphConstantBlock-D1Zk6TpZ.js","./flowGraphTransformCoordinatesSystemBlock-DVpbFbxT.js","./flowGraphGetAssetBlock-C3i8osTa.js","./flowGraphGetPropertyBlock-D0IaRwxo.js","./flowGraphSetPropertyBlock-BwaB_U6k.js","./flowGraphGetVariableBlock-BAu4mxNU.js","./flowGraphSetVariableBlock-BSyFUWPH.js","./flowGraphJsonPointerParserBlock-DqXDIgg0.js","./flowGraphMathCombineExtractBlocks-BIFp8oDY.js","./flowGraphTypeToTypeBlocks-UXTyliE6.js","./flowGraphEasingBlock-BnT2uZ12.js","./flowGraphBezierCurveEasingBlock-DGAdV_J2.js","./flowGraphPointerOverEventBlock-DxP_c5QG.js","./flowGraphPointerOutEventBlock-BWnyltcF.js","./flowGraphContextBlock-DRZMpl2V.js","./flowGraphArrayIndexBlock-Cky7vvd2.js","./flowGraphCodeExecutionBlock-B5ccdBOn.js","./flowGraphIndexOfBlock-6T3vKTlj.js","./flowGraphFunctionReferenceBlock-BH8LN4BJ.js","./flowGraphDataSwitchBlock-DjcRlAy4.js","./flowGraphGLTFDataProvider-CX2EVY_9.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{_ as n}from"./index-Ccc2t4AG.js";import{M as et,ay as ot,V as Q,az as rt,Q as R,C as at,aS as nt,h as p,_ as it,s as st,e as F,O,R as J,ax as q,an as lt,ao as ct}from"./index-Cb4A4-Xi.js";import{n as ut,p as pt,F as ht,g as _t,r as dt,s as $}from"./declarationMapper-r-RREw_K.js";import{G as mt,A as B}from"./objectModelMapping-D3Nr8hfO.js";function ft(r){return r==="Mesh"||r==="AbstractMesh"||r==="GroundMesh"||r==="InstanceMesh"||r==="LinesMesh"||r==="GoldbergMesh"||r==="GreasedLineMesh"||r==="TrailMesh"}function Z(r){return r==="Vector2"||r==="Vector3"||r==="Vector4"||r==="Quaternion"||r==="Color3"||r==="Color4"}function wt(r){return r==="Matrix"||r==="Matrix2D"||r==="Matrix3D"}function gt(r){return r==="AnimationGroup"}function Et(r,t,e=!1){if(r==="Vector2")return ot.FromArray(t);if(r==="Vector3")return e&&(t[2]*=-1),Q.FromArray(t);if(r==="Vector4")return rt.FromArray(t);if(r==="Quaternion")return e&&(t[2]*=-1,t[3]*=-1),R.FromArray(t);if(r==="Color3")return new at(t[0],t[1],t[2]);if(r==="Color4")return new nt(t[0],t[1],t[2],t[3]);throw new Error(`Unknown vector class name ${r}`)}function M(r,t,e){const o=t?.getClassName?.()??"";if(Z(o)||wt(o))e[r]={value:t.asArray(),className:o};else if(o==="FlowGraphInteger")e[r]={value:t.value,className:o};else if(o&&(t.id||t.name))e[r]={id:t.id,name:t.name,className:o};else if(typeof t!="object")e[r]=t;else throw new Error(`Could not serialize value ${t}`)}function x(r,t,e,o){const a=t[r];let i;const s=a?.type??a?.className;if(ft(s)){let c=o.meshes.filter(l=>a.id?l.id===a.id:l.name===a.name);c.length===0&&(c=o.transformNodes.filter(l=>a.id?l.id===a.id:l.name===a.name)),i=a.uniqueId?c.find(l=>l.uniqueId===a.uniqueId):c[0]}else if(Z(s))i=Et(s,a.value);else if(gt(s)){const c=o.animationGroups.filter(l=>l.name===a.name);i=c.length===1?c[0]:c.find(l=>l.uniqueId===a.uniqueId)}else s==="Matrix"?i=et.FromArray(a.value):s==="Matrix2D"?i=new ut(a.value):s==="Matrix3D"?i=new pt(a.value):s==="FlowGraphInteger"?i=ht.FromValue(a.value):s==="number"||s==="string"||s==="boolean"?i=a.value[0]:a&&a.value!==void 0?i=a.value:Array.isArray(a)?i=a.reduce((c,l)=>(l.eventData&&(c[l.id]={type:_t(l.type)},typeof l.value<"u"&&(c[l.id].value=x("value",l,e,o))),c),{}):i=a;return i}function yt(r){return r==="FlowGraphJsonPointerParserBlock"}var K;(function(r){r.Animation="Animation",r.AnimationGroup="AnimationGroup",r.Mesh="Mesh",r.Material="Material",r.Camera="Camera",r.Light="Light"})(K||(K={}));function vt(r,t,e,o){switch(t){case"Animation":return o?r.animations.find(a=>a.uniqueId===e)??null:r.animations[e]??null;case"AnimationGroup":return o?r.animationGroups.find(a=>a.uniqueId===e)??null:r.animationGroups[e]??null;case"Mesh":return o?r.meshes.find(a=>a.uniqueId===e)??null:r.meshes[e]??null;case"Material":return o?r.materials.find(a=>a.uniqueId===e)??null:r.materials[e]??null;case"Camera":return o?r.cameras.find(a=>a.uniqueId===e)??null:r.cameras[e]??null;case"Light":return o?r.lights.find(a=>a.uniqueId===e)??null:r.lights[e]??null;default:return null}}var U;(function(r){r.ExecuteBlock="ExecuteBlock",r.ExecuteEvent="ExecuteEvent",r.TriggerConnection="TriggerConnection",r.ContextVariableSet="ContextVariableSet",r.GlobalVariableSet="GlobalVariableSet",r.GlobalVariableDelete="GlobalVariableDelete",r.GlobalVariableGet="GlobalVariableGet",r.AddConnection="AddConnection",r.GetConnectionValue="GetConnectionValue",r.SetConnectionValue="SetConnectionValue",r.ActivateSignal="ActivateSignal",r.ContextVariableGet="ContextVariableGet"})(U||(U={}));class kt{constructor(){this.logToConsole=!1,this.log=[]}addLogItem(t){if(t.time||(t.time=Date.now()),this.log.push(t),this.logToConsole){const e=t.payload?.value;typeof e=="object"&&e.getClassName?p.Log(`[FGLog] ${t.className}:${t.uniqueId.split("-")[0]} ${t.action} - ${JSON.stringify(e.getClassName())}: ${e.toString()}`):p.Log(`[FGLog] ${t.className}:${t.uniqueId.split("-")[0]} ${t.action} - ${JSON.stringify(t.payload)}`)}}getItemsOfType(t){return this.log.filter(e=>e.action===t)}}class X{get enableLogging(){return this._enableLogging}set enableLogging(t){this._enableLogging!==t&&(this._enableLogging=t,this._enableLogging?(this.logger=new kt,this.logger.logToConsole=!0):this.logger=null)}constructor(t){this.uniqueId=F(),this._userVariables={},this._executionVariables={},this._globalContextVariables={},this._connectionValues={},this._pendingBlocks=[],this._executionId=0,this.onNodeExecutedObservable=new O,this.treatDataAsRightHanded=!1,this._enableLogging=!1,this._configuration=t,this.assetsContext=t.assetsContext??t.scene}hasVariable(t){return t in this._userVariables}setVariable(t,e){this._userVariables[t]=e,this.logger?.addLogItem({time:Date.now(),className:this.getClassName(),uniqueId:this.uniqueId,action:"ContextVariableSet",payload:{name:t,value:e}})}getAsset(t,e){return vt(this.assetsContext,t,e)}getVariable(t){return this.logger?.addLogItem({time:Date.now(),className:this.getClassName(),uniqueId:this.uniqueId,action:"ContextVariableGet",payload:{name:t,value:this._userVariables[t]}}),this._userVariables[t]}get userVariables(){return this._userVariables}getScene(){return this._configuration.scene}_getUniqueIdPrefixedName(t,e){return`${t.uniqueId}_${e}`}_getGlobalContextVariable(t,e){return this.logger?.addLogItem({time:Date.now(),className:this.getClassName(),uniqueId:this.uniqueId,action:"GlobalVariableGet",payload:{name:t,defaultValue:e,possibleValue:this._globalContextVariables[t]}}),this._hasGlobalContextVariable(t)?this._globalContextVariables[t]:e}_setGlobalContextVariable(t,e){this.logger?.addLogItem({time:Date.now(),className:this.getClassName(),uniqueId:this.uniqueId,action:"GlobalVariableSet",payload:{name:t,value:e}}),this._globalContextVariables[t]=e}_deleteGlobalContextVariable(t){this.logger?.addLogItem({time:Date.now(),className:this.getClassName(),uniqueId:this.uniqueId,action:"GlobalVariableDelete",payload:{name:t}}),delete this._globalContextVariables[t]}_hasGlobalContextVariable(t){return t in this._globalContextVariables}_setExecutionVariable(t,e,o){this._executionVariables[this._getUniqueIdPrefixedName(t,e)]=o}_getExecutionVariable(t,e,o){return this._hasExecutionVariable(t,e)?this._executionVariables[this._getUniqueIdPrefixedName(t,e)]:o}_deleteExecutionVariable(t,e){delete this._executionVariables[this._getUniqueIdPrefixedName(t,e)]}_hasExecutionVariable(t,e){return this._getUniqueIdPrefixedName(t,e)in this._executionVariables}_hasConnectionValue(t){return t.uniqueId in this._connectionValues}_setConnectionValue(t,e){this._connectionValues[t.uniqueId]=e,this.logger?.addLogItem({time:Date.now(),className:this.getClassName(),uniqueId:this.uniqueId,action:"SetConnectionValue",payload:{connectionPointId:t.uniqueId,value:e}})}_setConnectionValueByKey(t,e){this._connectionValues[t]=e}_getConnectionValue(t){return this.logger?.addLogItem({time:Date.now(),className:this.getClassName(),uniqueId:this.uniqueId,action:"GetConnectionValue",payload:{connectionPointId:t.uniqueId,value:this._connectionValues[t.uniqueId]}}),this._connectionValues[t.uniqueId]}get configuration(){return this._configuration}get hasPendingBlocks(){return this._pendingBlocks.length>0}_addPendingBlock(t){this._pendingBlocks.includes(t)||(this._pendingBlocks.push(t),this._pendingBlocks.sort((e,o)=>e.priority-o.priority))}_removePendingBlock(t){const e=this._pendingBlocks.indexOf(t);e!==-1&&this._pendingBlocks.splice(e,1)}_clearPendingBlocks(){for(const t of this._pendingBlocks)t._cancelPendingTasks(this);this._pendingBlocks.length=0}_notifyExecuteNode(t){this.onNodeExecutedObservable.notifyObservers(t),this.logger?.addLogItem({time:Date.now(),className:t.getClassName(),uniqueId:t.uniqueId,action:"ExecuteBlock"})}_notifyOnTick(t){this._setGlobalContextVariable("timeSinceStart",t.timeSinceStart),this._setGlobalContextVariable("deltaTime",t.deltaTime);for(const e of this._pendingBlocks)e._executeOnTick?.(this)}_increaseExecutionId(){this._executionId++}get executionId(){return this._executionId}serialize(t={},e=M){t.uniqueId=this.uniqueId,t._userVariables={};for(const o in this._userVariables)e(o,this._userVariables[o],t._userVariables);t._connectionValues={};for(const o in this._connectionValues)e(o,this._connectionValues[o],t._connectionValues);this.assetsContext!==this.getScene()&&(t._assetsContext={meshes:this.assetsContext.meshes.map(o=>o.id),materials:this.assetsContext.materials.map(o=>o.id),textures:this.assetsContext.textures.map(o=>o.name),animations:this.assetsContext.animations.map(o=>o.name),lights:this.assetsContext.lights.map(o=>o.id),cameras:this.assetsContext.cameras.map(o=>o.id),sounds:this.assetsContext.sounds?.map(o=>o.name),skeletons:this.assetsContext.skeletons.map(o=>o.id),particleSystems:this.assetsContext.particleSystems.map(o=>o.name),geometries:this.assetsContext.geometries.map(o=>o.id),multiMaterials:this.assetsContext.multiMaterials.map(o=>o.id),transformNodes:this.assetsContext.transformNodes.map(o=>o.id)})}getClassName(){return"FlowGraphContext"}}it([st()],X.prototype,"uniqueId",void 0);var W;(function(r){r[r.Input=0]="Input",r[r.Output=1]="Output"})(W||(W={}));class Y{constructor(t,e,o){this._ownerBlock=o,this._connectedPoint=[],this.uniqueId=F(),this.connectedPointIds=[],this.name=t,this._connectionType=e}get connectionType(){return this._connectionType}_isSingularConnection(){return!0}isConnected(){return this._connectedPoint.length>0}connectTo(t){if(this._connectionType===t._connectionType)throw new Error(`Cannot connect two points of type ${this.connectionType}`);if(this._isSingularConnection()&&this._connectedPoint.length>0||t._isSingularConnection()&&t._connectedPoint.length>0)throw new Error("Max number of connections for point reached");this._connectedPoint.push(t),t._connectedPoint.push(this)}disconnectFrom(t,e=!0){const o=this._connectedPoint.indexOf(t),a=t._connectedPoint.indexOf(this);o===-1||a===-1||(e&&this._connectedPoint.splice(o,1),t._connectedPoint.splice(a,1))}disconnectFromAll(){for(const t of this._connectedPoint)this.disconnectFrom(t,!1);this._connectedPoint.length=0}dispose(){for(const t of this._connectedPoint)this.disconnectFrom(t)}serialize(t={}){t.uniqueId=this.uniqueId,t.name=this.name,t._connectionType=this._connectionType,t.connectedPointIds=[],t.className=this.getClassName();for(const e of this._connectedPoint)t.connectedPointIds.push(e.uniqueId)}getClassName(){return"FGConnection"}deserialize(t){this.uniqueId=t.uniqueId,this.name=t.name,this._connectionType=t._connectionType,this.connectedPointIds=t.connectedPointIds}}class L extends Y{constructor(t,e,o,a,i=a.defaultValue,s=!1){super(t,e,o),this.richType=a,this._defaultValue=i,this._optional=s,this._isDisabled=!1,this._lastValue=null,this.dataTransformer=null,this.onValueChangedObservable=new O}get optional(){return this._optional}get isDisabled(){return this._isDisabled}set isDisabled(t){this._isDisabled!==t&&(this._isDisabled=t,this._isDisabled&&this.disconnectFromAll())}_isSingularConnection(){return this.connectionType===0}setValue(t,e){e._getConnectionValue(this)!==t&&(e._setConnectionValue(this,t),this.onValueChangedObservable.notifyObservers(t))}resetToDefaultValue(t){t._setConnectionValue(this,this._defaultValue)}connectTo(t){this._isDisabled||super.connectTo(t)}_getValueOrDefault(t){const e=t._getConnectionValue(this)??this._defaultValue;return this.dataTransformer?this.dataTransformer(e):e}getValue(t){if(this.connectionType===1){t._notifyExecuteNode(this._ownerBlock),this._ownerBlock._updateOutputs(t);const o=this._getValueOrDefault(t);return this._lastValue=o,this.richType.typeTransformer?this.richType.typeTransformer(o):o}const e=this.isConnected()?this._connectedPoint[0].getValue(t):this._getValueOrDefault(t);return this._lastValue=e,this.richType.typeTransformer?this.richType.typeTransformer(e):e}_getLastValue(){return this._lastValue}getClassName(){return"FlowGraphDataConnection"}serialize(t={}){super.serialize(t),t.richType={},this.richType.serialize(t.richType),t.optional=this._optional,M("defaultValue",this._defaultValue,t)}}J("FlowGraphDataConnection",L);class Gt{constructor(t){this.config=t,this.uniqueId=F(),this.name=this.config?.name??this.getClassName(),this.dataInputs=[],this.dataOutputs=[]}_updateOutputs(t){}registerDataInput(t,e,o){const a=new L(t,0,this,e,o);return this.dataInputs.push(a),a}registerDataOutput(t,e,o){const a=new L(t,1,this,e,o);return this.dataOutputs.push(a),a}getDataInput(t){return this.dataInputs.find(e=>e.name===t)}getDataOutput(t){return this.dataOutputs.find(e=>e.name===t)}serialize(t={},e=M){if(t.uniqueId=this.uniqueId,t.config={},this.config){const o=this.config;Object.keys(this.config).forEach(a=>{e(a,o[a],t.config)})}t.dataInputs=[],t.dataOutputs=[],t.className=this.getClassName();for(const o of this.dataInputs){const a={};o.serialize(a),t.dataInputs.push(a)}for(const o of this.dataOutputs){const a={};o.serialize(a),t.dataOutputs.push(a)}}deserialize(t){}_log(t,e,o){t.logger?.addLogItem({action:e,payload:o,className:this.getClassName(),uniqueId:this.uniqueId})}getClassName(){return"FlowGraphBlock"}}class N extends Y{constructor(){super(...arguments),this.priority=0}_isSingularConnection(){return!1}connectTo(t){super.connectTo(t),this._connectedPoint.sort((e,o)=>o.priority-e.priority)}_activateSignal(t){if(t.logger?.addLogItem({action:"ActivateSignal",className:this._ownerBlock.getClassName(),uniqueId:this._ownerBlock.uniqueId,payload:{connectionType:this.connectionType,name:this.name}}),this.connectionType===0)t._notifyExecuteNode(this._ownerBlock),this._ownerBlock._execute(t,this),t._increaseExecutionId();else for(const e of this._connectedPoint)e._activateSignal(t)}}J("FlowGraphSignalConnection",N);class D extends Gt{constructor(t){super(t),this.priority=0,this.signalInputs=[],this.signalOutputs=[],this.in=this._registerSignalInput("in"),this.error=this._registerSignalOutput("error")}_registerSignalInput(t){const e=new N(t,0,this);return this.signalInputs.push(e),e}_registerSignalOutput(t){const e=new N(t,1,this);return this.signalOutputs.push(e),e}_unregisterSignalInput(t){const e=this.signalInputs.findIndex(o=>o.name===t);e!==-1&&(this.signalInputs[e].dispose(),this.signalInputs.splice(e,1))}_unregisterSignalOutput(t){const e=this.signalOutputs.findIndex(o=>o.name===t);e!==-1&&(this.signalOutputs[e].dispose(),this.signalOutputs.splice(e,1))}_reportError(t,e){this.error.payload=typeof e=="string"?new Error(e):e,this.error._activateSignal(t)}getSignalInput(t){return this.signalInputs.find(e=>e.name===t)}getSignalOutput(t){return this.signalOutputs.find(e=>e.name===t)}serialize(t={}){super.serialize(t),t.signalInputs=[],t.signalOutputs=[];for(const e of this.signalInputs){const o={};e.serialize(o),t.signalInputs.push(o)}for(const e of this.signalOutputs){const o={};e.serialize(o),t.signalOutputs.push(o)}}deserialize(t){for(let e=0;e<t.signalInputs.length;e++){const o=this.getSignalInput(t.signalInputs[e].name);if(o)o.deserialize(t.signalInputs[e]);else throw new Error("Could not find signal input with name "+t.signalInputs[e].name+" in block "+t.className)}for(let e=0;e<t.signalOutputs.length;e++){const o=this.getSignalOutput(t.signalOutputs[e].name);if(o)o.deserialize(t.signalOutputs[e]);else throw new Error("Could not find signal output with name "+t.signalOutputs[e].name+" in block "+t.className)}}getClassName(){return"FlowGraphExecutionBlock"}}class Bt{constructor(t){this.onEventTriggeredObservable=new O,this.sceneReadyTriggered=!1,this._pointerUnderMeshState={},this._startingTime=0,this._scene=t,this._initialize()}_initialize(){this._sceneReadyObserver=this._scene.onReadyObservable.add(()=>{this.sceneReadyTriggered||(this.onEventTriggeredObservable.notifyObservers({type:"SceneReady"}),this.sceneReadyTriggered=!0)}),this._sceneDisposeObserver=this._scene.onDisposeObservable.add(()=>{this.onEventTriggeredObservable.notifyObservers({type:"SceneDispose"})}),this._sceneOnBeforeRenderObserver=this._scene.onBeforeRenderObservable.add(()=>{const t=this._scene.getEngine().getDeltaTime()/1e3;this.onEventTriggeredObservable.notifyObservers({type:"SceneBeforeRender",payload:{timeSinceStart:this._startingTime,deltaTime:t}}),this._startingTime+=t}),this._meshPickedObserver=this._scene.onPointerObservable.add(t=>{this.onEventTriggeredObservable.notifyObservers({type:"MeshPick",payload:t})},q.POINTERPICK),this._meshUnderPointerObserver=this._scene.onMeshUnderPointerUpdatedObservable.add(t=>{const e=t.pointerId,o=t.mesh,a=this._pointerUnderMeshState[e];!a&&o?this.onEventTriggeredObservable.notifyObservers({type:"PointerOver",payload:{pointerId:e,mesh:o}}):a&&!o?this.onEventTriggeredObservable.notifyObservers({type:"PointerOut",payload:{pointerId:e,mesh:a}}):a&&o&&a!==o&&(this.onEventTriggeredObservable.notifyObservers({type:"PointerOut",payload:{pointerId:e,mesh:a,over:o}}),this.onEventTriggeredObservable.notifyObservers({type:"PointerOver",payload:{pointerId:e,mesh:o,out:a}})),this._pointerUnderMeshState[e]=o},q.POINTERMOVE)}dispose(){this._sceneDisposeObserver?.remove(),this._sceneReadyObserver?.remove(),this._sceneOnBeforeRenderObserver?.remove(),this._meshPickedObserver?.remove(),this._meshUnderPointerObserver?.remove(),this.onEventTriggeredObservable.clear()}}function z(r,t){return!!(r.parent&&(r.parent===t||z(r.parent,t)))}function Ut(r){if(r.getClassName)return r.getClassName()}function Wt(r,t){return r===t&&(r==="Vector2"||r==="Vector3"||r==="Vector4")}function Ht(r,t){return r===t&&(r==="Matrix"||r==="Matrix2D"||r==="Matrix3D")}function Qt(r,t){return r==="FlowGraphInteger"&&t==="FlowGraphInteger"}function Jt(r,t){const e=typeof r=="number"||typeof r?.value=="number";return e&&!t?!isNaN(It(r)):e}function It(r){return typeof r=="number"?r:r.value}var H;(function(r){r[r.Stopped=0]="Stopped",r[r.Started=1]="Started"})(H||(H={}));class Ft{get state(){return this._state}set state(t){this._state=t,this.onStateChangedObservable.notifyObservers(t)}constructor(t){this.onStateChangedObservable=new O,this._eventBlocks={SceneReady:[],SceneDispose:[],SceneBeforeRender:[],MeshPick:[],PointerDown:[],PointerUp:[],PointerMove:[],PointerOver:[],PointerOut:[],SceneAfterRender:[],NoTrigger:[]},this._executionContexts=[],this._state=0,this._scene=t.scene,this._sceneEventCoordinator=new Bt(this._scene),this._coordinator=t.coordinator,this._eventObserver=this._sceneEventCoordinator.onEventTriggeredObservable.add(e=>{for(const o of this._executionContexts){const a=this._getContextualOrder(e.type,o);for(const i of a)if(!i._executeEvent(o,e.payload))break}switch(e.type){case"SceneReady":this._sceneEventCoordinator.sceneReadyTriggered=!0;break;case"SceneBeforeRender":for(const o of this._executionContexts)o._notifyOnTick(e.payload);break;case"SceneDispose":this.dispose();break}})}createContext(){const t=new X({scene:this._scene,coordinator:this._coordinator});return this._executionContexts.push(t),t}getContext(t){return this._executionContexts[t]}addEventBlock(t){if((t.type==="PointerOver"||t.type==="PointerOut")&&(this._scene.constantlyUpdateMeshUnderPointer=!0),t.type!=="NoTrigger"&&this._eventBlocks[t.type].push(t),this.state===1)for(const e of this._executionContexts)t._startPendingTasks(e);else this.onStateChangedObservable.addOnce(e=>{if(e===1)for(const o of this._executionContexts)t._startPendingTasks(o)})}start(){this.state!==1&&(this._executionContexts.length===0&&this.createContext(),this.onStateChangedObservable.add(t=>{t===1&&(this._startPendingEvents(),this._scene.isReady(!0)&&this._sceneEventCoordinator.onEventTriggeredObservable.notifyObservers({type:"SceneReady"}))}),this.state=1)}_startPendingEvents(){for(const t of this._executionContexts)for(const e in this._eventBlocks){const o=this._getContextualOrder(e,t);for(const a of o)a._startPendingTasks(t)}}_getContextualOrder(t,e){const o=this._eventBlocks[t].sort((a,i)=>i.initPriority-a.initPriority);if(t==="MeshPick"){const a=[];for(const i of o){const s=i.asset.getValue(e);let c=0;for(;c<o.length;c++){const _=o[c].asset.getValue(e);if(s&&_&&z(s,_))break}a.splice(c,0,i)}return a}return o}dispose(){if(this.state!==0){this.state=0;for(const t of this._executionContexts)t._clearPendingBlocks();this._executionContexts.length=0;for(const t in this._eventBlocks)this._eventBlocks[t].length=0;this._eventObserver?.remove(),this._sceneEventCoordinator.dispose()}}visitAllBlocks(t){const e=[],o=new Set;for(const a in this._eventBlocks)for(const i of this._eventBlocks[a])e.push(i),o.add(i.uniqueId);for(;e.length>0;){const a=e.pop();t(a);for(const i of a.dataInputs)for(const s of i._connectedPoint)o.has(s._ownerBlock.uniqueId)||(e.push(s._ownerBlock),o.add(s._ownerBlock.uniqueId));if(a instanceof D)for(const i of a.signalOutputs)for(const s of i._connectedPoint)o.has(s._ownerBlock.uniqueId)||(e.push(s._ownerBlock),o.add(s._ownerBlock.uniqueId))}}serialize(t={},e){t.allBlocks=[],this.visitAllBlocks(o=>{const a={};o.serialize(a),t.allBlocks.push(a)}),t.executionContexts=[];for(const o of this._executionContexts){const a={};o.serialize(a,e),t.executionContexts.push(a)}}}class E{constructor(t){this.config=t,this.dispatchEventsSynchronously=!0,this._flowGraphs=[],this._customEventsMap=new Map,this._eventExecutionCounter=new Map,this._executeOnNextFrame=[],this._eventUniqueId=0,this._disposeObserver=this.config.scene.onDisposeObservable.add(()=>{this.dispose()}),this._onBeforeRenderObserver=this.config.scene.onBeforeRenderObservable.add(()=>{this._eventExecutionCounter.clear();const o=this._executeOnNextFrame.slice(0);o.length&&o.forEach(a=>{this.notifyCustomEvent(a.id,a.data,!1);const i=this._executeOnNextFrame.findIndex(s=>s.uniqueId===a.uniqueId);i!==-1&&this._executeOnNextFrame.splice(i,1)})}),(E.SceneCoordinators.get(this.config.scene)??[]).push(this)}createGraph(){const t=new Ft({scene:this.config.scene,coordinator:this});return this._flowGraphs.push(t),t}removeGraph(t){const e=this._flowGraphs.indexOf(t);e!==-1&&(t.dispose(),this._flowGraphs.splice(e,1))}start(){this._flowGraphs.forEach(t=>t.start())}dispose(){this._flowGraphs.forEach(o=>o.dispose()),this._flowGraphs.length=0,this._disposeObserver?.remove(),this._onBeforeRenderObserver?.remove();const t=E.SceneCoordinators.get(this.config.scene)??[],e=t.indexOf(this);e!==-1&&t.splice(e,1)}serialize(t,e){t._flowGraphs=[],this._flowGraphs.forEach(o=>{const a={};o.serialize(a,e),t._flowGraphs.push(a)}),t.dispatchEventsSynchronously=this.dispatchEventsSynchronously}get flowGraphs(){return this._flowGraphs}getCustomEventObservable(t){let e=this._customEventsMap.get(t);return e||(e=new O,this._customEventsMap.set(t,e)),e}notifyCustomEvent(t,e,o=!this.dispatchEventsSynchronously){if(o){this._executeOnNextFrame.push({id:t,data:e,uniqueId:this._eventUniqueId++});return}if(this._eventExecutionCounter.has(t)){const i=this._eventExecutionCounter.get(t);if(this._eventExecutionCounter.set(t,i+1),i>=E.MaxEventTypeExecutionPerFrame){i===E.MaxEventTypeExecutionPerFrame&&p.Warn(`FlowGraphCoordinator: Too many executions of event "${t}".`);return}}else this._eventExecutionCounter.set(t,1);const a=this._customEventsMap.get(t);a&&a.notifyObservers(e)}}E.MaxEventsPerType=30;E.MaxEventTypeExecutionPerFrame=30;E.SceneCoordinators=new Map;const S={};function Tt(r,t,e){S[`${r}/${t}`]=e}function Vt(r){switch(r){case"FlowGraphPlayAnimationBlock":return async()=>(await n(()=>import("./flowGraphPlayAnimationBlock-DQh66smU.js"),__vite__mapDeps([0,1,2,3,4,5,6,7]),import.meta.url)).FlowGraphPlayAnimationBlock;case"FlowGraphStopAnimationBlock":return async()=>(await n(()=>import("./flowGraphStopAnimationBlock-Ad8vc_ad.js"),__vite__mapDeps([8,1,2,3,4,7]),import.meta.url)).FlowGraphStopAnimationBlock;case"FlowGraphPauseAnimationBlock":return async()=>(await n(()=>import("./flowGraphPauseAnimationBlock-DEDB__5r.js"),__vite__mapDeps([9,1,2,3,4,7]),import.meta.url)).FlowGraphPauseAnimationBlock;case"FlowGraphInterpolationBlock":return async()=>(await n(()=>import("./flowGraphInterpolationBlock-BqJW2qFy.js"),__vite__mapDeps([10,1,2,3,4,7]),import.meta.url)).FlowGraphInterpolationBlock;case"FlowGraphSceneReadyEventBlock":return async()=>(await n(()=>import("./flowGraphSceneReadyEventBlock-Cs83muND.js"),__vite__mapDeps([11,2,3,4,1,7]),import.meta.url)).FlowGraphSceneReadyEventBlock;case"FlowGraphSceneTickEventBlock":return async()=>(await n(()=>import("./flowGraphSceneTickEventBlock-C0G_eXum.js"),__vite__mapDeps([12,2,3,4,1,7]),import.meta.url)).FlowGraphSceneTickEventBlock;case"FlowGraphSendCustomEventBlock":return async()=>(await n(()=>import("./flowGraphSendCustomEventBlock-DfkJ-s1L.js"),__vite__mapDeps([13,2,3,4,1,7]),import.meta.url)).FlowGraphSendCustomEventBlock;case"FlowGraphReceiveCustomEventBlock":return async()=>(await n(()=>import("./flowGraphReceiveCustomEventBlock-BNMGCFYB.js"),__vite__mapDeps([14,2,3,4,1,7]),import.meta.url)).FlowGraphReceiveCustomEventBlock;case"FlowGraphMeshPickEventBlock":return async()=>(await n(()=>import("./flowGraphMeshPickEventBlock-CvXmR7ch.js"),__vite__mapDeps([15,2,3,4,1,7]),import.meta.url)).FlowGraphMeshPickEventBlock;case"FlowGraphEBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphEBlock;case"FlowGraphPIBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphPiBlock;case"FlowGraphInfBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphInfBlock;case"FlowGraphNaNBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphNaNBlock;case"FlowGraphRandomBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphRandomBlock;case"FlowGraphAddBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphAddBlock;case"FlowGraphSubtractBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphSubtractBlock;case"FlowGraphMultiplyBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphMultiplyBlock;case"FlowGraphDivideBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphDivideBlock;case"FlowGraphAbsBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphAbsBlock;case"FlowGraphSignBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphSignBlock;case"FlowGraphTruncBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphTruncBlock;case"FlowGraphFloorBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphFloorBlock;case"FlowGraphCeilBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphCeilBlock;case"FlowGraphRoundBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphRoundBlock;case"FlowGraphFractBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphFractionBlock;case"FlowGraphNegationBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphNegationBlock;case"FlowGraphModuloBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphModuloBlock;case"FlowGraphMinBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphMinBlock;case"FlowGraphMaxBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphMaxBlock;case"FlowGraphClampBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphClampBlock;case"FlowGraphSaturateBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphSaturateBlock;case"FlowGraphMathInterpolationBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphMathInterpolationBlock;case"FlowGraphEqualityBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphEqualityBlock;case"FlowGraphLessThanBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphLessThanBlock;case"FlowGraphLessThanOrEqualBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphLessThanOrEqualBlock;case"FlowGraphGreaterThanBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphGreaterThanBlock;case"FlowGraphGreaterThanOrEqualBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphGreaterThanOrEqualBlock;case"FlowGraphIsNaNBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphIsNanBlock;case"FlowGraphIsInfBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphIsInfinityBlock;case"FlowGraphDegToRadBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphDegToRadBlock;case"FlowGraphRadToDegBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphRadToDegBlock;case"FlowGraphSinBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphSinBlock;case"FlowGraphCosBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphCosBlock;case"FlowGraphTanBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphTanBlock;case"FlowGraphASinBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphAsinBlock;case"FlowGraphACosBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphAcosBlock;case"FlowGraphATanBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphAtanBlock;case"FlowGraphATan2Block":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphAtan2Block;case"FlowGraphSinhBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphSinhBlock;case"FlowGraphCoshBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphCoshBlock;case"FlowGraphTanhBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphTanhBlock;case"FlowGraphASinhBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphAsinhBlock;case"FlowGraphACoshBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphAcoshBlock;case"FlowGraphATanhBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphAtanhBlock;case"FlowGraphExponentialBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphExpBlock;case"FlowGraphLogBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphLogBlock;case"FlowGraphLog2Block":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphLog2Block;case"FlowGraphLog10Block":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphLog10Block;case"FlowGraphSquareRootBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphSquareRootBlock;case"FlowGraphPowerBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphPowerBlock;case"FlowGraphCubeRootBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphCubeRootBlock;case"FlowGraphBitwiseAndBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphBitwiseAndBlock;case"FlowGraphBitwiseOrBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphBitwiseOrBlock;case"FlowGraphBitwiseNotBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphBitwiseNotBlock;case"FlowGraphBitwiseXorBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphBitwiseXorBlock;case"FlowGraphBitwiseLeftShiftBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphBitwiseLeftShiftBlock;case"FlowGraphBitwiseRightShiftBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphBitwiseRightShiftBlock;case"FlowGraphLengthBlock":return async()=>(await n(()=>import("./flowGraphVectorMathBlocks-M_wfn49G.js"),__vite__mapDeps([21,1,2,3,4,17,18,19,20,7]),import.meta.url)).FlowGraphLengthBlock;case"FlowGraphNormalizeBlock":return async()=>(await n(()=>import("./flowGraphVectorMathBlocks-M_wfn49G.js"),__vite__mapDeps([21,1,2,3,4,17,18,19,20,7]),import.meta.url)).FlowGraphNormalizeBlock;case"FlowGraphDotBlock":return async()=>(await n(()=>import("./flowGraphVectorMathBlocks-M_wfn49G.js"),__vite__mapDeps([21,1,2,3,4,17,18,19,20,7]),import.meta.url)).FlowGraphDotBlock;case"FlowGraphCrossBlock":return async()=>(await n(()=>import("./flowGraphVectorMathBlocks-M_wfn49G.js"),__vite__mapDeps([21,1,2,3,4,17,18,19,20,7]),import.meta.url)).FlowGraphCrossBlock;case"FlowGraphRotate2DBlock":return async()=>(await n(()=>import("./flowGraphVectorMathBlocks-M_wfn49G.js"),__vite__mapDeps([21,1,2,3,4,17,18,19,20,7]),import.meta.url)).FlowGraphRotate2DBlock;case"FlowGraphRotate3DBlock":return async()=>(await n(()=>import("./flowGraphVectorMathBlocks-M_wfn49G.js"),__vite__mapDeps([21,1,2,3,4,17,18,19,20,7]),import.meta.url)).FlowGraphRotate3DBlock;case"FlowGraphTransposeBlock":return async()=>(await n(()=>import("./flowGraphMatrixMathBlocks-Bixr0sb_.js"),__vite__mapDeps([22,1,2,3,4,19,18,17,7]),import.meta.url)).FlowGraphTransposeBlock;case"FlowGraphDeterminantBlock":return async()=>(await n(()=>import("./flowGraphMatrixMathBlocks-Bixr0sb_.js"),__vite__mapDeps([22,1,2,3,4,19,18,17,7]),import.meta.url)).FlowGraphDeterminantBlock;case"FlowGraphInvertMatrixBlock":return async()=>(await n(()=>import("./flowGraphMatrixMathBlocks-Bixr0sb_.js"),__vite__mapDeps([22,1,2,3,4,19,18,17,7]),import.meta.url)).FlowGraphInvertMatrixBlock;case"FlowGraphMatrixMultiplicationBlock":return async()=>(await n(()=>import("./flowGraphMatrixMathBlocks-Bixr0sb_.js"),__vite__mapDeps([22,1,2,3,4,19,18,17,7]),import.meta.url)).FlowGraphMatrixMultiplicationBlock;case"FlowGraphBranchBlock":return async()=>(await n(()=>import("./flowGraphBranchBlock-Cmz3HUH_.js"),__vite__mapDeps([23,1,2,3,4,7]),import.meta.url)).FlowGraphBranchBlock;case"FlowGraphSetDelayBlock":return async()=>(await n(()=>import("./flowGraphSetDelayBlock-DsrOvLHy.js"),__vite__mapDeps([24,1,2,3,4,7]),import.meta.url)).FlowGraphSetDelayBlock;case"FlowGraphCancelDelayBlock":return async()=>(await n(()=>import("./flowGraphCancelDelayBlock-DR3yfbrj.js"),__vite__mapDeps([25,2,3,4,1,7]),import.meta.url)).FlowGraphCancelDelayBlock;case"FlowGraphCallCounterBlock":return async()=>(await n(()=>import("./flowGraphCounterBlock-BazS5lEJ.js"),__vite__mapDeps([26,1,2,3,4,7]),import.meta.url)).FlowGraphCallCounterBlock;case"FlowGraphDebounceBlock":return async()=>(await n(()=>import("./flowGraphDebounceBlock-DEdqoOLr.js"),__vite__mapDeps([27,1,2,3,4,7]),import.meta.url)).FlowGraphDebounceBlock;case"FlowGraphThrottleBlock":return async()=>(await n(()=>import("./flowGraphThrottleBlock-D2tWef1Y.js"),__vite__mapDeps([28,1,2,3,4,7]),import.meta.url)).FlowGraphThrottleBlock;case"FlowGraphDoNBlock":return async()=>(await n(()=>import("./flowGraphDoNBlock-DXcMjbAJ.js"),__vite__mapDeps([29,1,2,3,4,7]),import.meta.url)).FlowGraphDoNBlock;case"FlowGraphFlipFlopBlock":return async()=>(await n(()=>import("./flowGraphFlipFlopBlock-D-QhtGXk.js"),__vite__mapDeps([30,1,2,3,4,7]),import.meta.url)).FlowGraphFlipFlopBlock;case"FlowGraphForLoopBlock":return async()=>(await n(()=>import("./flowGraphForLoopBlock-9LTiiNg-.js"),__vite__mapDeps([31,1,2,3,4,7]),import.meta.url)).FlowGraphForLoopBlock;case"FlowGraphMultiGateBlock":return async()=>(await n(()=>import("./flowGraphMultiGateBlock-jaDVjzzS.js"),__vite__mapDeps([32,2,3,4,1,7]),import.meta.url)).FlowGraphMultiGateBlock;case"FlowGraphSequenceBlock":return async()=>(await n(()=>import("./flowGraphSequenceBlock-Cr5WTdwC.js"),__vite__mapDeps([33,2,3,4,1,7]),import.meta.url)).FlowGraphSequenceBlock;case"FlowGraphSwitchBlock":return async()=>(await n(()=>import("./flowGraphSwitchBlock-C4FBt2Pq.js"),__vite__mapDeps([34,1,2,3,4,7]),import.meta.url)).FlowGraphSwitchBlock;case"FlowGraphWaitAllBlock":return async()=>(await n(()=>import("./flowGraphWaitAllBlock-daeh0TjD.js"),__vite__mapDeps([35,2,3,4,1,7]),import.meta.url)).FlowGraphWaitAllBlock;case"FlowGraphWhileLoopBlock":return async()=>(await n(()=>import("./flowGraphWhileLoopBlock-CKjqOxbf.js"),__vite__mapDeps([36,1,2,3,4,7]),import.meta.url)).FlowGraphWhileLoopBlock;case"FlowGraphConsoleLogBlock":return async()=>(await n(()=>import("./flowGraphConsoleLogBlock-CySDeX0T.js"),__vite__mapDeps([37,1,2,3,4,7]),import.meta.url)).FlowGraphConsoleLogBlock;case"FlowGraphConditionalBlock":return async()=>(await n(()=>import("./flowGraphConditionalDataBlock-CQ_HDF-s.js"),__vite__mapDeps([38,1,2,3,4,7]),import.meta.url)).FlowGraphConditionalDataBlock;case"FlowGraphConstantBlock":return async()=>(await n(()=>import("./flowGraphConstantBlock-D1Zk6TpZ.js"),__vite__mapDeps([39,1,2,3,4,7]),import.meta.url)).FlowGraphConstantBlock;case"FlowGraphTransformCoordinatesSystemBlock":return async()=>(await n(()=>import("./flowGraphTransformCoordinatesSystemBlock-DVpbFbxT.js"),__vite__mapDeps([40,1,2,3,4,7]),import.meta.url)).FlowGraphTransformCoordinatesSystemBlock;case"FlowGraphGetAssetBlock":return async()=>(await n(()=>import("./flowGraphGetAssetBlock-C3i8osTa.js"),__vite__mapDeps([41,1,2,3,4,7]),import.meta.url)).FlowGraphGetAssetBlock;case"FlowGraphGetPropertyBlock":return async()=>(await n(()=>import("./flowGraphGetPropertyBlock-D0IaRwxo.js"),__vite__mapDeps([42,1,2,3,4,18,7]),import.meta.url)).FlowGraphGetPropertyBlock;case"FlowGraphSetPropertyBlock":return async()=>(await n(()=>import("./flowGraphSetPropertyBlock-BwaB_U6k.js"),__vite__mapDeps([43,1,2,3,4,7]),import.meta.url)).FlowGraphSetPropertyBlock;case"FlowGraphGetVariableBlock":return async()=>(await n(()=>import("./flowGraphGetVariableBlock-BAu4mxNU.js"),__vite__mapDeps([44,1,2,3,4,7]),import.meta.url)).FlowGraphGetVariableBlock;case"FlowGraphSetVariableBlock":return async()=>(await n(()=>import("./flowGraphSetVariableBlock-BSyFUWPH.js"),__vite__mapDeps([45,2,3,4,1,7]),import.meta.url)).FlowGraphSetVariableBlock;case"FlowGraphJsonPointerParserBlock":return async()=>(await n(()=>import("./flowGraphJsonPointerParserBlock-DqXDIgg0.js"),__vite__mapDeps([46,1,2,3,4,18,7]),import.meta.url)).FlowGraphJsonPointerParserBlock;case"FlowGraphLeadingZerosBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphLeadingZerosBlock;case"FlowGraphTrailingZerosBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphTrailingZerosBlock;case"FlowGraphOneBitsCounterBlock":return async()=>(await n(()=>import("./flowGraphMathBlocks-BzIhuErF.js"),__vite__mapDeps([16,2,3,4,1,17,18,19,20,7]),import.meta.url)).FlowGraphOneBitsCounterBlock;case"FlowGraphCombineVector2Block":return async()=>(await n(()=>import("./flowGraphMathCombineExtractBlocks-BIFp8oDY.js"),__vite__mapDeps([47,18,1,2,3,4,7]),import.meta.url)).FlowGraphCombineVector2Block;case"FlowGraphCombineVector3Block":return async()=>(await n(()=>import("./flowGraphMathCombineExtractBlocks-BIFp8oDY.js"),__vite__mapDeps([47,18,1,2,3,4,7]),import.meta.url)).FlowGraphCombineVector3Block;case"FlowGraphCombineVector4Block":return async()=>(await n(()=>import("./flowGraphMathCombineExtractBlocks-BIFp8oDY.js"),__vite__mapDeps([47,18,1,2,3,4,7]),import.meta.url)).FlowGraphCombineVector4Block;case"FlowGraphCombineMatrixBlock":return async()=>(await n(()=>import("./flowGraphMathCombineExtractBlocks-BIFp8oDY.js"),__vite__mapDeps([47,18,1,2,3,4,7]),import.meta.url)).FlowGraphCombineMatrixBlock;case"FlowGraphExtractVector2Block":return async()=>(await n(()=>import("./flowGraphMathCombineExtractBlocks-BIFp8oDY.js"),__vite__mapDeps([47,18,1,2,3,4,7]),import.meta.url)).FlowGraphExtractVector2Block;case"FlowGraphExtractVector3Block":return async()=>(await n(()=>import("./flowGraphMathCombineExtractBlocks-BIFp8oDY.js"),__vite__mapDeps([47,18,1,2,3,4,7]),import.meta.url)).FlowGraphExtractVector3Block;case"FlowGraphExtractVector4Block":return async()=>(await n(()=>import("./flowGraphMathCombineExtractBlocks-BIFp8oDY.js"),__vite__mapDeps([47,18,1,2,3,4,7]),import.meta.url)).FlowGraphExtractVector4Block;case"FlowGraphExtractMatrixBlock":return async()=>(await n(()=>import("./flowGraphMathCombineExtractBlocks-BIFp8oDY.js"),__vite__mapDeps([47,18,1,2,3,4,7]),import.meta.url)).FlowGraphExtractMatrixBlock;case"FlowGraphTransformVectorBlock":return async()=>(await n(()=>import("./flowGraphVectorMathBlocks-M_wfn49G.js"),__vite__mapDeps([21,1,2,3,4,17,18,19,20,7]),import.meta.url)).FlowGraphTransformBlock;case"FlowGraphTransformCoordinatesBlock":return async()=>(await n(()=>import("./flowGraphVectorMathBlocks-M_wfn49G.js"),__vite__mapDeps([21,1,2,3,4,17,18,19,20,7]),import.meta.url)).FlowGraphTransformCoordinatesBlock;case"FlowGraphMatrixDecompose":return async()=>(await n(()=>import("./flowGraphMatrixMathBlocks-Bixr0sb_.js"),__vite__mapDeps([22,1,2,3,4,19,18,17,7]),import.meta.url)).FlowGraphMatrixDecomposeBlock;case"FlowGraphMatrixCompose":return async()=>(await n(()=>import("./flowGraphMatrixMathBlocks-Bixr0sb_.js"),__vite__mapDeps([22,1,2,3,4,19,18,17,7]),import.meta.url)).FlowGraphMatrixComposeBlock;case"FlowGraphBooleanToFloat":return async()=>(await n(()=>import("./flowGraphTypeToTypeBlocks-UXTyliE6.js"),__vite__mapDeps([48,19,18,1,2,3,4,7]),import.meta.url)).FlowGraphBooleanToFloat;case"FlowGraphBooleanToInt":return async()=>(await n(()=>import("./flowGraphTypeToTypeBlocks-UXTyliE6.js"),__vite__mapDeps([48,19,18,1,2,3,4,7]),import.meta.url)).FlowGraphBooleanToInt;case"FlowGraphFloatToBoolean":return async()=>(await n(()=>import("./flowGraphTypeToTypeBlocks-UXTyliE6.js"),__vite__mapDeps([48,19,18,1,2,3,4,7]),import.meta.url)).FlowGraphFloatToBoolean;case"FlowGraphIntToBoolean":return async()=>(await n(()=>import("./flowGraphTypeToTypeBlocks-UXTyliE6.js"),__vite__mapDeps([48,19,18,1,2,3,4,7]),import.meta.url)).FlowGraphIntToBoolean;case"FlowGraphIntToFloat":return async()=>(await n(()=>import("./flowGraphTypeToTypeBlocks-UXTyliE6.js"),__vite__mapDeps([48,19,18,1,2,3,4,7]),import.meta.url)).FlowGraphIntToFloat;case"FlowGraphFloatToInt":return async()=>(await n(()=>import("./flowGraphTypeToTypeBlocks-UXTyliE6.js"),__vite__mapDeps([48,19,18,1,2,3,4,7]),import.meta.url)).FlowGraphFloatToInt;case"FlowGraphEasingBlock":return async()=>(await n(()=>import("./flowGraphEasingBlock-BnT2uZ12.js"),__vite__mapDeps([49,2,3,4,1,7]),import.meta.url)).FlowGraphEasingBlock;case"FlowGraphBezierCurveEasing":return async()=>(await n(()=>import("./flowGraphBezierCurveEasingBlock-DGAdV_J2.js"),__vite__mapDeps([50,2,3,4,1,7]),import.meta.url)).FlowGraphBezierCurveEasingBlock;case"FlowGraphPointerOverEventBlock":return async()=>(await n(()=>import("./flowGraphPointerOverEventBlock-DxP_c5QG.js"),__vite__mapDeps([51,1,2,3,4,7]),import.meta.url)).FlowGraphPointerOverEventBlock;case"FlowGraphPointerOutEventBlock":return async()=>(await n(()=>import("./flowGraphPointerOutEventBlock-BWnyltcF.js"),__vite__mapDeps([52,1,2,3,4,7]),import.meta.url)).FlowGraphPointerOutEventBlock;case"FlowGraphContextBlock":return async()=>(await n(()=>import("./flowGraphContextBlock-DRZMpl2V.js"),__vite__mapDeps([53,1,2,3,4,7]),import.meta.url)).FlowGraphContextBlock;case"FlowGraphArrayIndexBlock":return async()=>(await n(()=>import("./flowGraphArrayIndexBlock-Cky7vvd2.js"),__vite__mapDeps([54,1,2,3,4,7]),import.meta.url)).FlowGraphArrayIndexBlock;case"FlowGraphCodeExecutionBlock":return async()=>(await n(()=>import("./flowGraphCodeExecutionBlock-B5ccdBOn.js"),__vite__mapDeps([55,1,2,3,4,7]),import.meta.url)).FlowGraphCodeExecutionBlock;case"FlowGraphIndexOfBlock":return async()=>(await n(()=>import("./flowGraphIndexOfBlock-6T3vKTlj.js"),__vite__mapDeps([56,1,2,3,4,7]),import.meta.url)).FlowGraphIndexOfBlock;case"FlowGraphFunctionReference":return async()=>(await n(()=>import("./flowGraphFunctionReferenceBlock-BH8LN4BJ.js"),__vite__mapDeps([57,1,2,3,4,7]),import.meta.url)).FlowGraphFunctionReferenceBlock;case"FlowGraphDataSwitchBlock":return async()=>(await n(()=>import("./flowGraphDataSwitchBlock-DjcRlAy4.js"),__vite__mapDeps([58,1,2,3,4,7]),import.meta.url)).FlowGraphDataSwitchBlock;default:if(S[r])return S[r];throw new Error(`Unknown block name ${r}`)}}class bt extends D{constructor(t){super(t),this.out=this._registerSignalOutput("out")}}class Pt extends bt{constructor(t,e){super(t),this._eventsSignalOutputs={},this.done=this._registerSignalOutput("done"),e?.forEach(o=>{this._eventsSignalOutputs[o]=this._registerSignalOutput(o+"Event")})}_executeOnTick(t){}_startPendingTasks(t){t._getExecutionVariable(this,"_initialized",!1)&&(this._cancelPendingTasks(t),this._resetAfterCanceled(t)),this._preparePendingTasks(t),t._addPendingBlock(this),this.out._activateSignal(t),t._setExecutionVariable(this,"_initialized",!0)}_resetAfterCanceled(t){t._deleteExecutionVariable(this,"_initialized"),t._removePendingBlock(this)}}class Ct extends Pt{constructor(){super(...arguments),this.initPriority=0,this.type="NoTrigger"}_execute(t){t._notifyExecuteNode(this),this.done._activateSignal(t)}}function Ot(r,t){for(const e of r)for(const o of e.dataOutputs)if(o.uniqueId===t)return o;throw new Error("Could not find data out connection with unique id "+t)}function xt(r,t){for(const e of r)if(e instanceof D){for(const o of e.signalInputs)if(o.uniqueId===t)return o}throw new Error("Could not find signal in connection with unique id "+t)}async function Dt(r,t){const e=await Promise.all(r.allBlocks.map(async o=>Vt(o.className)()));return At(r,t,e)}function At(r,t,e){const o=t.coordinator.createGraph(),a=[],i=t.valueParseFunction??x;for(let s=0;s<r.allBlocks.length;s++){const c=r.allBlocks[s],l=Lt(c,{scene:t.coordinator.config.scene,pathConverter:t.pathConverter,assetsContainer:t.coordinator.config.scene,valueParseFunction:i},e[s]);a.push(l),l instanceof Ct&&o.addEventBlock(l)}for(const s of a){for(const c of s.dataInputs)for(const l of c.connectedPointIds){const _=Ot(a,l);c.connectTo(_)}if(s instanceof D)for(const c of s.signalOutputs)for(const l of c.connectedPointIds){const _=xt(a,l);c.connectTo(_)}}for(const s of r.executionContexts)Rt(s,{graph:o,valueParseFunction:i},r.rightHanded);return o}function Rt(r,t,e){const o=t.graph.createContext();r.enableLogging&&(o.enableLogging=!0),o.treatDataAsRightHanded=e||!1;const a=t.valueParseFunction??x;o.uniqueId=r.uniqueId;const i=o.getScene();if(r._assetsContext){const s=r._assetsContext,c={meshes:s.meshes?.map(l=>i.getMeshById(l)),lights:s.lights?.map(l=>i.getLightByName(l)),cameras:s.cameras?.map(l=>i.getCameraByName(l)),materials:s.materials?.map(l=>i.getMaterialById(l)),textures:s.textures?.map(l=>i.getTextureByName(l)),animations:s.animations?.map(l=>i.animations.find(_=>_.name===l)),skeletons:s.skeletons?.map(l=>i.getSkeletonByName(l)),particleSystems:s.particleSystems?.map(l=>i.getParticleSystemById(l)),animationGroups:s.animationGroups?.map(l=>i.getAnimationGroupByName(l)),transformNodes:s.transformNodes?.map(l=>i.getTransformNodeById(l)),rootNodes:[],multiMaterials:[],morphTargetManagers:[],geometries:[],actionManagers:[],environmentTexture:null,postProcesses:[],sounds:null,effectLayers:[],layers:[],reflectionProbes:[],lensFlareSystems:[],proceduralTextures:[],getNodes:function(){throw new Error("Function not implemented.")}};o.assetsContext=c}for(const s in r._userVariables){const c=a(s,r._userVariables,o.assetsContext,i);o.userVariables[s]=c}for(const s in r._connectionValues){const c=a(s,r._connectionValues,o.assetsContext,i);o._setConnectionValueByKey(s,c)}return o}function Lt(r,t,e){const o={},a=t.valueParseFunction??x;if(r.config)for(const s in r.config)o[s]=a(s,r.config,t.assetsContainer||t.scene,t.scene);if(yt(r.className)){if(!t.pathConverter)throw new Error("Path converter is required for this block");o.pathConverter=t.pathConverter}const i=new e(o);i.uniqueId=r.uniqueId;for(let s=0;s<r.dataInputs.length;s++){const c=i.getDataInput(r.dataInputs[s].name);if(c)c.deserialize(r.dataInputs[s]);else throw new Error("Could not find data input with name "+r.dataInputs[s].name+" in block "+r.className)}for(let s=0;s<r.dataOutputs.length;s++){const c=i.getDataOutput(r.dataOutputs[s].name);if(c)c.deserialize(r.dataOutputs[s]);else throw new Error("Could not find data output with name "+r.dataOutputs[s].name+" in block "+r.className)}return i.metadata=r.metadata,i.deserialize&&i.deserialize(r),i}const Nt={float:{length:1,flowGraphType:"number",elementType:"number"},bool:{length:1,flowGraphType:"boolean",elementType:"boolean"},float2:{length:2,flowGraphType:"Vector2",elementType:"number"},float3:{length:3,flowGraphType:"Vector3",elementType:"number"},float4:{length:4,flowGraphType:"Vector4",elementType:"number"},float4x4:{length:16,flowGraphType:"Matrix",elementType:"number"},float2x2:{length:4,flowGraphType:"Matrix2D",elementType:"number"},float3x3:{length:9,flowGraphType:"Matrix3D",elementType:"number"},int:{length:1,flowGraphType:"FlowGraphInteger",elementType:"number"}};class St{constructor(t,e,o){this._interactivityGraph=t,this._gltf=e,this._loader=o,this._types=[],this._mappings=[],this._staticVariables=[],this._events=[],this._internalEventsCounter=0,this._nodes=[],this._parseTypes(),this._parseDeclarations(),this._parseVariables(),this._parseEvents(),this._parseNodes()}get arrays(){return{types:this._types,mappings:this._mappings,staticVariables:this._staticVariables,events:this._events,nodes:this._nodes}}_parseTypes(){if(this._interactivityGraph.types)for(const t of this._interactivityGraph.types)this._types.push(Nt[t.signature])}_parseDeclarations(){if(this._interactivityGraph.declarations)for(const t of this._interactivityGraph.declarations){const e=dt(t);if(!e)throw p.Error(["No mapping found for declaration",t]),new Error("Error parsing declarations");this._mappings.push({flowGraphMapping:e,fullOperationName:t.extension?t.op+":"+t.extension:t.op})}}_parseVariables(){if(this._interactivityGraph.variables)for(const t of this._interactivityGraph.variables){const e=this._parseVariable(t);this._staticVariables.push(e)}}_parseVariable(t,e){const o=this._types[t.type];if(!o)throw p.Error(["No type found for variable",t]),new Error("Error parsing variables");if(t.value&&t.value.length!==o.length)throw p.Error(["Invalid value length for variable",t,o]),new Error("Error parsing variables");const a=t.value||[];if(!a.length)switch(o.flowGraphType){case"boolean":a.push(!1);break;case"FlowGraphInteger":a.push(0);break;case"number":a.push(NaN);break;case"Vector2":a.push(NaN,NaN);break;case"Vector3":a.push(NaN,NaN,NaN);break;case"Vector4":case"Matrix2D":case"Quaternion":a.fill(NaN,0,4);break;case"Matrix":a.fill(NaN,0,16);break;case"Matrix3D":a.fill(NaN,0,9);break}return{type:o.flowGraphType,value:e?e(a,this):a}}_parseEvents(){if(this._interactivityGraph.events)for(const t of this._interactivityGraph.events){const e={eventId:t.id||"internalEvent_"+this._internalEventsCounter++};t.values&&(e.eventData=Object.keys(t.values).map(o=>{const a=t.values?.[o];if(!a)throw p.Error(["No value found for event key",o]),new Error("Error parsing events");const i=this._types[a.type];if(!i)throw p.Error(["No type found for event value",a]),new Error("Error parsing events");const s=typeof a.value<"u"?this._parseVariable(a):void 0;return{id:o,type:i.flowGraphType,eventData:!0,value:s}})),this._events.push(e)}}_parseNodes(){if(this._interactivityGraph.nodes)for(const t of this._interactivityGraph.nodes){if(typeof t.declaration!="number")throw p.Error(["No declaration found for node",t]),new Error("Error parsing nodes");const e=this._mappings[t.declaration];if(!e)throw p.Error(["No mapping found for node",t]),new Error("Error parsing nodes");if(e.flowGraphMapping.validation&&!e.flowGraphMapping.validation(t,this._interactivityGraph,this._gltf))throw new Error(`Error validating interactivity node ${t}`);const o=[];for(const a of e.flowGraphMapping.blocks){const i=this._getEmptyBlock(a,e.fullOperationName);this._parseNodeConfiguration(t,i,e.flowGraphMapping,a),o.push(i)}this._nodes.push({blocks:o,fullOperationName:e.fullOperationName})}}_getEmptyBlock(t,e){return{uniqueId:F(),className:t,dataInputs:[],dataOutputs:[],signalInputs:[],signalOutputs:[],config:{},type:e,metadata:{}}}_parseNodeConfiguration(t,e,o,a){const i=e.config;t.configuration&&Object.keys(t.configuration).forEach(s=>{const c=t.configuration?.[s];if(!c)throw p.Error(["No value found for node configuration",s]),new Error("Error parsing node configuration");const l=o.configuration?.[s];if(l&&l.toBlock?l.toBlock===a:o.blocks.indexOf(a)===0){const u=l?.name||s;(!c||typeof c.value>"u")&&typeof l?.defaultValue<"u"?i[u]={value:l.defaultValue}:c.value.length>=1?i[u]={value:c.value.length===1?c.value[0]:c.value}:p.Warn(["Invalid value for node configuration",c]),l&&l.dataTransformer&&(i[u].value=l.dataTransformer([i[u].value],this)[0])}})}_parseNodeConnections(t){for(let e=0;e<this._nodes.length;e++){const o=this._interactivityGraph.nodes?.[e];if(!o)throw p.Error(["No node found for interactivity node",this._nodes[e]]),new Error("Error parsing node connections");const a=this._nodes[e],i=this._mappings[o.declaration];if(!i)throw p.Error(["No mapping found for node",o]),new Error("Error parsing node connections");const s=o.flows||{},c=Object.keys(s).sort();for(const u of c){const d=s[u],h=i.flowGraphMapping.outputs?.flows?.[u],I=h?.name||u,T=this._createNewSocketConnection(I,!0);(h&&h.toBlock&&a.blocks.find(w=>w.className===h.toBlock)||a.blocks[0]).signalOutputs.push(T);const A=d.node,m=this._nodes[A];if(!m)throw p.Error(["No node found for input node id",A]),new Error("Error parsing node connections");const y=$(m.fullOperationName);if(!y)throw p.Error(["No mapping found for input node",m]),new Error("Error parsing node connections");let f=y.inputs?.flows?.[d.socket||"in"],k=!1;if(!f)for(const w in y.inputs?.flows)w.startsWith("[")&&w.endsWith("]")&&(k=!0,f=y.inputs?.flows?.[w]);const g=f?k?f.name.replace("$1",d.socket||""):f.name:d.socket||"in",b=f&&f.toBlock&&m.blocks.find(w=>w.className===f.toBlock)||m.blocks[0];let v=b.signalInputs.find(w=>w.name===g);v||(v=this._createNewSocketConnection(g),b.signalInputs.push(v)),v.connectedPointIds.push(T.uniqueId),T.connectedPointIds.push(v.uniqueId)}const l=o.values||{},_=Object.keys(l);for(const u of _){const d=l[u];let h=i.flowGraphMapping.inputs?.values?.[u],I=!1;if(!h)for(const m in i.flowGraphMapping.inputs?.values)m.startsWith("[")&&m.endsWith("]")&&(I=!0,h=i.flowGraphMapping.inputs?.values?.[m]);const T=h?I?h.name.replace("$1",u):h.name:u,V=this._createNewSocketConnection(T);if((h&&h.toBlock&&a.blocks.find(m=>m.className===h.toBlock)||a.blocks[0]).dataInputs.push(V),d.value!==void 0){const m=this._parseVariable(d,h&&h.dataTransformer);t._connectionValues[V.uniqueId]=m}else if(typeof d.node<"u"){const m=d.node,y=d.socket||"value",f=this._nodes[m];if(!f)throw p.Error(["No node found for output socket reference",d]),new Error("Error parsing node connections");const k=$(f.fullOperationName);if(!k)throw p.Error(["No mapping found for output socket reference",d]),new Error("Error parsing node connections");let g=k.outputs?.values?.[y],b=!1;if(!g)for(const G in k.outputs?.values)G.startsWith("[")&&G.endsWith("]")&&(b=!0,g=k.outputs?.values?.[G]);const v=g?b?g.name.replace("$1",y):g?.name:y,w=g&&g.toBlock&&f.blocks.find(G=>G.className===g.toBlock)||f.blocks[0];let P=w.dataOutputs.find(G=>G.name===v);P||(P=this._createNewSocketConnection(v,!0),w.dataOutputs.push(P)),V.connectedPointIds.push(P.uniqueId),P.connectedPointIds.push(V.uniqueId)}else throw p.Error(["Invalid value for value connection",d]),new Error("Error parsing node connections")}if(i.flowGraphMapping.interBlockConnectors)for(const u of i.flowGraphMapping.interBlockConnectors){const d=u.input,h=u.output,I=u.isVariable;this._connectFlowGraphNodes(d,h,a.blocks[u.inputBlockIndex],a.blocks[u.outputBlockIndex],I)}if(i.flowGraphMapping.extraProcessor){const u=this._interactivityGraph.declarations?.[o.declaration];if(!u)throw p.Error(["No declaration found for extra processor",o]),new Error("Error parsing node connections");a.blocks=i.flowGraphMapping.extraProcessor(o,u,i.flowGraphMapping,this,a.blocks,t,this._gltf)}}}_createNewSocketConnection(t,e){return{uniqueId:F(),name:t,_connectionType:e?1:0,connectedPointIds:[]}}_connectFlowGraphNodes(t,e,o,a,i){const s=i?o.dataInputs:o.signalInputs,c=i?a.dataOutputs:a.signalOutputs,l=s.find(u=>u.name===t)||this._createNewSocketConnection(t),_=c.find(u=>u.name===e)||this._createNewSocketConnection(e,!0);s.find(u=>u.name===t)||s.push(l),c.find(u=>u.name===e)||c.push(_),l.connectedPointIds.push(_.uniqueId),_.connectedPointIds.push(l.uniqueId)}getVariableName(t){return"staticVariable_"+t}serializeToFlowGraph(){const t={uniqueId:F(),_userVariables:{},_connectionValues:{}};this._parseNodeConnections(t);for(let o=0;o<this._staticVariables.length;o++){const a=this._staticVariables[o];t._userVariables[this.getVariableName(o)]=a}return{rightHanded:!0,allBlocks:this._nodes.reduce((o,a)=>o.concat(a.blocks),[]),executionContexts:[t]}}}const C="KHR_interactivity";class j{constructor(t){this._loader=t,this.name=C,this.enabled=this._loader.isExtensionUsed(C),this._pathConverter=mt(this._loader.gltf),t._skipStartAnimationStep=!0;const e=t.babylonScene;e&&tt(e)}dispose(){this._loader=null,delete this._pathConverter}async onReady(){if(!this._loader.babylonScene||!this._pathConverter)return;const t=this._loader.babylonScene,e=this._loader.gltf.extensions?.KHR_interactivity;if(!e)return;const o=new E({scene:t});o.dispatchEventsSynchronously=!1;const a=e.graphs.map(i=>new St(i,this._loader.gltf,this._loader).serializeToFlowGraph());await Promise.all(a.map(i=>Dt(i,{coordinator:o,pathConverter:this._pathConverter}))),o.start()}}function tt(r){B("/extensions/KHR_interactivity/?/activeCamera/rotation",{get:()=>r.activeCamera?R.FromRotationMatrix(r.activeCamera.getWorldMatrix()).normalize():new R(NaN,NaN,NaN,NaN),type:"Quaternion",getTarget:()=>r.activeCamera}),B("/extensions/KHR_interactivity/?/activeCamera/position",{get:()=>r.activeCamera?r.activeCamera.position:new Q(NaN,NaN,NaN),type:"Vector3",getTarget:()=>r.activeCamera}),B("/animations/{}/extensions/KHR_interactivity/isPlaying",{get:t=>t._babylonAnimationGroup?.isPlaying??!1,type:"boolean",getTarget:t=>t._babylonAnimationGroup}),B("/animations/{}/extensions/KHR_interactivity/minTime",{get:t=>(t._babylonAnimationGroup?.from??0)/60,type:"number",getTarget:t=>t._babylonAnimationGroup}),B("/animations/{}/extensions/KHR_interactivity/maxTime",{get:t=>(t._babylonAnimationGroup?.to??0)/60,type:"number",getTarget:t=>t._babylonAnimationGroup}),B("/animations/{}/extensions/KHR_interactivity/playhead",{get:t=>(t._babylonAnimationGroup?.getCurrentFrame()??0)/60,type:"number",getTarget:t=>t._babylonAnimationGroup}),B("/animations/{}/extensions/KHR_interactivity/virtualPlayhead",{get:t=>(t._babylonAnimationGroup?.getCurrentFrame()??0)/60,type:"number",getTarget:t=>t._babylonAnimationGroup})}Tt(C,"FlowGraphGLTFDataProvider",async()=>(await n(()=>import("./flowGraphGLTFDataProvider-CX2EVY_9.js"),__vite__mapDeps([59,1,2,3,4,7]),import.meta.url)).FlowGraphGLTFDataProvider);lt(C);ct(C,!0,r=>new j(r));const Zt=Object.freeze(Object.defineProperty({__proto__:null,KHR_interactivity:j,_AddInteractivityObjectModel:tt},Symbol.toStringTag,{value:"Module"}));export{Gt as F,vt as G,Zt as K,z as _,Pt as a,bt as b,Ct as c,E as d,Ut as e,D as f,It as g,M as h,Jt as i,Wt as j,Ht as k,Qt as l};
//# sourceMappingURL=KHR_interactivity-DVSiPm30.js.map
