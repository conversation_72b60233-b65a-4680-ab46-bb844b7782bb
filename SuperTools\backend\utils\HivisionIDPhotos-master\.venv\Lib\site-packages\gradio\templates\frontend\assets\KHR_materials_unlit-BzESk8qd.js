import{ar as d,C as l,an as p,ao as u}from"./index-Cb4A4-Xi.js";import{GLTFLoader as h}from"./glTFLoader-D-NF4fEj.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";import"./bone-CgNwSK5F.js";import"./rawTexture-PjZ4PTsN.js";import"./assetContainer-CIn78ZXO.js";import"./objectModelMapping-D3Nr8hfO.js";const t="KHR_materials_unlit";class m{constructor(e){this.name=t,this.order=210,this._loader=e,this.enabled=this._loader.isExtensionUsed(t)}dispose(){this._loader=null}loadMaterialPropertiesAsync(e,o,r){return h.LoadExtensionAsync(e,o,this.name,()=>this._loadUnlitPropertiesAsync(e,o,r))}_loadUnlitPropertiesAsync(e,o,r){if(!(r instanceof d))throw new Error(`${e}: Material type not supported`);const n=new Array;r.unlit=!0;const s=o.pbrMetallicRoughness;return s&&(s.baseColorFactor?(r.albedoColor=l.FromArray(s.baseColorFactor),r.alpha=s.baseColorFactor[3]):r.albedoColor=l.White(),s.baseColorTexture&&n.push(this._loader.loadTextureInfoAsync(`${e}/baseColorTexture`,s.baseColorTexture,a=>{a.name=`${r.name} (Base Color)`,r.albedoTexture=a}))),o.doubleSided&&(r.backFaceCulling=!1,r.twoSidedLighting=!0),this._loader.loadMaterialAlphaProperties(e,o,r),Promise.all(n).then(()=>{})}}p(t);u(t,!0,i=>new m(i));export{m as KHR_materials_unlit};
//# sourceMappingURL=KHR_materials_unlit-BzESk8qd.js.map
