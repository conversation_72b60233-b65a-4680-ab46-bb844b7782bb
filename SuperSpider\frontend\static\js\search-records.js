/**
 * SuperSpider 搜索历史记录脚本
 * 处理用户搜索历史记录相关功能
 */

// 当前页码
let currentPage = 1;
// 每页记录数
const perPage = 10;
// 当前筛选条件
let currentFilters = {};
// 选中的搜索记录ID
let selectedRecordIds = [];
// 当前视图模式：'list' 或 'grid'
let currentViewMode = 'list';
// 当前排序方式
let currentSort = { field: 'created_at', order: 'desc' };

// 防抖变量
let loadHistoryTimeout = null;
let loadStatsTimeout = null;

// 初始化搜索历史记录功能
function initSearchHistoryFeatures() {
    console.log('初始化搜索记录功能...');

    // 搜索记录链接点击事件
    const searchHistoryLink = document.getElementById('search-history-link');
    console.log('搜索记录链接元素:', searchHistoryLink);

    if (searchHistoryLink) {
        // 移除可能存在的旧事件监听器
        searchHistoryLink.removeEventListener('click', handleSearchHistoryLinkClick);
        // 添加新的事件监听器
        searchHistoryLink.addEventListener('click', handleSearchHistoryLinkClick);
        console.log('搜索记录链接事件监听器已添加');
    } else {
        console.error('未找到搜索记录链接元素');
    }

    // 初始化模态框关闭事件
    initModalCloseEvents();
}

// 初始化模态框关闭事件
function initModalCloseEvents() {
    // 监听模态框关闭按钮
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('close-modal') ||
            e.target.closest('.close-modal')) {
            const modal = e.target.closest('.modal');
            if (modal) {
                modal.classList.remove('active');
            }
        }

        // 点击遮罩层关闭模态框
        if (e.target.classList.contains('modal-overlay')) {
            const modal = e.target.closest('.modal');
            if (modal) {
                modal.classList.remove('active');
            }
        }
    });

    // ESC键关闭模态框
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const activeModal = document.querySelector('.modal.active');
            if (activeModal) {
                activeModal.classList.remove('active');
            }
        }
    });
}

// 处理搜索记录链接点击事件
function handleSearchHistoryLinkClick(e) {
    if (e) e.preventDefault();
    console.log('搜索记录链接被点击');

    // 显示搜索历史记录模态框
    showSearchHistoryModal();
}

// 显示搜索历史记录模态框
function showSearchHistoryModal() {
    console.log('显示搜索历史记录模态框');

    const modal = document.getElementById('search-history-modal');
    if (modal) {
        modal.classList.add('active');

        // 初始化模态框功能
        setTimeout(() => {
            initSearchHistoryModalFeatures();
            loadSearchHistory();
            loadSearchStats();
        }, 100);
    } else {
        console.error('找不到搜索历史记录模态框元素');
    }
}



// 关闭搜索历史记录模态框
function closeSearchHistoryModal() {
    const modal = document.getElementById('search-history-modal');
    if (modal) {
        modal.classList.remove('active');
    }
}

// 继续初始化搜索历史记录功能
function continueInitSearchHistoryFeatures() {
    // 筛选表单提交
    const filterForm = document.getElementById('search-filter-form');
    if (filterForm) {
        filterForm.addEventListener('submit', (e) => {
            e.preventDefault();

            // 获取筛选条件
            const platform = document.getElementById('filter-platform').value;
            const status = document.getElementById('filter-status').value;
            const contentType = document.getElementById('filter-content-type').value;
            const favorite = document.getElementById('filter-favorite').checked;

            // 更新筛选条件
            currentFilters = {
                platform: platform || undefined,
                status: status || undefined,
                content_type: contentType || undefined
            };

            // 如果选择了只显示收藏
            if (favorite) {
                currentFilters.is_favorite = true;
            }

            // 重置页码并加载数据
            currentPage = 1;
            loadSearchHistoryDebounced();
        });
    }

    // 重置筛选按钮
    const resetFilterBtn = document.getElementById('reset-filter-btn');
    if (resetFilterBtn) {
        resetFilterBtn.addEventListener('click', () => {
            // 重置筛选表单
            const filterForm = document.getElementById('search-filter-form');
            if (filterForm) {
                filterForm.reset();
            }

            // 清空筛选条件
            currentFilters = {};

            // 重置页码并加载数据
            currentPage = 1;
            loadSearchHistoryDebounced();
        });
    }

    // 分页按钮
    document.addEventListener('click', (e) => {
        if (e.target.classList.contains('page-link')) {
            e.preventDefault();

            const page = parseInt(e.target.dataset.page);
            if (!isNaN(page) && page !== currentPage) {
                currentPage = page;
                loadSearchHistoryDebounced();
            }
        }
    });

    // 视图切换按钮
    const viewModeButtons = document.querySelectorAll('.view-mode-btn');
    viewModeButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            const mode = btn.dataset.mode;
            if (mode && mode !== currentViewMode) {
                currentViewMode = mode;

                // 更新按钮状态
                viewModeButtons.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');

                // 重新渲染搜索记录列表
                renderSearchRecordsList(window.lastSearchRecords || []);
            }
        });
    });

    // 排序下拉菜单
    const sortSelect = document.getElementById('sort-select');
    if (sortSelect) {
        sortSelect.addEventListener('change', () => {
            const value = sortSelect.value;
            const [field, order] = value.split('-');

            if (field && order) {
                currentSort = { field, order };
                loadSearchHistoryDebounced();
            }
        });
    }

    // 批量操作按钮
    const batchActionButtons = document.querySelectorAll('.batch-btn');
    batchActionButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            const action = btn.dataset.action;

            if (!selectedRecordIds.length) {
                showToast('请先选择搜索记录', 'warning');
                return;
            }

            if (action === 'delete') {
                if (confirm('确定要删除选中的搜索记录吗？')) {
                    batchDeleteSearchRecords(selectedRecordIds);
                }
            } else if (action === 'favorite') {
                batchToggleFavorite(selectedRecordIds, true);
            } else if (action === 'unfavorite') {
                batchToggleFavorite(selectedRecordIds, false);
            }
        });
    });

    // 全选/取消全选复选框
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', () => {
            const checked = selectAllCheckbox.checked;
            const checkboxes = document.querySelectorAll('.search-record-checkbox');

            checkboxes.forEach(checkbox => {
                checkbox.checked = checked;

                // 更新选中的搜索记录ID
                const recordId = parseInt(checkbox.dataset.id);
                if (checked) {
                    if (!selectedRecordIds.includes(recordId)) {
                        selectedRecordIds.push(recordId);
                    }
                } else {
                    selectedRecordIds = selectedRecordIds.filter(id => id !== recordId);
                }
            });

            // 更新批量操作按钮状态
            updateBatchActionButtons();
        });
    }

    // 导出按钮
    const exportBtn = document.getElementById('export-search-records-btn');
    if (exportBtn) {
        exportBtn.addEventListener('click', () => {
            exportSearchRecords();
        });
    }
}

// 初始化其他搜索历史功能（当模态框打开时调用）
function initSearchHistoryModalFeatures() {
    // 当搜索历史模态框打开时，初始化其他功能
    const searchHistoryModal = document.getElementById('search-history-modal');
    if (searchHistoryModal && !searchHistoryModal.dataset.initialized) {
        // 标记模态框已初始化，防止重复初始化
        searchHistoryModal.dataset.initialized = 'true';

        // 初始化其他功能
        continueInitSearchHistoryFeatures();
    }
}

// 防抖版本的加载搜索历史记录
function loadSearchHistoryDebounced(delay = 300) {
    if (loadHistoryTimeout) {
        clearTimeout(loadHistoryTimeout);
    }

    loadHistoryTimeout = setTimeout(() => {
        loadSearchHistory();
    }, delay);
}

// 防抖版本的加载搜索统计
function loadSearchStatsDebounced(delay = 300) {
    if (loadStatsTimeout) {
        clearTimeout(loadStatsTimeout);
    }

    loadStatsTimeout = setTimeout(() => {
        loadSearchStats();
    }, delay);
}

// 加载搜索历史记录
async function loadSearchHistory() {
    console.log('开始加载搜索历史记录...');

    const searchRecordsList = document.getElementById('search-records-list');
    const searchRecordsPagination = document.getElementById('search-records-pagination');
    const searchRecordsLoading = document.getElementById('search-records-loading');
    const searchRecordsEmpty = document.getElementById('search-records-empty');

    console.log('DOM元素检查:', {
        searchRecordsList: !!searchRecordsList,
        searchRecordsPagination: !!searchRecordsPagination,
        searchRecordsLoading: !!searchRecordsLoading,
        searchRecordsEmpty: !!searchRecordsEmpty
    });

    if (!searchRecordsList || !searchRecordsPagination) {
        console.error('关键DOM元素未找到');
        return;
    }

    // 检查登录状态（但不阻止API调用，让后端来验证）
    console.log('检查登录状态:', window.isLoggedIn);

    try {
        // 显示加载中
        if (searchRecordsLoading) {
            searchRecordsLoading.style.display = 'block';
        }

        // 隐藏空状态
        if (searchRecordsEmpty) {
            searchRecordsEmpty.style.display = 'none';
        }

        // 构建查询参数 - 简化版本
        const params = new URLSearchParams({
            page: currentPage || 1,
            per_page: 10
        });

        // 只添加有值的筛选条件
        if (currentFilters.platform) {
            params.append('platform', currentFilters.platform);
        }
        if (currentFilters.content_type) {
            params.append('content_type', currentFilters.content_type);
        }
        if (currentFilters.status) {
            params.append('status', currentFilters.status);
        }

        // 确定API端点
        let url = '/api/search/history';
        if (currentFilters.is_favorite) {
            url = '/api/search/favorites';
        }

        const fullUrl = `${url}?${params.toString()}`;
        console.log('发送API请求:', fullUrl);

        const response = await fetch(fullUrl, {
            method: 'GET',
            credentials: 'include', // 确保包含cookies
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        console.log('API响应状态:', response.status);

        // 检查响应状态
        if (response.status === 401) {
            // 未登录，显示登录提示
            if (searchRecordsLoading) {
                searchRecordsLoading.style.display = 'none';
            }

            searchRecordsList.innerHTML = `
                <div class="empty-container">
                    <i class="fas fa-user-lock"></i>
                    <h3>需要登录</h3>
                    <p>请先登录后查看搜索记录</p>
                    <button class="btn btn-primary" onclick="showLoginModal()">立即登录</button>
                </div>
            `;

            // 清空分页
            searchRecordsPagination.innerHTML = '';
            return;
        }

        const data = await response.json();
        console.log('完整API响应数据:', data);

        // 隐藏加载中
        if (searchRecordsLoading) {
            searchRecordsLoading.style.display = 'none';
        }

        if (data.success) {
            const { records, total, page, pages } = data.data;
            console.log('API返回的完整数据:', data.data);
            console.log('解析后的records:', records);
            console.log('records类型:', typeof records);
            console.log('records是否为数组:', Array.isArray(records));
            console.log('records长度:', records ? records.length : 'undefined');

            // 保存搜索记录，用于视图切换
            window.lastSearchRecords = records;

            // 更新当前页码
            currentPage = page;

            if (!records || !Array.isArray(records) || records.length === 0) {
                console.log('没有搜索记录，显示空状态');

                // 显示友好的空状态消息
                searchRecordsList.innerHTML = `
                    <div class="empty-container">
                        <i class="fas fa-inbox"></i>
                        <p>暂无搜索记录</p>
                    </div>
                `;
                searchRecordsPagination.innerHTML = '';
                return;
            }

            console.log('开始渲染', records.length, '条记录');
            console.log('第一条记录内容:', records[0]);

            // 渲染搜索列表
            renderSearchRecordsList(records);

            // 渲染分页
            renderPagination(page, pages, total);

            // 只在首次加载时加载搜索统计，避免重复请求
            if (currentPage === 1 && Object.keys(currentFilters).length === 0) {
                loadSearchStats();
            }

            // 重置选中的搜索记录ID
            selectedRecordIds = [];

            // 更新批量操作按钮状态
            updateBatchActionButtons();
        } else {
            console.error('获取搜索历史记录失败:', data.message);

            // 显示错误消息
            searchRecordsList.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    <p>获取搜索历史记录失败: ${data.message}</p>
                    <button class="btn btn-primary" onclick="loadSearchHistory()">重试</button>
                </div>
            `;

            // 清空分页
            searchRecordsPagination.innerHTML = '';
        }
    } catch (error) {
        console.error('获取搜索历史记录请求失败:', error);

        // 隐藏加载中
        if (searchRecordsLoading) {
            searchRecordsLoading.style.display = 'none';
        }

        // 显示错误消息
        searchRecordsList.innerHTML = `
            <div class="error-message">
                <i class="fas fa-exclamation-circle"></i>
                <p>获取搜索历史记录请求失败，请稍后再试</p>
                <button class="btn btn-primary" onclick="loadSearchHistory()">重试</button>
            </div>
        `;

        // 清空分页
        searchRecordsPagination.innerHTML = '';
    }
}

// 渲染搜索记录列表
function renderSearchRecordsList(records) {
    console.log('renderSearchRecordsList被调用，参数:', records);
    console.log('records长度:', records ? records.length : 'undefined');

    const searchRecordsList = document.getElementById('search-records-list');
    console.log('找到search-records-list元素:', searchRecordsList);

    if (!searchRecordsList) {
        console.error('search-records-list 元素未找到');
        return;
    }

    // 清空列表
    searchRecordsList.innerHTML = '';
    console.log('列表已清空');

    // 根据视图模式渲染
    if (currentViewMode === 'grid') {
        console.log('使用网格视图渲染');
        renderGridView(records, searchRecordsList);
    } else {
        console.log('使用列表视图渲染');
        renderListView(records, searchRecordsList);
    }

    console.log('渲染完成，容器内容:', searchRecordsList.innerHTML.substring(0, 200));
}

// 渲染列表视图
function renderListView(records, container) {
    console.log('renderListView开始，records:', records);

    try {
        // 创建表格
        const table = document.createElement('table');
        table.className = 'search-records-table';
        // 添加强制样式确保可见
        table.style.cssText = 'width: 100%; border-collapse: collapse; background: white; display: table;';
        console.log('创建表格成功');

        // 创建表头
        const thead = document.createElement('thead');
        thead.innerHTML = `
            <tr>
                <th class="checkbox-column">
                    <input type="checkbox" id="select-all-checkbox">
                </th>
                <th>标题</th>
                <th>平台</th>
                <th>类型</th>
                <th>状态</th>
                <th>时间</th>
                <th>操作</th>
            </tr>
        `;
        table.appendChild(thead);
        console.log('表头创建成功');

        // 创建表体
        const tbody = document.createElement('tbody');
        console.log('开始处理', records.length, '条记录');

        records.forEach((record, index) => {
            console.log(`处理第${index + 1}条记录:`, record);

            try {
                const tr = document.createElement('tr');

                // 设置状态类名
                if (record.status === 'success') {
                    tr.classList.add('status-completed');
                } else if (record.status === 'failed') {
                    tr.classList.add('status-failed');
                }

                // 如果是收藏，添加收藏类名
                if (record.is_favorite) {
                    tr.classList.add('is-favorite');
                }

                // 简化的行内容，避免复杂的函数调用
                tr.innerHTML = `
                    <td class="checkbox-column">
                        <input type="checkbox" class="search-record-checkbox" data-id="${record.id}">
                    </td>
                    <td class="search-record-title" title="${record.title || '未知标题'}">${record.title || '未知标题'}</td>
                    <td>${record.platform || '未知'}</td>
                    <td>${record.content_type || '未知'}</td>
                    <td>${record.status || '未知'}</td>
                    <td>${record.created_at || '未知时间'}</td>
                    <td>
                        <div class="search-record-actions">
                            <button class="action-btn favorite-btn ${record.is_favorite ? 'active' : ''}"
                                    data-id="${record.id}"
                                    title="${record.is_favorite ? '取消收藏' : '收藏'}"
                                    onclick="toggleFavorite(${record.id})">
                                <i class="fas fa-star"></i>
                            </button>
                            <button class="action-btn delete-btn"
                                    data-id="${record.id}"
                                    title="删除"
                                    onclick="deleteSearchRecord(${record.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                `;

                tbody.appendChild(tr);
                console.log(`第${index + 1}条记录添加成功`);
            } catch (error) {
                console.error(`处理第${index + 1}条记录时出错:`, error);
            }
        });

        table.appendChild(tbody);
        container.appendChild(table);
        console.log('表格添加到容器成功，表格行数:', tbody.children.length);
        console.log('容器信息:', {
            id: container.id,
            className: container.className,
            innerHTML: container.innerHTML.substring(0, 200) + '...',
            offsetHeight: container.offsetHeight,
            scrollHeight: container.scrollHeight,
            style: container.style.cssText
        });

        // 添加复选框事件
        addCheckboxEvents();
        console.log('renderListView完成');
    } catch (error) {
        console.error('renderListView出错:', error);
    }
}

// 渲染网格视图
function renderGridView(records, container) {
    const grid = document.createElement('div');
    grid.className = 'search-records-grid';

    records.forEach(record => {
        const card = document.createElement('div');
        card.className = 'search-record-card';

        // 设置状态类名
        if (record.status === 'success') {
            card.classList.add('status-completed');
        } else if (record.status === 'failed') {
            card.classList.add('status-failed');
        }

        // 如果是收藏，添加收藏类名
        if (record.is_favorite) {
            card.classList.add('is-favorite');
        }

        // 缩略图
        let thumbnailHtml = '';
        if (record.thumbnail_url) {
            thumbnailHtml = `<img src="${record.thumbnail_url}" alt="${record.title || '未知标题'}" class="search-record-thumbnail">`;
        } else {
            thumbnailHtml = `<div class="search-record-thumbnail-placeholder">
                <i class="${getContentTypeIcon(record.content_type)}"></i>
            </div>`;
        }

        // 设置卡片内容
        card.innerHTML = `
            <div class="search-record-card-header">
                <div class="search-record-checkbox-container">
                    <input type="checkbox" class="search-record-checkbox" data-id="${record.id}">
                </div>
                <div class="search-record-card-platform">${getPlatformLabel(record.platform)}</div>
                ${record.is_favorite ? '<div class="search-record-card-favorite"><i class="fas fa-star"></i></div>' : ''}
            </div>
            <div class="search-record-card-thumbnail">
                ${thumbnailHtml}
            </div>
            <div class="search-record-card-content">
                <h3 class="search-record-card-title" title="${record.title || '未知标题'}">${record.title || '未知标题'}</h3>
                <div class="search-record-card-author">${record.author || '未知作者'}</div>
                <div class="search-record-card-meta">
                    <span class="search-record-card-type">${getContentTypeLabel(record.content_type)}</span>
                    <span class="search-record-card-status">${getStatusLabel(record.status)}</span>
                </div>
                <div class="search-record-card-date">${formatDate(record.created_at)}</div>
                ${record.notes ? '<div class="search-record-card-notes-indicator"><i class="fas fa-sticky-note"></i> 有笔记</div>' : ''}
            </div>
            <div class="search-record-card-actions">
                ${record.video_url ?
                    `<a href="${record.video_url}" target="_blank" class="view-btn" title="查看视频">
                        <i class="fas fa-external-link-alt"></i>
                    </a>` : ''
                }
                <button class="action-btn favorite-btn ${record.is_favorite ? 'active' : ''}"
                        data-id="${record.id}"
                        title="${record.is_favorite ? '取消收藏' : '收藏'}"
                        onclick="toggleFavorite(${record.id})">
                    <i class="fas fa-star"></i>
                </button>
                <button class="action-btn notes-btn ${record.notes ? 'has-notes' : ''}"
                        data-id="${record.id}"
                        title="${record.notes ? '编辑笔记' : '添加笔记'}"
                        onclick="showNotesModal(${record.id}, '${record.title || '未知标题'}')">
                    <i class="fas fa-sticky-note"></i>
                </button>
                <button class="action-btn delete-btn"
                        data-id="${record.id}"
                        title="删除"
                        onclick="deleteSearchRecord(${record.id})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;

        grid.appendChild(card);
    });

    container.appendChild(grid);

    // 添加复选框事件
    addCheckboxEvents();
}

// 添加复选框事件
function addCheckboxEvents() {
    const checkboxes = document.querySelectorAll('.search-record-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', () => {
            const recordId = parseInt(checkbox.dataset.id);

            if (checkbox.checked) {
                if (!selectedRecordIds.includes(recordId)) {
                    selectedRecordIds.push(recordId);
                }
            } else {
                selectedRecordIds = selectedRecordIds.filter(id => id !== recordId);

                // 取消全选复选框
                const selectAllCheckbox = document.getElementById('select-all-checkbox');
                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = false;
                }
            }

            // 更新批量操作按钮状态
            updateBatchActionButtons();
        });
    });
}

// 更新批量操作按钮状态
function updateBatchActionButtons() {
    const batchActionButtons = document.querySelectorAll('.batch-btn');
    const hasSelected = selectedRecordIds.length > 0;

    batchActionButtons.forEach(btn => {
        if (hasSelected) {
            btn.classList.remove('disabled');
        } else {
            btn.classList.add('disabled');
        }
    });

    // 更新选中数量显示
    const selectedCountEl = document.getElementById('selected-count');
    if (selectedCountEl) {
        selectedCountEl.textContent = selectedRecordIds.length;
    }
}

// 渲染分页
function renderPagination(currentPage, totalPages, totalItems) {
    const paginationContainer = document.getElementById('search-records-pagination');
    if (!paginationContainer) return;

    // 清空分页
    paginationContainer.innerHTML = '';

    // 如果只有一页，不显示分页
    if (totalPages <= 1) return;

    // 创建分页
    const pagination = document.createElement('div');
    pagination.className = 'pagination';

    // 添加总记录数信息
    const info = document.createElement('div');
    info.className = 'pagination-info';
    info.textContent = `共 ${totalItems} 条记录，${totalPages} 页`;
    pagination.appendChild(info);

    // 添加分页链接
    const links = document.createElement('div');
    links.className = 'pagination-links';

    // 上一页
    const prevLink = document.createElement('a');
    prevLink.className = `page-link ${currentPage <= 1 ? 'disabled' : ''}`;
    prevLink.href = '#';
    prevLink.dataset.page = currentPage - 1;
    prevLink.innerHTML = '<i class="fas fa-chevron-left"></i>';
    links.appendChild(prevLink);

    // 页码
    const maxPages = 5; // 最多显示的页码数
    let startPage = Math.max(1, currentPage - Math.floor(maxPages / 2));
    let endPage = Math.min(totalPages, startPage + maxPages - 1);

    if (endPage - startPage + 1 < maxPages) {
        startPage = Math.max(1, endPage - maxPages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
        const pageLink = document.createElement('a');
        pageLink.className = `page-link ${i === currentPage ? 'active' : ''}`;
        pageLink.href = '#';
        pageLink.dataset.page = i;
        pageLink.textContent = i;
        links.appendChild(pageLink);
    }

    // 下一页
    const nextLink = document.createElement('a');
    nextLink.className = `page-link ${currentPage >= totalPages ? 'disabled' : ''}`;
    nextLink.href = '#';
    nextLink.dataset.page = currentPage + 1;
    nextLink.innerHTML = '<i class="fas fa-chevron-right"></i>';
    links.appendChild(nextLink);

    pagination.appendChild(links);
    paginationContainer.appendChild(pagination);
}

// 加载搜索统计
async function loadSearchStats() {
    const statsContainer = document.getElementById('search-stats');
    if (!statsContainer) return;

    try {
        const response = await fetch('/api/search/stats');
        const data = await response.json();

        if (data.success) {
            const { total_searches, platform_stats, recent_searches, favorite_count, content_type_stats, success_rate } = data.data;

            // 更新统计信息 - 使用简化的统计项
            const statItems = statsContainer.querySelectorAll('.stat-item');

            if (statItems.length >= 4) {
                // 总搜索数
                const totalValue = statItems[0].querySelector('.stat-value');
                if (totalValue) {
                    totalValue.textContent = total_searches || 0;
                }

                // 最近7天
                const recentValue = statItems[1].querySelector('.stat-value');
                if (recentValue) {
                    recentValue.textContent = recent_searches || 0;
                }

                // 收藏数
                const favoriteValue = statItems[2].querySelector('.stat-value');
                if (favoriteValue) {
                    favoriteValue.textContent = favorite_count || 0;
                }

                // 成功率
                const successValue = statItems[3].querySelector('.stat-value');
                if (successValue) {
                    const rate = success_rate || 0;
                    successValue.textContent = `${Math.round(rate)}%`;
                }
            }
        } else {
            console.error('获取搜索统计失败:', data.message);
        }
    } catch (error) {
        console.error('获取搜索统计请求失败:', error);
    }
}

// 获取平台标签
function getPlatformLabel(platform) {
    const platforms = {
        'kuaishou': '快手',
        'douyin': '抖音',
        'bilibili': '哔哩哔哩',
        'csdn': 'CSDN',
        'zhihu': '知乎',
        'weibo': '微博'
    };

    return platforms[platform] || platform;
}

// 获取内容类型标签
function getContentTypeLabel(contentType) {
    const contentTypes = {
        'video': '视频',
        'article': '文章',
        'resource': '资源',
        'image': '图片',
        'audio': '音频'
    };

    return contentTypes[contentType] || contentType;
}

// 获取内容类型图标
function getContentTypeIcon(contentType) {
    const contentTypeIcons = {
        'video': 'fas fa-video',
        'article': 'fas fa-file-alt',
        'resource': 'fas fa-file-archive',
        'image': 'fas fa-image',
        'audio': 'fas fa-music'
    };

    return contentTypeIcons[contentType] || 'fas fa-file';
}

// 获取状态标签
function getStatusLabel(status) {
    const statuses = {
        'success': '<span class="status-badge completed">成功</span>',
        'failed': '<span class="status-badge failed">失败</span>'
    };

    return statuses[status] || status;
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '-';

    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === undefined || bytes === null) return '-';

    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
}

// 格式化时长
function formatDuration(seconds) {
    if (seconds === undefined || seconds === null) return '-';

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    } else {
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
}

// 显示提示消息
function showToast(message, type = 'info') {
    // 检查是否已存在Toast容器
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container';
        document.body.appendChild(toastContainer);
    }

    // 创建Toast元素
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.innerHTML = `
        <div class="toast-icon">
            <i class="fas ${getToastIcon(type)}"></i>
        </div>
        <div class="toast-message">${message}</div>
    `;

    // 添加到容器
    toastContainer.appendChild(toast);

    // 显示Toast
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);

    // 自动关闭
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            toastContainer.removeChild(toast);
        }, 300);
    }, 3000);
}

// 获取Toast图标
function getToastIcon(type) {
    const icons = {
        'success': 'fa-check-circle',
        'error': 'fa-times-circle',
        'warning': 'fa-exclamation-triangle',
        'info': 'fa-info-circle'
    };

    return icons[type] || icons.info;
}

// 切换收藏状态
async function toggleFavorite(recordId) {
    try {
        const response = await fetch(`/api/search/${recordId}/favorite`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (data.success) {
            // 更新UI
            const favoriteBtn = document.querySelector(`.favorite-btn[data-id="${recordId}"]`);
            if (favoriteBtn) {
                if (data.data.is_favorite) {
                    favoriteBtn.classList.add('active');
                    favoriteBtn.title = '取消收藏';

                    // 添加收藏类名到行或卡片
                    const row = favoriteBtn.closest('tr');
                    const card = favoriteBtn.closest('.search-record-card');
                    if (row) row.classList.add('is-favorite');
                    if (card) card.classList.add('is-favorite');

                    showToast('已添加到收藏', 'success');
                } else {
                    favoriteBtn.classList.remove('active');
                    favoriteBtn.title = '收藏';

                    // 移除收藏类名
                    const row = favoriteBtn.closest('tr');
                    const card = favoriteBtn.closest('.search-record-card');
                    if (row) row.classList.remove('is-favorite');
                    if (card) card.classList.remove('is-favorite');

                    showToast('已取消收藏', 'info');
                }
            }

            // 如果当前是收藏视图，需要刷新列表
            if (currentFilters.is_favorite) {
                loadSearchHistoryDebounced();
            } else {
                // 只更新统计信息，不重新加载列表
                loadSearchStatsDebounced();
            }
        } else {
            showToast(`操作失败: ${data.message}`, 'error');
        }
    } catch (error) {
        console.error('切换收藏状态失败:', error);
        showToast('操作失败，请稍后再试', 'error');
    }
}

// 批量切换收藏状态
async function batchToggleFavorite(recordIds, isFavorite) {
    if (!recordIds.length) return;

    const promises = recordIds.map(id =>
        fetch(`/api/search/${id}/favorite`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        }).then(response => response.json())
    );

    try {
        const results = await Promise.all(promises);
        const successCount = results.filter(result => result.success).length;

        if (successCount > 0) {
            showToast(`成功${isFavorite ? '添加' : '取消'}${successCount}个收藏`, 'success');

            // 刷新列表
            loadSearchHistoryDebounced();
        } else {
            showToast('操作失败，请稍后再试', 'error');
        }
    } catch (error) {
        console.error('批量切换收藏状态失败:', error);
        showToast('操作失败，请稍后再试', 'error');
    }
}

// 删除搜索记录
async function deleteSearchRecord(recordId) {
    if (!confirm('确定要删除此搜索记录吗？')) return;

    try {
        const response = await fetch(`/api/search/${recordId}`, {
            method: 'DELETE'
        });

        const data = await response.json();

        if (data.success) {
            showToast('删除成功', 'success');

            // 刷新列表
            loadSearchHistoryDebounced();
        } else {
            showToast(`删除失败: ${data.message}`, 'error');
        }
    } catch (error) {
        console.error('删除搜索记录失败:', error);
        showToast('删除失败，请稍后再试', 'error');
    }
}

// 批量删除搜索记录
async function batchDeleteSearchRecords(recordIds) {
    if (!recordIds.length) return;

    try {
        const response = await fetch('/api/search/batch-delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ record_ids: recordIds })
        });

        const data = await response.json();

        if (data.success) {
            showToast(`成功删除${data.data.deleted_count}条记录`, 'success');

            // 刷新列表
            loadSearchHistoryDebounced();
        } else {
            showToast(`删除失败: ${data.message}`, 'error');
        }
    } catch (error) {
        console.error('批量删除搜索记录失败:', error);
        showToast('删除失败，请稍后再试', 'error');
    }
}

// 显示笔记模态框
function showNotesModal(recordId, title) {
    // 检查是否已存在笔记模态框
    let notesModal = document.getElementById('notes-modal');
    if (!notesModal) {
        // 创建模态框
        notesModal = document.createElement('div');
        notesModal.className = 'modal';
        notesModal.id = 'notes-modal';

        notesModal.innerHTML = `
            <div class="modal-overlay"></div>
            <div class="modal-container">
                <div class="modal-header">
                    <h2>笔记</h2>
                    <button class="close-modal"><i class="fas fa-times"></i></button>
                </div>
                <div class="modal-body">
                    <div class="notes-title"></div>
                    <form id="notes-form">
                        <input type="hidden" id="notes-record-id">
                        <div class="form-group">
                            <textarea id="notes-content" rows="6" placeholder="添加笔记..."></textarea>
                        </div>
                        <div class="status-container" id="notes-status" style="display: none;">
                            <p class="status-message"></p>
                        </div>
                        <button type="submit" class="submit-btn">保存</button>
                    </form>
                </div>
            </div>
        `;

        document.body.appendChild(notesModal);

        // 关闭按钮事件
        const closeBtn = notesModal.querySelector('.close-modal');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                notesModal.classList.remove('active');
            });
        }

        // 表单提交事件
        const notesForm = document.getElementById('notes-form');
        if (notesForm) {
            notesForm.addEventListener('submit', async (e) => {
                e.preventDefault();

                const recordId = document.getElementById('notes-record-id').value;
                const notes = document.getElementById('notes-content').value;

                await saveNotes(recordId, notes);
            });
        }
    }

    // 更新模态框内容
    const titleEl = notesModal.querySelector('.notes-title');
    if (titleEl) {
        titleEl.textContent = title;
    }

    // 设置记录ID
    const recordIdInput = document.getElementById('notes-record-id');
    if (recordIdInput) {
        recordIdInput.value = recordId;
    }

    // 获取并显示笔记内容
    loadNotes(recordId);

    // 显示模态框
    notesModal.classList.add('active');
}

// 加载笔记内容
async function loadNotes(recordId) {
    try {
        // 获取搜索记录详情
        const response = await fetch(`/api/search/history?record_id=${recordId}`);
        const data = await response.json();

        if (data.success && data.data.records && data.data.records.length > 0) {
            const record = data.data.records[0];

            // 更新笔记内容
            const notesContent = document.getElementById('notes-content');
            if (notesContent) {
                notesContent.value = record.notes || '';
            }
        } else {
            console.error('获取笔记内容失败:', data.message);
        }
    } catch (error) {
        console.error('加载笔记内容失败:', error);
    }
}

// 保存笔记
async function saveNotes(recordId, notes) {
    const statusContainer = document.getElementById('notes-status');

    try {
        const response = await fetch(`/api/search/${recordId}/notes`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ notes })
        });

        const data = await response.json();

        if (data.success) {
            // 显示成功消息
            if (statusContainer) {
                statusContainer.style.display = 'block';
                statusContainer.innerHTML = '<p class="status-message success">保存成功</p>';

                // 自动隐藏
                setTimeout(() => {
                    statusContainer.style.display = 'none';
                }, 2000);
            }

            // 更新UI
            const notesBtn = document.querySelector(`.notes-btn[data-id="${recordId}"]`);
            if (notesBtn) {
                if (notes) {
                    notesBtn.classList.add('has-notes');
                    notesBtn.title = '编辑笔记';
                } else {
                    notesBtn.classList.remove('has-notes');
                    notesBtn.title = '添加笔记';
                }
            }

            // 关闭模态框
            setTimeout(() => {
                const notesModal = document.getElementById('notes-modal');
                if (notesModal) {
                    notesModal.classList.remove('active');
                }

                // 不需要刷新整个列表，笔记更新只影响UI显示
                // loadDownloadHistory(); // 移除这个调用以避免重复请求
            }, 1500);
        } else {
            // 显示错误消息
            if (statusContainer) {
                statusContainer.style.display = 'block';
                statusContainer.innerHTML = `<p class="status-message error">保存失败: ${data.message}</p>`;
            }
        }
    } catch (error) {
        console.error('保存笔记失败:', error);

        // 显示错误消息
        if (statusContainer) {
            statusContainer.style.display = 'block';
            statusContainer.innerHTML = '<p class="status-message error">保存失败，请稍后再试</p>';
        }
    }
}

// 导出搜索记录
function exportSearchRecords() {
    // 获取当前筛选条件下的所有搜索记录
    fetch(`/api/search/history?per_page=1000&${new URLSearchParams(currentFilters).toString()}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data.records) {
                const records = data.data.records;

                // 转换为CSV格式
                const headers = ['ID', '标题', '平台', '类型', '作者', '状态', '时间', '搜索次数', '收藏'];
                const rows = records.map(record => [
                    record.id,
                    record.title || '未知标题',
                    getPlatformLabel(record.platform),
                    getContentTypeLabel(record.content_type),
                    record.author || '未知作者',
                    record.status,
                    formatDate(record.created_at),
                    record.search_count || 0,
                    record.is_favorite ? '是' : '否'
                ]);

                // 添加表头
                rows.unshift(headers);

                // 转换为CSV字符串
                const csv = rows.map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');

                // 创建Blob对象
                const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });

                // 创建下载链接
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = `搜索记录_${new Date().toISOString().slice(0, 10)}.csv`;
                link.style.display = 'none';

                // 触发下载
                document.body.appendChild(link);
                link.click();

                // 清理
                setTimeout(() => {
                    document.body.removeChild(link);
                    URL.revokeObjectURL(link.href);
                }, 100);

                showToast('导出成功', 'success');
            } else {
                showToast('导出失败，请稍后再试', 'error');
            }
        })
        .catch(error => {
            console.error('导出搜索记录失败:', error);
            showToast('导出失败，请稍后再试', 'error');
        });
}

// 导出函数 - 保持向后兼容
window.initSearchHistoryFeatures = initSearchHistoryFeatures;
window.showSearchHistoryModal = showSearchHistoryModal;
window.closeSearchHistoryModal = closeSearchHistoryModal;
window.loadSearchHistory = loadSearchHistory;
window.toggleFavorite = toggleFavorite;
window.deleteSearchRecord = deleteSearchRecord;
window.showNotesModal = showNotesModal;

// 向后兼容的别名
window.initDownloadFeatures = initSearchHistoryFeatures;
window.showDownloadsModal = showSearchHistoryModal;
