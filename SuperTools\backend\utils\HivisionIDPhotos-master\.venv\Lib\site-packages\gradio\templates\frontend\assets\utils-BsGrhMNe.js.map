{"version": 3, "file": "utils-BsGrhMNe.js", "sources": ["../../../../js/utils/src/utils.ts"], "sourcesContent": ["import type { ActionReturn } from \"svelte/action\";\nimport type { Client } from \"@gradio/client\";\nimport type { ComponentType, SvelteComponent } from \"svelte\";\n\nexport interface ValueData {\n\tvalue: any;\n\tis_value_data: boolean;\n}\n\nexport interface SelectData {\n\trow_value?: any[];\n\tcol_value?: any[];\n\tindex: number | [number, number];\n\tvalue: any;\n\tselected?: boolean;\n}\n\nexport interface LikeData {\n\tindex: number | [number, number];\n\tvalue: any;\n\tliked?: boolean | string;\n}\n\nexport interface KeyUpData {\n\tkey: string;\n\tinput_value: string;\n}\n\nexport interface ShareData {\n\tdescription: string;\n\ttitle?: string;\n}\n\nexport interface CopyData {\n\tvalue: string;\n}\n\nexport class ShareError extends Error {\n\tconstructor(message: string) {\n\t\tsuper(message);\n\t\tthis.name = \"ShareError\";\n\t}\n}\n\nexport async function uploadToHuggingFace(\n\tdata: string | { url?: string; path?: string },\n\ttype: \"base64\" | \"url\"\n): Promise<string> {\n\tif (window.__gradio_space__ == null) {\n\t\tthrow new ShareError(\"Must be on Spaces to share.\");\n\t}\n\tlet blob: Blob;\n\tlet contentType: string;\n\tlet filename: string;\n\tif (type === \"url\") {\n\t\tlet url: string;\n\n\t\tif (typeof data === \"object\" && data.url) {\n\t\t\turl = data.url;\n\t\t} else if (typeof data === \"string\") {\n\t\t\turl = data;\n\t\t} else {\n\t\t\tthrow new Error(\"Invalid data format for URL type\");\n\t\t}\n\n\t\tconst response = await fetch(url);\n\t\tblob = await response.blob();\n\t\tcontentType = response.headers.get(\"content-type\") || \"\";\n\t\tfilename = response.headers.get(\"content-disposition\") || \"\";\n\t} else {\n\t\tlet dataurl: string;\n\n\t\tif (typeof data === \"object\" && data.path) {\n\t\t\tdataurl = data.path;\n\t\t} else if (typeof data === \"string\") {\n\t\t\tdataurl = data;\n\t\t} else {\n\t\t\tthrow new Error(\"Invalid data format for base64 type\");\n\t\t}\n\n\t\tblob = dataURLtoBlob(dataurl);\n\t\tcontentType = dataurl.split(\";\")[0].split(\":\")[1];\n\t\tfilename = \"file.\" + contentType.split(\"/\")[1];\n\t}\n\n\tconst file = new File([blob], filename, { type: contentType });\n\n\t// Send file to endpoint\n\tconst uploadResponse = await fetch(\"https://huggingface.co/uploads\", {\n\t\tmethod: \"POST\",\n\t\tbody: file,\n\t\theaders: {\n\t\t\t\"Content-Type\": file.type,\n\t\t\t\"X-Requested-With\": \"XMLHttpRequest\"\n\t\t}\n\t});\n\n\t// Check status of response\n\tif (!uploadResponse.ok) {\n\t\tif (\n\t\t\tuploadResponse.headers.get(\"content-type\")?.includes(\"application/json\")\n\t\t) {\n\t\t\tconst error = await uploadResponse.json();\n\t\t\tthrow new ShareError(`Upload failed: ${error.error}`);\n\t\t}\n\t\tthrow new ShareError(`Upload failed.`);\n\t}\n\n\t// Return response if needed\n\tconst result = await uploadResponse.text();\n\treturn result;\n}\n\nfunction dataURLtoBlob(dataurl: string): Blob {\n\tvar arr = dataurl.split(\",\"),\n\t\tmime = (arr[0].match(/:(.*?);/) as RegExpMatchArray)[1],\n\t\tbstr = atob(arr[1]),\n\t\tn = bstr.length,\n\t\tu8arr = new Uint8Array(n);\n\twhile (n--) {\n\t\tu8arr[n] = bstr.charCodeAt(n);\n\t}\n\treturn new Blob([u8arr], { type: mime });\n}\n\nexport function copy(node: HTMLDivElement): ActionReturn {\n\tnode.addEventListener(\"click\", handle_copy);\n\n\tasync function handle_copy(event: MouseEvent): Promise<void> {\n\t\tconst path = event.composedPath() as HTMLButtonElement[];\n\n\t\tconst [copy_button] = path.filter(\n\t\t\t(e) => e?.tagName === \"BUTTON\" && e.classList.contains(\"copy_code_button\")\n\t\t);\n\n\t\tif (copy_button) {\n\t\t\tevent.stopImmediatePropagation();\n\n\t\t\tconst copy_text = copy_button.parentElement!.innerText.trim();\n\t\t\tconst copy_sucess_button = Array.from(\n\t\t\t\tcopy_button.children\n\t\t\t)[1] as HTMLDivElement;\n\n\t\t\tconst copied = await copy_to_clipboard(copy_text);\n\n\t\t\tif (copied) copy_feedback(copy_sucess_button);\n\n\t\t\tfunction copy_feedback(_copy_sucess_button: HTMLDivElement): void {\n\t\t\t\t_copy_sucess_button.style.opacity = \"1\";\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t_copy_sucess_button.style.opacity = \"0\";\n\t\t\t\t}, 2000);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn {\n\t\tdestroy(): void {\n\t\t\tnode.removeEventListener(\"click\", handle_copy);\n\t\t}\n\t};\n}\n\nasync function copy_to_clipboard(value: string): Promise<boolean> {\n\tlet copied = false;\n\tif (\"clipboard\" in navigator) {\n\t\tawait navigator.clipboard.writeText(value);\n\t\tcopied = true;\n\t} else {\n\t\tconst textArea = document.createElement(\"textarea\");\n\t\ttextArea.value = value;\n\n\t\ttextArea.style.position = \"absolute\";\n\t\ttextArea.style.left = \"-999999px\";\n\n\t\tdocument.body.prepend(textArea);\n\t\ttextArea.select();\n\n\t\ttry {\n\t\t\tdocument.execCommand(\"copy\");\n\t\t\tcopied = true;\n\t\t} catch (error) {\n\t\t\tconsole.error(error);\n\t\t\tcopied = false;\n\t\t} finally {\n\t\t\ttextArea.remove();\n\t\t}\n\t}\n\n\treturn copied;\n}\n\nexport const format_time = (seconds: number): string => {\n\tconst hours = Math.floor(seconds / 3600);\n\tconst minutes = Math.floor((seconds % 3600) / 60);\n\tconst seconds_remainder = Math.round(seconds) % 60;\n\tconst padded_minutes = `${minutes < 10 ? \"0\" : \"\"}${minutes}`;\n\tconst padded_seconds = `${\n\t\tseconds_remainder < 10 ? \"0\" : \"\"\n\t}${seconds_remainder}`;\n\n\tif (hours > 0) {\n\t\treturn `${hours}:${padded_minutes}:${padded_seconds}`;\n\t}\n\treturn `${minutes}:${padded_seconds}`;\n};\n\ninterface Args {\n\tapi_url: string;\n\tname: string;\n\tid?: string;\n\tvariant: \"component\" | \"example\" | \"base\";\n}\n\ntype component_loader = (args: Args) => {\n\tname: \"string\";\n\tcomponent: {\n\t\tdefault: ComponentType<SvelteComponent>;\n\t};\n};\n\nconst is_browser = typeof window !== \"undefined\";\n\nexport type I18nFormatter = any;\nexport class Gradio<T extends Record<string, any> = Record<string, any>> {\n\t#id: number;\n\ttheme: string;\n\tversion: string;\n\ti18n: I18nFormatter;\n\t#el: HTMLElement;\n\troot: string;\n\tautoscroll: boolean;\n\tmax_file_size: number | null;\n\tclient: Client;\n\t_load_component?: component_loader;\n\tload_component = _load_component.bind(this);\n\n\tconstructor(\n\t\tid: number,\n\t\tel: HTMLElement,\n\t\ttheme: string,\n\t\tversion: string,\n\t\troot: string,\n\t\tautoscroll: boolean,\n\t\tmax_file_size: number | null,\n\t\ti18n: I18nFormatter = (x: string): string => x,\n\t\tclient: Client,\n\t\tvirtual_component_loader?: component_loader\n\t) {\n\t\tthis.#id = id;\n\t\tthis.theme = theme;\n\t\tthis.version = version;\n\t\tthis.#el = el;\n\t\tthis.max_file_size = max_file_size;\n\n\t\tthis.i18n = i18n;\n\t\tthis.root = root;\n\t\tthis.autoscroll = autoscroll;\n\t\tthis.client = client;\n\n\t\tthis._load_component = virtual_component_loader;\n\t}\n\n\tdispatch<E extends keyof T>(event_name: E, data?: T[E]): void {\n\t\tif (!is_browser || !this.#el) return;\n\t\tconst e = new CustomEvent(\"gradio\", {\n\t\t\tbubbles: true,\n\t\t\tdetail: { data, id: this.#id, event: event_name }\n\t\t});\n\t\tthis.#el.dispatchEvent(e);\n\t}\n}\n\nfunction _load_component(\n\tthis: Gradio,\n\tname: string,\n\tvariant: \"component\" | \"example\" | \"base\" = \"component\"\n): ReturnType<component_loader> {\n\treturn this._load_component!({\n\t\tname,\n\t\tapi_url: this.client.config?.root!,\n\t\tvariant\n\t});\n}\n\nexport const css_units = (dimension_value: string | number): string => {\n\treturn typeof dimension_value === \"number\"\n\t\t? dimension_value + \"px\"\n\t\t: dimension_value;\n};\n"], "names": ["ShareError", "message", "uploadToHuggingFace", "data", "type", "blob", "contentType", "filename", "url", "response", "file", "uploadResponse", "error", "copy", "node", "handle_copy", "event", "path", "copy_button", "e", "copy_feedback", "_copy_sucess_button", "copy_text", "copy_sucess_button", "copy_to_clipboard", "value", "copied", "textArea", "format_time", "seconds", "hours", "minutes", "seconds_remainder", "padded_minutes", "padded_seconds", "is_browser", "Gradio", "#id", "#el", "_load_component", "id", "el", "theme", "version", "root", "autoscroll", "max_file_size", "i18n", "x", "client", "virtual_component_loader", "event_name", "name", "variant", "css_units", "dimension_value"], "mappings": "AAqCO,MAAMA,UAAmB,KAAM,CACrC,YAAYC,EAAiB,CAC5B,MAAMA,CAAO,EACb,KAAK,KAAO,YACb,CACD,CAEsB,eAAAC,EACrBC,EACAC,EACkB,CACd,GAAA,OAAO,kBAAoB,KACxB,MAAA,IAAIJ,EAAW,6BAA6B,EAE/C,IAAAK,EACAC,EACAC,EACgB,CACf,IAAAC,EAEJ,GAAI,OAAOL,GAAS,UAAYA,EAAK,IACpCK,EAAML,EAAK,YACD,OAAOA,GAAS,SACpBK,EAAAL,MAEA,OAAA,IAAI,MAAM,kCAAkC,EAG7C,MAAAM,EAAW,MAAM,MAAMD,CAAG,EACzBH,EAAA,MAAMI,EAAS,OACtBH,EAAcG,EAAS,QAAQ,IAAI,cAAc,GAAK,GACtDF,EAAWE,EAAS,QAAQ,IAAI,qBAAqB,GAAK,EAe3D,CAEM,MAAAC,EAAO,IAAI,KAAK,CAACL,CAAI,EAAGE,EAAU,CAAE,KAAMD,CAAA,CAAa,EAGvDK,EAAiB,MAAM,MAAM,iCAAkC,CACpE,OAAQ,OACR,KAAMD,EACN,QAAS,CACR,eAAgBA,EAAK,KACrB,mBAAoB,gBACrB,CAAA,CACA,EAGG,GAAA,CAACC,EAAe,GAAI,CACvB,GACCA,EAAe,QAAQ,IAAI,cAAc,GAAG,SAAS,kBAAkB,EACtE,CACK,MAAAC,EAAQ,MAAMD,EAAe,OACnC,MAAM,IAAIX,EAAW,kBAAkBY,EAAM,KAAK,EAAE,CACrD,CACM,MAAA,IAAIZ,EAAW,gBAAgB,CACtC,CAIO,OADQ,MAAMW,EAAe,MAErC,CAcO,SAASE,EAAKC,EAAoC,CACnDA,EAAA,iBAAiB,QAASC,CAAW,EAE1C,eAAeA,EAAYC,EAAkC,CACtD,MAAAC,EAAOD,EAAM,eAEb,CAACE,CAAW,EAAID,EAAK,OACzBE,GAAMA,GAAG,UAAY,UAAYA,EAAE,UAAU,SAAS,kBAAkB,CAAA,EAG1E,GAAID,EAAa,CAYP,IAAAE,EAAT,SAAuBC,EAA2C,CACjEA,EAAoB,MAAM,QAAU,IACpC,WAAW,IAAM,CAChBA,EAAoB,MAAM,QAAU,KAClC,GAAI,CAAA,EAfRL,EAAM,yBAAyB,EAE/B,MAAMM,EAAYJ,EAAY,cAAe,UAAU,KAAK,EACtDK,EAAqB,MAAM,KAChCL,EAAY,UACX,CAAC,EAEY,MAAMM,EAAkBF,CAAS,GAEpCF,EAAcG,CAAkB,CAQ7C,CACD,CAEO,MAAA,CACN,SAAgB,CACVT,EAAA,oBAAoB,QAASC,CAAW,CAC9C,CAAA,CAEF,CAEA,eAAeS,EAAkBC,EAAiC,CACjE,IAAIC,EAAS,GACb,GAAI,cAAe,UACZ,MAAA,UAAU,UAAU,UAAUD,CAAK,EAChCC,EAAA,OACH,CACA,MAAAC,EAAW,SAAS,cAAc,UAAU,EAClDA,EAAS,MAAQF,EAEjBE,EAAS,MAAM,SAAW,WAC1BA,EAAS,MAAM,KAAO,YAEb,SAAA,KAAK,QAAQA,CAAQ,EAC9BA,EAAS,OAAO,EAEZ,GAAA,CACH,SAAS,YAAY,MAAM,EAClBD,EAAA,SACDd,EAAO,CACf,QAAQ,MAAMA,CAAK,EACVc,EAAA,EAAA,QACR,CACDC,EAAS,OAAO,CACjB,CACD,CAEO,OAAAD,CACR,CAEa,MAAAE,EAAeC,GAA4B,CACvD,MAAMC,EAAQ,KAAK,MAAMD,EAAU,IAAI,EACjCE,EAAU,KAAK,MAAOF,EAAU,KAAQ,EAAE,EAC1CG,EAAoB,KAAK,MAAMH,CAAO,EAAI,GAC1CI,EAAiB,GAAGF,EAAU,GAAK,IAAM,EAAE,GAAGA,CAAO,GACrDG,EAAiB,GACtBF,EAAoB,GAAK,IAAM,EAChC,GAAGA,CAAiB,GAEpB,OAAIF,EAAQ,EACJ,GAAGA,CAAK,IAAIG,CAAc,IAAIC,CAAc,GAE7C,GAAGH,CAAO,IAAIG,CAAc,EACpC,EAgBMC,EAAa,OAAO,OAAW,IAG9B,MAAMC,CAA4D,CACxEC,GACA,MACA,QACA,KACAC,GACA,KACA,WACA,cACA,OACA,gBACA,eAAiBC,EAAgB,KAAK,IAAI,EAE1C,YACCC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAAuBC,GAAsBA,EAC7CC,EACAC,EACC,CACD,KAAKb,GAAMG,EACX,KAAK,MAAQE,EACb,KAAK,QAAUC,EACf,KAAKL,GAAMG,EACX,KAAK,cAAgBK,EAErB,KAAK,KAAOC,EACZ,KAAK,KAAOH,EACZ,KAAK,WAAaC,EAClB,KAAK,OAASI,EAEd,KAAK,gBAAkBC,CACxB,CAEA,SAA4BC,EAAehD,EAAmB,CACzD,GAAA,CAACgC,GAAc,CAAC,KAAKG,GAAK,OACxB,MAAAnB,EAAI,IAAI,YAAY,SAAU,CACnC,QAAS,GACT,OAAQ,CAAE,KAAAhB,EAAM,GAAI,KAAKkC,GAAK,MAAOc,CAAW,CAAA,CAChD,EACI,KAAAb,GAAI,cAAcnB,CAAC,CACzB,CACD,CAEA,SAASoB,EAERa,EACAC,EAA4C,YACb,CAC/B,OAAO,KAAK,gBAAiB,CAC5B,KAAAD,EACA,QAAS,KAAK,OAAO,QAAQ,KAC7B,QAAAC,CAAA,CACA,CACF,CAEa,MAAAC,EAAaC,GAClB,OAAOA,GAAoB,SAC/BA,EAAkB,KAClBA"}